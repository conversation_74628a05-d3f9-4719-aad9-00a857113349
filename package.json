{"name": "calc9", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev --turbopack", "build": "node scripts/generate-version.js && next build", "build:static": "node scripts/generate-version.js && next build", "build:production": "node scripts/generate-version.js && next build && node scripts/submit-indexnow.js", "build:version": "node scripts/generate-version.js", "indexnow": "node scripts/submit-indexnow.js", "indexnow:test": "node scripts/test-indexnow.js", "start": "next start", "preview": "serve out -l 3000", "preview:open": "serve out -l 3000 -o", "lint": "next lint"}, "dependencies": {"next": "15.3.4", "react": "^19.0.0", "react-dom": "^19.0.0"}, "devDependencies": {"@eslint/eslintrc": "^3", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "autoprefixer": "^10.4.20", "eslint": "^9", "eslint-config-next": "15.3.4", "postcss": "^8.4.47", "serve": "^14.2.4", "tailwindcss": "^3.4.14", "typescript": "^5"}}