'use client';

import { useState, useRef, useEffect } from 'react';
import { useTheme } from '@/contexts/ThemeContext';
import { cn } from '@/components/ui/styles';

export default function ThemeToggle() {
  const { theme, setTheme } = useTheme();
  const [isOpen, setIsOpen] = useState(false);
  const dropdownRef = useRef<HTMLDivElement>(null);

  const themeOptions = [
    {
      value: 'light',
      label: 'Light',
      icon: (
        <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 3v1m0 16v1m9-9h-1M4 12H3m15.364 6.364l-.707-.707M6.343 6.343l-.707-.707m12.728 0l-.707.707M6.343 17.657l-.707.707M16 12a4 4 0 11-8 0 4 4 0 018 0z" />
        </svg>
      )
    },
    {
      value: 'dark',
      label: 'Dark',
      icon: (
        <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M20.354 15.354A9 9 0 018.646 3.646 9.003 9.003 0 0012 21a9.003 9.003 0 008.354-5.646z" />
        </svg>
      )
    },
    {
      value: 'system',
      label: 'System',
      icon: (
        <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9.75 17L9 20l-1 1h8l-1-1-.75-3M3 13h18M5 17h14a2 2 0 002-2V5a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z" />
        </svg>
      )
    }
  ];

  const currentTheme = themeOptions.find(option => option.value === theme);

  // Handle click outside to close dropdown
  useEffect(() => {
    function handleClickOutside(event: MouseEvent) {
      if (dropdownRef.current && !dropdownRef.current.contains(event.target as Node)) {
        setIsOpen(false);
      }
    }

    document.addEventListener('mousedown', handleClickOutside);
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, []);

  const handleThemeChange = (newTheme: 'light' | 'dark' | 'system') => {
    setTheme(newTheme);
    setIsOpen(false);
  };

  return (
    <div className="relative" ref={dropdownRef}>
      <button
        onClick={() => setIsOpen(!isOpen)}
        className={cn(
          "flex items-center justify-center space-x-2 px-4 py-2.5 rounded-lg transition-colors",
          "bg-purple-50 dark:bg-purple-950/80 border border-purple-200 dark:border-purple-800/60",
          "text-purple-700 dark:text-purple-200 font-medium",
          "hover:bg-purple-100 dark:hover:bg-purple-900/80",
          "focus:outline-none focus:ring-2 focus:ring-purple-500 focus:ring-offset-2 dark:focus:ring-offset-gray-800",
          "w-full sm:w-auto min-w-[140px] h-10 backdrop-blur-sm"
        )}
        title="Change theme"
      >
        {currentTheme?.icon}
        <span className="font-medium">{currentTheme?.label}</span>
        <svg
          className={cn(
            "w-4 h-4 transition-transform duration-200",
            isOpen ? 'rotate-180' : ''
          )}
          fill="none"
          stroke="currentColor"
          viewBox="0 0 24 24"
        >
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
        </svg>
      </button>

      {isOpen && (
        <div className={cn(
          "absolute top-full left-1/2 transform -translate-x-1/2 mt-2 w-40",
          "bg-purple-50/95 dark:bg-purple-950/95",
          "border border-purple-200 dark:border-purple-800/60",
          "rounded-lg shadow-lg z-50 backdrop-blur-sm"
        )}>
          {themeOptions.filter(option => option.value !== theme).map((option) => (
            <button
              key={option.value}
              onClick={() => handleThemeChange(option.value as 'light' | 'dark' | 'system')}
              className={cn(
                "w-full flex items-center space-x-2 px-4 py-2 text-left",
                "text-purple-700 dark:text-purple-200",
                "hover:bg-purple-100 dark:hover:bg-purple-900/60",
                "first:rounded-t-lg last:rounded-b-lg",
                "transition-colors focus:outline-none focus:ring-2 focus:ring-purple-500 focus:ring-inset"
              )}
            >
              {option.icon}
              <span className="font-medium">{option.label}</span>
            </button>
          ))}
        </div>
      )}
    </div>
  );
}