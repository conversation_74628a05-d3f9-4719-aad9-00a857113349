import Link from 'next/link';

export default function ConcreteBlogContent() {
  return (
    <div className="prose prose-lg max-w-none">
      <section className="mb-8">
        <h2 className="text-2xl font-bold text-gray-800 dark:text-white mb-4">Why Accurate Concrete Calculations Matter</h2>
        <p className="text-gray-700 dark:text-gray-300 mb-4">
          Whether you&apos;re pouring a new patio, creating a foundation, or building a driveway, calculating the right amount of concrete is crucial for project success. Order too little, and you&apos;ll face delays and potentially weak joints. Order too much, and you&apos;ll waste money and have disposal challenges.
        </p>
        <div className="bg-yellow-50 dark:bg-yellow-900/20 border border-yellow-200 dark:border-yellow-700 rounded-lg p-4 mb-4">
          <h3 className="text-lg font-semibold text-yellow-800 dark:text-yellow-200 mb-2">Cost Impact</h3>
          <p className="text-yellow-700 dark:text-yellow-300">
            Concrete typically costs $100-150 per cubic yard delivered. A 10% miscalculation on a medium project could mean $200-300 in wasted material or emergency rush orders.
          </p>
        </div>
      </section>

      <section className="mb-8">
        <h2 className="text-2xl font-bold text-gray-800 dark:text-white mb-4">Basic Concrete Volume Calculations</h2>
        <p className="text-gray-700 dark:text-gray-300 mb-4">
          All concrete calculations start with volume - length × width × height. However, the shape of your project determines the specific formula:
        </p>
        
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-6">
          <div className="bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-700 rounded-lg p-4">
            <h3 className="font-semibold text-blue-800 dark:text-blue-200 mb-2">Rectangular Slabs</h3>
            <div className="bg-white dark:bg-gray-800 p-3 rounded font-mono text-sm text-gray-700 dark:text-gray-300 mb-2 border border-gray-200 dark:border-gray-600">
              Volume = Length × Width × Thickness
            </div>
            <p className="text-sm text-gray-700 dark:text-gray-300">Most common for patios, driveways, and walkways.</p>
          </div>
          
          <div className="bg-green-50 dark:bg-green-900/20 border border-green-200 dark:border-green-700 rounded-lg p-4">
            <h3 className="font-semibold text-green-800 dark:text-green-200 mb-2">Circular Areas</h3>
            <div className="bg-white dark:bg-gray-800 p-3 rounded font-mono text-sm text-gray-700 dark:text-gray-300 mb-2 border border-gray-200 dark:border-gray-600">
              Volume = π × Radius² × Thickness
            </div>
            <p className="text-sm text-gray-700 dark:text-gray-300">For round patios or decorative elements.</p>
          </div>
          
          <div className="bg-purple-50 dark:bg-purple-900/20 border border-purple-200 dark:border-purple-700 rounded-lg p-4">
            <h3 className="font-semibold text-purple-800 dark:text-purple-200 mb-2">Footings</h3>
            <div className="bg-white dark:bg-gray-800 p-3 rounded font-mono text-sm text-gray-700 dark:text-gray-300 mb-2 border border-gray-200 dark:border-gray-600">
              Volume = Length × Width × Depth
            </div>
            <p className="text-sm text-gray-700 dark:text-gray-300">For foundation footings and fence posts.</p>
          </div>
          
          <div className="bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-700 rounded-lg p-4">
            <h3 className="font-semibold text-red-800 dark:text-red-200 mb-2">Steps</h3>
            <div className="bg-white dark:bg-gray-800 p-3 rounded font-mono text-sm text-gray-700 dark:text-gray-300 mb-2 border border-gray-200 dark:border-gray-600">
              Volume = (W × L × H) per step
            </div>
            <p className="text-sm text-gray-700 dark:text-gray-300">Calculate each step individually and sum.</p>
          </div>
        </div>
      </section>

      <section className="mb-8">
        <h2 className="text-2xl font-bold text-gray-800 dark:text-white mb-4">Unit Conversions and Measurements</h2>
        <p className="text-gray-700 dark:text-gray-300 mb-4">
          Concrete is sold by the cubic yard, but you might measure in feet or inches. Here are the key conversions:
        </p>
        
        <div className="bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-lg p-6 mb-4">
          <h3 className="text-lg font-semibold text-gray-800 dark:text-white mb-3">Volume Conversions</h3>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <h4 className="font-medium text-gray-700 dark:text-gray-300 mb-2">Cubic Feet to Cubic Yards</h4>
              <div className="bg-gray-50 dark:bg-gray-700 p-3 rounded font-mono text-sm text-gray-700 dark:text-gray-300 border border-gray-200 dark:border-gray-600">
                Cubic Yards = Cubic Feet ÷ 27
              </div>
            </div>
            <div>
              <h4 className="font-medium text-gray-700 dark:text-gray-300 mb-2">Common Thickness Conversions</h4>
              <ul className="text-sm text-gray-700 dark:text-gray-300 space-y-1">
                <li>• 4 inches = 0.33 feet</li>
                <li>• 6 inches = 0.5 feet</li>
                <li>• 8 inches = 0.67 feet</li>
                <li>• 12 inches = 1 foot</li>
              </ul>
            </div>
          </div>
        </div>
        
        <div className="bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-700 rounded-lg p-4 mb-4">
          <h3 className="text-lg font-semibold text-blue-800 dark:text-blue-200 mb-2">Quick Reference</h3>
          <p className="text-gray-700 dark:text-gray-300 text-sm">
            One cubic yard of concrete covers 81 square feet at 4 inches thick, or 54 square feet at 6 inches thick.
          </p>
        </div>
      </section>

      <section className="mb-8">
        <h2 className="text-2xl font-bold text-gray-800 dark:text-white mb-4">Common DIY Project Calculations</h2>
        
        <div className="space-y-6">
          <div className="bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-lg p-6">
            <h3 className="text-xl font-semibold text-gray-800 dark:text-white mb-4">Patio Slab</h3>
            <div className="bg-gray-50 dark:bg-gray-700 p-4 rounded mb-3 border border-gray-200 dark:border-gray-600">
              <p className="text-gray-700 dark:text-gray-300 mb-2"><strong>Example:</strong> 12 ft × 16 ft patio, 4 inches thick</p>
              <div className="font-mono text-sm text-gray-700 dark:text-gray-300 space-y-1">
                <div>1. Convert thickness: 4 inches = 4/12 = 0.33 feet</div>
                <div>2. Calculate volume: 12 × 16 × 0.33 = 63.36 cubic feet</div>
                <div>3. Convert to yards: 63.36 ÷ 27 = 2.35 cubic yards</div>
                <div>4. Add 10% waste: 2.35 × 1.1 = 2.6 cubic yards</div>
              </div>
            </div>
            <p className="text-sm text-gray-600 dark:text-gray-400">
              <strong>Recommended thickness:</strong> 4 inches for pedestrian areas, 6 inches for vehicles
            </p>
          </div>
          
          <div className="bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-lg p-6">
            <h3 className="text-xl font-semibold text-gray-800 dark:text-white mb-4">Driveway</h3>
            <div className="bg-gray-50 dark:bg-gray-700 p-4 rounded mb-3 border border-gray-200 dark:border-gray-600">
              <p className="text-gray-700 dark:text-gray-300 mb-2"><strong>Example:</strong> 10 ft × 30 ft driveway, 6 inches thick</p>
              <div className="font-mono text-sm text-gray-700 dark:text-gray-300 space-y-1">
                <div>1. Convert thickness: 6 inches = 6/12 = 0.5 feet</div>
                <div>2. Calculate volume: 10 × 30 × 0.5 = 150 cubic feet</div>
                <div>3. Convert to yards: 150 ÷ 27 = 5.56 cubic yards</div>
                <div>4. Add 10% waste: 5.56 × 1.1 = 6.1 cubic yards</div>
              </div>
            </div>
            <p className="text-sm text-gray-600 dark:text-gray-400">
              <strong>Note:</strong> Consider thicker edges (8 inches) for heavy vehicle loads
            </p>
          </div>
          
          <div className="bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-lg p-6">
            <h3 className="text-xl font-semibold text-gray-800 dark:text-white mb-4">Foundation Footing</h3>
            <div className="bg-gray-50 dark:bg-gray-700 p-4 rounded mb-3 border border-gray-200 dark:border-gray-600">
              <p className="text-gray-700 dark:text-gray-300 mb-2"><strong>Example:</strong> 20 ft long × 2 ft wide × 1 ft deep footing</p>
              <div className="font-mono text-sm text-gray-700 dark:text-gray-300 space-y-1">
                <div>1. Calculate volume: 20 × 2 × 1 = 40 cubic feet</div>
                <div>2. Convert to yards: 40 ÷ 27 = 1.48 cubic yards</div>
                <div>3. Add 15% waste: 1.48 × 1.15 = 1.7 cubic yards</div>
              </div>
            </div>
            <p className="text-sm text-gray-600 dark:text-gray-400">
              <strong>Important:</strong> Footing dimensions must meet local building codes
            </p>
          </div>
        </div>
      </section>

      <section className="mb-8">
        <h2 className="text-2xl font-bold text-gray-800 dark:text-white mb-4">Waste Factors and Safety Margins</h2>
        <p className="text-gray-700 dark:text-gray-300 mb-4">
          Always order more concrete than your exact calculation. Here&apos;s why and how much:
        </p>
        
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
          <div className="bg-yellow-50 dark:bg-yellow-900/20 border border-yellow-200 dark:border-yellow-700 rounded-lg p-4">
            <h3 className="font-semibold text-yellow-800 dark:text-yellow-200 mb-2">Reasons for Waste</h3>
            <ul className="text-sm text-gray-700 dark:text-gray-300 space-y-1">
              <li>• Measurement inaccuracies</li>
              <li>• Uneven subgrade</li>
              <li>• Form deflection</li>
              <li>• Spillage during pour</li>
              <li>• Concrete left in mixer/pump</li>
            </ul>
          </div>
          
          <div className="bg-green-50 dark:bg-green-900/20 border border-green-200 dark:border-green-700 rounded-lg p-4">
            <h3 className="font-semibold text-green-800 dark:text-green-200 mb-2">Recommended Waste Factors</h3>
            <ul className="text-sm text-gray-700 dark:text-gray-300 space-y-1">
              <li>• Simple slabs: 5-10%</li>
              <li>• Complex shapes: 10-15%</li>
              <li>• Footings/foundations: 10-15%</li>
              <li>• Steps/stairs: 15-20%</li>
              <li>• First-time DIY: 15-20%</li>
            </ul>
          </div>
        </div>
      </section>

      <section className="mb-8">
        <h2 className="text-2xl font-bold text-gray-800 dark:text-white mb-4">Concrete Mix Types and Strength</h2>
        <p className="text-gray-700 dark:text-gray-300 mb-4">
          Different projects require different concrete strengths, measured in PSI (pounds per square inch):
        </p>
        
        <div className="overflow-x-auto mb-4">
          <table className="w-full border-collapse border border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-800 rounded-lg overflow-hidden">
            <thead>
              <tr className="bg-gray-100 dark:bg-gray-700">
                <th className="border border-gray-300 dark:border-gray-600 px-4 py-2 text-left text-gray-800 dark:text-white">Application</th>
                <th className="border border-gray-300 dark:border-gray-600 px-4 py-2 text-left text-gray-800 dark:text-white">Recommended PSI</th>
                <th className="border border-gray-300 dark:border-gray-600 px-4 py-2 text-left text-gray-800 dark:text-white">Notes</th>
              </tr>
            </thead>
            <tbody className="text-gray-700 dark:text-gray-300">
              <tr className="hover:bg-gray-50 dark:hover:bg-gray-700">
                <td className="border border-gray-300 dark:border-gray-600 px-4 py-2">Sidewalks, patios</td>
                <td className="border border-gray-300 dark:border-gray-600 px-4 py-2">2,500-3,000 PSI</td>
                <td className="border border-gray-300 dark:border-gray-600 px-4 py-2">Standard residential use</td>
              </tr>
              <tr className="hover:bg-gray-50 dark:hover:bg-gray-700">
                <td className="border border-gray-300 dark:border-gray-600 px-4 py-2">Driveways</td>
                <td className="border border-gray-300 dark:border-gray-600 px-4 py-2">3,000-4,000 PSI</td>
                <td className="border border-gray-300 dark:border-gray-600 px-4 py-2">Vehicle loads</td>
              </tr>
              <tr className="hover:bg-gray-50 dark:hover:bg-gray-700">
                <td className="border border-gray-300 dark:border-gray-600 px-4 py-2">Foundation footings</td>
                <td className="border border-gray-300 dark:border-gray-600 px-4 py-2">3,000-4,000 PSI</td>
                <td className="border border-gray-300 dark:border-gray-600 px-4 py-2">Structural requirements</td>
              </tr>
              <tr className="hover:bg-gray-50 dark:hover:bg-gray-700">
                <td className="border border-gray-300 dark:border-gray-600 px-4 py-2">Basement walls</td>
                <td className="border border-gray-300 dark:border-gray-600 px-4 py-2">4,000+ PSI</td>
                <td className="border border-gray-300 dark:border-gray-600 px-4 py-2">Moisture resistance</td>
              </tr>
            </tbody>
          </table>
        </div>
      </section>

      <section className="mb-8">
        <h2 className="text-2xl font-bold text-gray-800 dark:text-white mb-4">Ordering and Delivery Considerations</h2>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          <div>
            <h3 className="text-lg font-semibold text-gray-800 dark:text-white mb-3">Before Ordering</h3>
            <ul className="list-disc pl-6 text-gray-700 dark:text-gray-300 space-y-2">
              <li>Have your calculations double-checked</li>
              <li>Verify truck access to your site</li>
              <li>Confirm forms are properly set</li>
              <li>Have finishing tools ready</li>
              <li>Check weather forecast (avoid rain/extreme heat)</li>
              <li>Arrange adequate help for the pour</li>
            </ul>
          </div>
          
          <div>
            <h3 className="text-lg font-semibold text-gray-800 dark:text-white mb-3">Delivery Tips</h3>
            <ul className="list-disc pl-6 text-gray-700 dark:text-gray-300 space-y-2">
              <li>Schedule early morning delivery in hot weather</li>
              <li>Have a backup plan for excess concrete</li>
              <li>Understand delivery truck limitations</li>
              <li>Be ready to begin work immediately</li>
              <li>Consider pump truck for difficult access</li>
              <li>Have wheelbarrows ready if needed</li>
            </ul>
          </div>
        </div>
      </section>

      <section className="mb-8">
        <h2 className="text-2xl font-bold text-gray-800 dark:text-white mb-4">Cost Estimation</h2>
        <div className="bg-green-50 dark:bg-green-900/20 border border-green-200 dark:border-green-700 rounded-lg p-6 mb-4">
          <h3 className="text-lg font-semibold text-green-800 dark:text-green-200 mb-3">Typical Costs (2024)</h3>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <h4 className="font-medium text-gray-700 dark:text-gray-300 mb-2">Ready-Mix Concrete</h4>
              <ul className="text-sm text-gray-700 dark:text-gray-300 space-y-1">
                <li>• $100-150 per cubic yard delivered</li>
                <li>• $50-75 additional for pump truck</li>
                <li>• $25-50 for small load surcharge (&lt;4 yards)</li>
              </ul>
            </div>
            <div>
              <h4 className="font-medium text-gray-700 dark:text-gray-300 mb-2">Bagged Concrete</h4>
              <ul className="text-sm text-gray-700 dark:text-gray-300 space-y-1">
                <li>• $4-6 per 80lb bag</li>
                <li>• ~45 bags per cubic yard</li>
                <li>• Good for small projects (&lt;1 yard)</li>
              </ul>
            </div>
          </div>
        </div>
        <p className="text-gray-700 dark:text-gray-300 text-sm">
          <strong>Note:</strong> Prices vary by location and can fluctuate with fuel costs and demand.
        </p>
      </section>

      <section className="mb-8">
        <h2 className="text-2xl font-bold text-gray-800 dark:text-white mb-4">Use Our Concrete Calculator</h2>
        <p className="text-gray-700 dark:text-gray-300 mb-4">
          Ready to calculate concrete for your project? Our comprehensive concrete calculator handles all the common shapes and automatically includes waste factors.
        </p>
        <div className="text-center">
          <Link
            href="/concrete-calculator"
            className="inline-flex items-center px-6 py-3 bg-orange-500 hover:bg-orange-600 dark:bg-orange-600 dark:hover:bg-orange-700 text-white font-medium rounded-lg transition-colors"
          >
            Calculate Concrete Needs
            <svg className="w-4 h-4 ml-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
            </svg>
          </Link>
        </div>
      </section>

      <section className="mb-8">
        <h2 className="text-2xl font-bold text-gray-800 dark:text-white mb-4">Safety Reminders</h2>
        <div className="bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-700 rounded-lg p-4">
          <h3 className="text-lg font-semibold text-red-800 dark:text-red-200 mb-2">Important Safety Notes</h3>
          <ul className="list-disc pl-6 text-red-700 dark:text-red-300 space-y-1 text-sm">
            <li>Always check local building codes and permits</li>
            <li>Call 811 to mark underground utilities before digging</li>
            <li>Wear proper protective equipment (gloves, eye protection)</li>
            <li>Fresh concrete is caustic - avoid skin contact</li>
            <li>Have a plan for disposing of excess concrete</li>
            <li>Consider hiring professionals for structural elements</li>
          </ul>
        </div>
      </section>

      <section className="mb-8">
        <h2 className="text-2xl font-bold text-gray-800 dark:text-white mb-4">Final Tips for Success</h2>
        <p className="text-gray-700 dark:text-gray-300">
          Accurate concrete calculations are just the beginning of a successful project. Proper preparation, timing, and execution are equally important. Take time to plan thoroughly, have all materials and tools ready, and don&apos;t hesitate to consult with professionals for complex projects. Remember, concrete work is unforgiving - it&apos;s better to over-prepare than to discover problems mid-pour.
        </p>
      </section>
    </div>
  );
}