'use client';

import { useState } from 'react';
import Link from 'next/link';
import { formatVersionInfo, getCopyrightYears } from '../../lib/version';
import FeedbackModal from './FeedbackModal';
import ThemeToggle from './ThemeToggle';
import { cn } from '@/components/ui/styles';

export default function Footer() {
  const [isFeedbackModalOpen, setIsFeedbackModalOpen] = useState(false);
  const [showShareMessage, setShowShareMessage] = useState(false);

  const handleFeedback = () => {
    setIsFeedbackModalOpen(true);
  };

  const handleShare = async () => {
    const url = window.location.href;
    try {
      await navigator.clipboard.writeText(url);
      setShowShareMessage(true);
      setTimeout(() => setShowShareMessage(false), 3000);
    } catch {
      // Fallback for older browsers
      const textArea = document.createElement('textarea');
      textArea.value = url;
      document.body.appendChild(textArea);
      textArea.select();
      document.execCommand('copy');
      document.body.removeChild(textArea);
      setShowShareMessage(true);
      setTimeout(() => setShowShareMessage(false), 3000);
    }
  };

  const version = formatVersionInfo();
  const copyrightYears = getCopyrightYears();

  return (
    <>
      <footer className="py-8 bg-transparent transition-colors duration-300">
        {/* Elegant divider line with spacing */}
        <div className="container mx-auto px-8 mb-8">
          <div className="h-px bg-gradient-to-r from-transparent via-gray-400/60 dark:via-gray-600/30 to-transparent"></div>
        </div>

        <div className="container mx-auto px-4 text-center">
          <div className="flex flex-col items-center space-y-4">
            {/* Action Buttons */}
            <div className="flex flex-col sm:flex-row items-center justify-center gap-3 sm:gap-4 w-full sm:w-auto">
              <button
                onClick={handleFeedback}
                className={cn(
                  "flex items-center justify-center space-x-2 px-4 py-2.5 rounded-lg transition-colors",
                  "bg-blue-50 dark:bg-blue-950/80 border border-blue-200 dark:border-blue-800/60",
                  "text-blue-700 dark:text-blue-200 font-medium",
                  "hover:bg-blue-100 dark:hover:bg-blue-900/80",
                  "focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 dark:focus:ring-offset-gray-800",
                  "w-full sm:w-auto min-w-[140px] h-10 backdrop-blur-sm"
                )}
                title="Send feedback"
              >
                <svg className="w-4 h-4 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-3.582 8-8 8a8.959 8.959 0 01-4.906-1.488L3 21l2.488-5.094A8.959 8.959 0 013 12c0-4.418 3.582-8 8-8s8 3.582 8 8z" />
                </svg>
                <span className="hidden sm:inline font-medium">Feedback & Suggestions</span>
                <span className="sm:hidden font-medium">Feedback</span>
              </button>

              {/* Share Button */}
              <div className="relative w-full sm:w-auto">
                <button
                  onClick={handleShare}
                  className={cn(
                    "flex items-center justify-center space-x-2 px-4 py-2.5 rounded-lg transition-colors",
                    "bg-green-50 dark:bg-green-950/80 border border-green-200 dark:border-green-800/60",
                    "text-green-700 dark:text-green-200 font-medium",
                    "hover:bg-green-100 dark:hover:bg-green-900/80",
                    "focus:outline-none focus:ring-2 focus:ring-green-500 focus:ring-offset-2 dark:focus:ring-offset-gray-800",
                    "w-full sm:w-auto min-w-[140px] h-10 backdrop-blur-sm"
                  )}
                  title="Share this calculator"
                >
                  <svg className="w-4 h-4 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8.684 13.342C8.886 12.938 9 12.482 9 12c0-.482-.114-.938-.316-1.342m0 2.684a3 3 0 110-2.684m0 2.684l6.632 3.316m-6.632-6l6.632-3.316m0 0a3 3 0 105.367-2.684 3 3 0 00-5.367 2.684zm0 9.316a3 3 0 105.367 2.684 3 3 0 00-5.367-2.684z" />
                  </svg>
                  <span className="hidden sm:inline font-medium">Share Calculator</span>
                  <span className="sm:hidden font-medium">Share</span>
                </button>
                {showShareMessage && (
                  <div className="absolute bottom-full mb-2 left-1/2 transform -translate-x-1/2 bg-green-100 dark:bg-green-900 text-green-800 dark:text-green-200 px-3 py-2 rounded-lg text-xs whitespace-nowrap shadow-lg border border-green-200 dark:border-green-700 backdrop-blur-sm z-50">
                    <div className="flex items-center gap-1">
                      <span>✅</span>
                      <span>Link copied! Ready to share anywhere</span>
                    </div>
                  </div>
                )}
              </div>

              {/* Theme Toggle */}
              <div className="w-full sm:w-auto">
                <ThemeToggle />
              </div>
            </div>

            {/* Navigation Links */}
            <div className="flex flex-wrap justify-center gap-6 text-sm text-gray-600 dark:text-tertiary">
              <Link href="/about" className="hover:text-blue-600 dark:hover:text-accent transition-colors">
                About
              </Link>
              <Link href="/blog" className="hover:text-blue-600 dark:hover:text-accent transition-colors">
                Blog
              </Link>
              <Link href="/privacy-policy" className="hover:text-blue-600 dark:hover:text-accent transition-colors">
                Privacy Policy
              </Link>
              <Link href="/terms-of-service" className="hover:text-blue-600 dark:hover:text-accent transition-colors">
                Terms of Service
              </Link>
            </div>

            {/* Copyright */}
            <p className="text-gray-600 dark:text-secondary">&copy; {copyrightYears} Calc9.com - Professional Calculator Tools</p>

            {/* Version */}
            <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 dark:bg-blue-900 text-blue-800 dark:text-blue-100 border border-blue-200 dark:border-blue-700">
              {version}
            </span>

            {/* Tagline */}
            <p className="text-sm text-gray-500 dark:text-muted">
              Made for better calculations. Fast, accurate, and always free.
            </p>
          </div>
        </div>
      </footer>

      <FeedbackModal
        isOpen={isFeedbackModalOpen}
        onClose={() => setIsFeedbackModalOpen(false)}
      />
    </>
  );
}