import BackToHome from "@/components/BackToHome";
import CategoryBadge from '@/components/CategoryBadge';

type Category = 'Financial' | 'Health' | 'Math' | 'Other';

interface CalculatorHeaderProps {
  title: string;
  description: string;
  category: Category;
  className?: string;
}

export default function CalculatorHeader({
  title,
  description,
  category,
  className = "text-center mb-8"
}: CalculatorHeaderProps) {
  return (
    <header className={className}>
      <BackToHome />
      <div className="flex justify-center items-center gap-3 mb-4">
        <h1 className="text-3xl md:text-4xl font-bold text-gray-800 dark:text-primary">{title}</h1>
        <CategoryBadge category={category} />
      </div>
      <p className="text-lg text-gray-600 dark:text-secondary max-w-2xl mx-auto">
        {description}
      </p>
    </header>
  );
}