import Link from 'next/link';
import Logo from './Logo';

interface BackToHomeProps {
  className?: string;
}

export default function BackToHome({ className = "text-blue-600 dark:text-accent hover:text-blue-700 dark:hover:text-blue-300 mb-4 inline-flex items-center transition-colors duration-300" }: BackToHomeProps) {
  return (
    <Link href="/" className={className}>
      <Logo
        width={24}
        height={24}
        className="h-6 w-auto mr-2"
      />
      ← Back to Calc9
    </Link>
  );
}