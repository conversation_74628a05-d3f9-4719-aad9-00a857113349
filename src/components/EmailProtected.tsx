'use client';

interface EmailProtectedProps {
  user: string;
  domain: string;
  className?: string;
}

export default function EmailProtected({ user, domain, className = "" }: EmailProtectedProps) {
  const handleClick = () => {
    window.location.href = `mailto:${user}@${domain}`;
  };

  return (
    <button
      onClick={handleClick}
      className={`text-blue-600 hover:text-blue-700 underline cursor-pointer ${className}`}
      title="Click to send email"
    >
      {user}@{domain}
    </button>
  );
}