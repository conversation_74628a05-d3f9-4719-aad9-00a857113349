type Category = 'Financial' | 'Health' | 'Math' | 'Other';

interface CategoryBadgeProps {
  category: Category;
  className?: string;
}

const getCategoryStyle = (category: Category) => {
  switch (category) {
    case 'Financial':
      return 'bg-green-100/90 dark:bg-green-900/30 text-green-800 dark:text-green-300 border border-green-200/60 dark:border-green-400/40 backdrop-blur-sm shadow-sm dark:shadow-green-500/20';
    case 'Health':
      return 'bg-blue-100/90 dark:bg-blue-900/30 text-blue-800 dark:text-blue-300 border border-blue-200/60 dark:border-blue-400/40 backdrop-blur-sm shadow-sm dark:shadow-blue-500/20';
    case 'Math':
      return 'bg-purple-100/90 dark:bg-purple-900/30 text-purple-800 dark:text-purple-300 border border-purple-200/60 dark:border-purple-400/40 backdrop-blur-sm shadow-sm dark:shadow-purple-500/20';
    case 'Other':
      return 'bg-slate-100/90 dark:bg-slate-800/40 text-slate-800 dark:text-slate-300 border border-slate-200/60 dark:border-slate-400/40 backdrop-blur-sm shadow-sm dark:shadow-slate-500/20';
    default:
      return 'bg-slate-100/90 dark:bg-slate-800/40 text-slate-800 dark:text-slate-300 border border-slate-200/60 dark:border-slate-400/40 backdrop-blur-sm shadow-sm dark:shadow-slate-500/20';
  }
};

export default function CategoryBadge({ category, className = '' }: CategoryBadgeProps) {
  return (
    <span className={`px-2 py-1 text-xs font-medium rounded-full transition-all duration-300 ${getCategoryStyle(category)} ${className}`}>
      {category}
    </span>
  );
}