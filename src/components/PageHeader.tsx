import Link from "next/link";

interface PageHeaderProps {
  title: string;
  description?: string;
  lastUpdated?: string;
  className?: string;
}

export default function PageHeader({ 
  title, 
  description, 
  lastUpdated, 
  className = "text-center mb-8" 
}: PageHeaderProps) {
  return (
    <header className={className}>
      <Link href="/" className="text-blue-600 hover:text-blue-700 mb-4 inline-flex items-center">
        <svg className="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M10 19l-7-7m0 0l7-7m-7 7h18" />
        </svg>
        Back to Home
      </Link>
      <h1 className="text-4xl md:text-5xl font-bold text-gray-800 mb-4">{title}</h1>
      {description && (
        <p className="text-xl text-gray-600 max-w-2xl mx-auto">
          {description}
        </p>
      )}
      {lastUpdated && (
        <p className="text-lg text-gray-600 mt-2">
          Last updated: {lastUpdated}
        </p>
      )}
    </header>
  );
}