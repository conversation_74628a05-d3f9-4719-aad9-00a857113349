import Link from 'next/link';
import Footer from '@/components/Footer';
import { containerStyles } from '@/components/ui/styles';
import { blogPosts } from '@/data/blog';
import { calculators } from '@/data/calculators';

interface BlogLayoutProps {
  slug: string;
  children: React.ReactNode;
}

export default function BlogLayout({ slug, children }: BlogLayoutProps) {
  const post = blogPosts[slug];
  
  if (!post) {
    return null;
  }

  // Get related calculators data
  const relatedCalculators = post.relatedCalculators?.map(calcSlug => 
    calculators.find(calc => calc.href === `/${calcSlug}`)
  ).filter(Boolean) || [];

  return (
    <>
      <div className={containerStyles.page}>
        <div className="container mx-auto px-4">
          <div className="max-w-4xl mx-auto">
            {/* Back to Blog Navigation */}
            <header className="mb-8">
              <Link 
                href="/blog" 
                className="text-blue-600 dark:text-blue-400 hover:text-blue-700 dark:hover:text-blue-300 mb-4 inline-flex items-center transition-colors"
              >
                <svg className="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M10 19l-7-7m0 0l7-7m-7 7h18" />
                </svg>
                Back to Blog
              </Link>
              
              {/* Article Meta */}
              <div className="flex items-center gap-3 mb-4">
                <span className={`inline-block px-3 py-1 text-xs font-medium rounded-full ${
                  post.category === 'Health' ? 'bg-green-100 dark:bg-green-900/50 text-green-800 dark:text-green-200' :
                  post.category === 'Finance' ? 'bg-blue-100 dark:bg-blue-900/50 text-blue-800 dark:text-blue-200' :
                  post.category === 'Nutrition' ? 'bg-orange-100 dark:bg-orange-900/50 text-orange-800 dark:text-orange-200' :
                  post.category === 'Construction' ? 'bg-yellow-100 dark:bg-yellow-900/50 text-yellow-800 dark:text-yellow-200' :
                  post.category === 'Design' ? 'bg-purple-100 dark:bg-purple-900/50 text-purple-800 dark:text-purple-200' :
                  'bg-gray-100 dark:bg-gray-900/50 text-gray-800 dark:text-gray-200'
                }`}>
                  {post.category}
                </span>
                <span className="text-sm text-gray-500 dark:text-gray-400">{post.readTime}</span>
                <span className="text-sm text-gray-500 dark:text-gray-400">
                  {new Date(post.date).toLocaleDateString('en-US', {
                    year: 'numeric',
                    month: 'long',
                    day: 'numeric'
                  })}
                </span>
              </div>
              
              {/* Article Title and Description */}
              <h1 className="text-4xl font-bold text-gray-800 dark:text-white mb-4">
                {post.title}
              </h1>
              
              <p className="text-xl text-gray-600 dark:text-gray-300 leading-relaxed">
                {post.description}
              </p>
            </header>

            {/* Article Content */}
            <article className="bg-white dark:bg-gray-800 rounded-lg shadow-lg dark:shadow-gray-700/50 p-8 border border-gray-200 dark:border-gray-700 mb-8">
              <div className="prose prose-lg max-w-none">
                {children}
              </div>
            </article>

            {/* Related Calculators */}
            {relatedCalculators.length > 0 && (
              <div className="bg-gray-50 dark:bg-gray-900/50 rounded-lg p-6 mb-8 border border-gray-200 dark:border-gray-600">
                <h3 className="text-lg font-semibold text-gray-800 dark:text-gray-200 mb-4">
                  🧮 Related Calculators
                </h3>
                <p className="text-sm text-gray-600 dark:text-gray-400 mb-4">
                  Try these calculators mentioned in this article:
                </p>
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-3">
                  {relatedCalculators.map((calc) => (
                    <Link
                      key={calc?.href}
                      href={calc?.href || '#'}
                      className="flex items-center p-3 bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700 hover:shadow-md hover:border-blue-300 dark:hover:border-blue-600 transition-all group"
                    >
                      <div className={`w-8 h-8 rounded-lg ${calc?.iconBg} flex items-center justify-center mr-3`}>
                        <svg className={`w-4 h-4 ${calc?.iconColor}`} fill="currentColor" viewBox="0 0 24 24">
                          <path d={calc?.iconPath} />
                        </svg>
                      </div>
                      <div>
                        <div className="font-medium text-gray-800 dark:text-gray-200 group-hover:text-blue-600 dark:group-hover:text-blue-400 transition-colors">
                          {calc?.title}
                        </div>
                        <div className="text-xs text-gray-500 dark:text-gray-400">
                          {calc?.category}
                        </div>
                      </div>
                    </Link>
                  ))}
                </div>
              </div>
            )}

            {/* Article Footer */}
            <div className="border-t border-gray-200 dark:border-gray-600 pt-6 mb-8">
              <div className="flex flex-col md:flex-row md:items-center md:justify-between">
                <div className="mb-4 md:mb-0">
                  <p className="text-sm text-gray-600 dark:text-gray-400">
                    Found this article helpful? Explore more calculation tools and guides.
                  </p>
                </div>
                <div className="flex flex-wrap gap-2">
                  <Link 
                    href="/blog" 
                    className="inline-block bg-blue-100 dark:bg-blue-900/30 text-blue-800 dark:text-blue-200 px-4 py-2 rounded-lg text-sm font-medium hover:bg-blue-200 dark:hover:bg-blue-900/50 transition-colors"
                  >
                    More Articles
                  </Link>
                  <Link 
                    href="/" 
                    className="inline-block bg-gray-100 dark:bg-gray-700 text-gray-800 dark:text-gray-200 px-4 py-2 rounded-lg text-sm font-medium hover:bg-gray-200 dark:hover:bg-gray-600 transition-colors"
                  >
                    All Calculators
                  </Link>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
      <Footer />
    </>
  );
}