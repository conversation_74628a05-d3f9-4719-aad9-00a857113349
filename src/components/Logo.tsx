'use client';

import Image from 'next/image';

interface LogoProps {
  width?: number;
  height?: number;
  className?: string;
}

export default function Logo({ width = 64, height = 64, className = "" }: LogoProps) {
  return (
    <div className={`transition-colors duration-300 ${className}`}>
      <Image
        src="/logo.svg"
        alt="Calc9 Logo"
        width={width}
        height={height}
        className="text-gray-900 dark:text-gray-100"
        style={{ 
          filter: 'var(--logo-filter)',
          transition: 'filter 0.3s ease'
        }}
      />
    </div>
  );
}