import { blogPosts } from '@/data/blog';

/**
 * 生成页面标题的工具函数
 * @param pageTitle 页面具体标题
 * @param baseTitle 基础标题，默认为 "Calc9"
 * @returns 完整的页面标题
 */
export function generatePageTitle(pageTitle: string, baseTitle: string = SITE_CONFIG.baseTitle): string {
  return `${pageTitle} - ${baseTitle}`;
}

/**
 * 生成canonical URL的工具函数
 * @param path 页面路径（以/开头）
 * @returns 完整的canonical URL
 */
export function generateCanonicalUrl(path: string): string {
  const baseUrl = SITE_CONFIG.baseUrl;
  // 确保路径以/开头
  const cleanPath = path.startsWith('/') ? path : `/${path}`;
  // 移除末尾的/（除了根路径）
  const normalizedPath = cleanPath === '/' ? '/' : cleanPath.replace(/\/$/, '');
  return `${baseUrl}${normalizedPath}`;
}

/**
 * 生成博客文章metadata的工具函数
 * @param slug 文章slug
 * @returns metadata对象或null
 */
export function generateBlogMetadata(slug: string) {
  const post = blogPosts[slug];

  if (!post) {
    return null;
  }

  return {
    title: generatePageTitle(post.title),
    description: post.description,
    keywords: post.keywords,
    robots: "index, follow",
    alternates: {
      canonical: generateCanonicalUrl(`/blog/${slug}`),
    },
  };
}

/**
 * 网站配置常量
 */
export const SITE_CONFIG = {
  baseTitle: "Calc9",
  baseUrl: "https://calc9.com", // 请根据实际域名修改
  defaultDescription: "Professional calculator tools for all your calculation needs",
} as const;