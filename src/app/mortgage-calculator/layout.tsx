import { Metadata } from 'next';
import { generateCanonicalUrl } from '@/lib/utils';

export const metadata: Metadata = {
  title: 'Mortgage Calculator - Professional Home Loan Calculator | Calc9',
  description: 'Calculate mortgage payments, interest rates, and amortization schedules. Professional mortgage calculator with advanced features including PMI, taxes, insurance, and payment strategies.',
  keywords: 'mortgage calculator, home loan calculator, mortgage payment calculator, amortization schedule, PMI calculator, property tax calculator, mortgage interest rate calculator, home affordability calculator',
  openGraph: {
    title: 'Mortgage Calculator - Professional Home Loan Calculator | Calc9',
    description: 'Calculate mortgage payments, interest rates, and amortization schedules. Professional mortgage calculator with advanced features including PMI, taxes, insurance, and payment strategies.',
    url: generateCanonicalUrl('/mortgage-calculator'),
    type: 'website',
  },
  twitter: {
    card: 'summary_large_image',
    title: 'Mortgage Calculator - Professional Home Loan Calculator | Calc9',
    description: 'Calculate mortgage payments, interest rates, and amortization schedules. Professional mortgage calculator with advanced features including PMI, taxes, insurance, and payment strategies.',
  },
  alternates: {
    canonical: generateCanonicalUrl('/mortgage-calculator'),
  },
};

export default function MortgageCalculatorLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  return children;
}