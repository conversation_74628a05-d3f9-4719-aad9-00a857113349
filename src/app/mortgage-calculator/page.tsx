'use client';

import { useState, useMemo, useEffect, useCallback, Suspense } from 'react';
import { useRouter, useSearchParams } from 'next/navigation';
import { containerStyles, textStyles, inputStyles, statusStyles, cn } from '@/components/ui/styles';
import CalculatorHeader from '@/components/CalculatorHeader';
import Footer from '@/components/Footer';

interface MortgageData {
  homePrice: number;
  downPayment: number;
  downPaymentPercent: number;
  loanTerm: number;
  interestRate: number;
  startDate: string;
  propertyTax: number;
  propertyTaxPercent: number;
  isPropertyTaxPercent: boolean;
  homeInsurance: number;
  homeInsurancePercent: number;
  isHomeInsurancePercent: boolean;
  pmiInsurance: number;
  pmiInsurancePercent: number;
  isPmiInsurancePercent: boolean;
  hoaFee: number;
  hoaFeePercent: number;
  isHoaFeePercent: boolean;
  otherCosts: number;
  otherCostsPercent: number;
  isOtherCostsPercent: boolean;
  includeTaxesAndCosts: boolean;
  paymentFrequency: 'monthly' | 'biweekly';
  currency: 'USD' | 'EUR' | 'GBP' | 'JPY' | 'CNY' | 'CAD' | 'AUD' | 'CHF' | 'SEK' | 'NOK';
}


interface AmortizationEntry {
  month: number;
  date: string;
  payment: number;
  principal: number;
  interest: number;
  balance: number;
  totalPaid: number;
  totalInterest: number;
}

function MortgageCalculatorContent() {
  const router = useRouter();
  const searchParams = useSearchParams();
  
  // Default values - memoized to prevent unnecessary re-renders
  const defaultData = useMemo((): MortgageData => ({
    homePrice: 400000,
    downPayment: 80000,
    downPaymentPercent: 20,
    loanTerm: 30,
    interestRate: 6.5,
    startDate: new Date().toISOString().split('T')[0],
    propertyTax: 5000,
    propertyTaxPercent: 1.25,
    isPropertyTaxPercent: true,
    homeInsurance: 1200,
    homeInsurancePercent: 0.3,
    isHomeInsurancePercent: false,
    pmiInsurance: 0,
    pmiInsurancePercent: 0.5,
    isPmiInsurancePercent: false,
    hoaFee: 0,
    hoaFeePercent: 0,
    isHoaFeePercent: false,
    otherCosts: 0,
    otherCostsPercent: 0,
    isOtherCostsPercent: false,
    includeTaxesAndCosts: false,
    paymentFrequency: 'monthly',
    currency: 'USD',
  }), []);

  // Load data from URL params
  const loadDataFromURL = useCallback(() => {
    const data = { ...defaultData };
    
    // Load numeric values
    const numericFields = ['homePrice', 'downPayment', 'downPaymentPercent', 'loanTerm', 'interestRate', 
                          'propertyTax', 'propertyTaxPercent', 'homeInsurance', 'homeInsurancePercent',
                          'pmiInsurance', 'pmiInsurancePercent', 'hoaFee', 'hoaFeePercent', 
                          'otherCosts', 'otherCostsPercent'];
    
    numericFields.forEach(field => {
      const value = searchParams.get(field);
      if (value !== null && !isNaN(Number(value))) {
        (data as Record<string, number | string | boolean>)[field] = Number(value);
      }
    });
    
    // Load boolean values
    const booleanFields = ['isPropertyTaxPercent', 'isHomeInsurancePercent', 'isPmiInsurancePercent',
                          'isHoaFeePercent', 'isOtherCostsPercent', 'includeTaxesAndCosts'];
    
    booleanFields.forEach(field => {
      const value = searchParams.get(field);
      if (value !== null) {
        (data as Record<string, number | string | boolean>)[field] = value === 'true';
      }
    });
    
    // Load string values
    const startDate = searchParams.get('startDate');
    if (startDate) data.startDate = startDate;
    
    const currency = searchParams.get('currency');
    if (currency) data.currency = currency as MortgageData['currency'];
    
    const paymentFrequency = searchParams.get('paymentFrequency');
    if (paymentFrequency) data.paymentFrequency = paymentFrequency as MortgageData['paymentFrequency'];
    
    return data;
  }, [searchParams, defaultData]);

  const [mortgageData, setMortgageData] = useState<MortgageData>(defaultData);
  const [isDownPaymentPercent, setIsDownPaymentPercent] = useState(true);
  const [scheduleView, setScheduleView] = useState<'monthly' | 'annual'>('monthly');
  const [isLoaded, setIsLoaded] = useState(false);

  // Load data from URL on mount
  useEffect(() => {
    const data = loadDataFromURL();
    setMortgageData(data);
    setIsDownPaymentPercent(searchParams.get('isDownPaymentPercent') !== 'false');
    setIsLoaded(true);
  }, [loadDataFromURL, searchParams]);

  // Update URL when data changes
  const updateURL = useCallback((data: MortgageData) => {
    if (!isLoaded) return; // Don't update URL during initial load
    
    const params = new URLSearchParams();
    
    // Add all data to URL params
    Object.entries(data).forEach(([key, value]) => {
      if (value !== defaultData[key as keyof MortgageData]) {
        params.set(key, String(value));
      }
    });
    
    // Add down payment percent state
    if (!isDownPaymentPercent) {
      params.set('isDownPaymentPercent', 'false');
    }
    
    const newURL = params.toString() ? `?${params.toString()}` : window.location.pathname;
    router.replace(newURL, { scroll: false });
  }, [router, defaultData, isDownPaymentPercent, isLoaded]);

  const mortgageResult = useMemo(() => {
    const { 
      homePrice, 
      downPayment, 
      loanTerm, 
      interestRate, 
      propertyTax, 
      propertyTaxPercent,
      isPropertyTaxPercent,
      homeInsurance, 
      homeInsurancePercent,
      isHomeInsurancePercent,
      pmiInsurance, 
      pmiInsurancePercent,
      isPmiInsurancePercent,
      hoaFee, 
      hoaFeePercent,
      isHoaFeePercent,
      otherCosts, 
      otherCostsPercent,
      isOtherCostsPercent,
      includeTaxesAndCosts 
    } = mortgageData;
    
    const loanAmount = homePrice - downPayment;
    const monthlyRate = interestRate / 100 / 12;
    const totalMonths = loanTerm * 12;
    
    if (loanAmount <= 0 || monthlyRate <= 0) {
      return {
        monthlyPayment: 0,
        totalPayment: 0,
        totalInterest: 0,
        loanAmount: 0,
        principalAndInterest: 0,
        totalMonthlyPayment: 0,
        payoffDate: '',
        totalMonths: 0,
      };
    }
    
    const principalAndInterest = loanAmount * (monthlyRate * Math.pow(1 + monthlyRate, totalMonths)) / (Math.pow(1 + monthlyRate, totalMonths) - 1);
    
    // Calculate actual costs based on percentage or amount
    const actualPropertyTax = includeTaxesAndCosts ? (isPropertyTaxPercent ? (homePrice * propertyTaxPercent) / 100 : propertyTax) : 0;
    const actualHomeInsurance = includeTaxesAndCosts ? (isHomeInsurancePercent ? (homePrice * homeInsurancePercent) / 100 : homeInsurance) : 0;
    const actualPmiInsurance = includeTaxesAndCosts ? (isPmiInsurancePercent ? (homePrice * pmiInsurancePercent) / 100 : pmiInsurance) : 0;
    const actualHoaFee = includeTaxesAndCosts ? (isHoaFeePercent ? (homePrice * hoaFeePercent) / 100 : hoaFee) : 0;
    const actualOtherCosts = includeTaxesAndCosts ? (isOtherCostsPercent ? (homePrice * otherCostsPercent) / 100 : otherCosts) : 0;
    
    const monthlyTax = actualPropertyTax / 12;
    const monthlyInsurance = actualHomeInsurance / 12;
    const monthlyPMI = actualPmiInsurance / 12;
    const monthlyHOA = actualHoaFee / 12;
    const monthlyOther = actualOtherCosts / 12;
    
    const totalMonthlyPayment = principalAndInterest + monthlyTax + monthlyInsurance + monthlyPMI + monthlyHOA + monthlyOther;
    
    const totalPayment = principalAndInterest * totalMonths;
    const totalInterest = totalPayment - loanAmount;
    
    const payoffDate = new Date(mortgageData.startDate);
    payoffDate.setMonth(payoffDate.getMonth() + totalMonths);
    
    return {
      monthlyPayment: principalAndInterest,
      totalPayment,
      totalInterest,
      loanAmount,
      principalAndInterest,
      totalMonthlyPayment,
      payoffDate: payoffDate.toLocaleDateString(),
      totalMonths,
    };
  }, [mortgageData]);

  const amortizationSchedule = useMemo(() => {
    const { homePrice, downPayment, loanTerm, interestRate } = mortgageData;
    const loanAmount = homePrice - downPayment;
    const monthlyRate = interestRate / 100 / 12;
    const totalMonths = loanTerm * 12;
    
    if (loanAmount <= 0 || monthlyRate <= 0) return [];
    
    const monthlyPayment = loanAmount * (monthlyRate * Math.pow(1 + monthlyRate, totalMonths)) / (Math.pow(1 + monthlyRate, totalMonths) - 1);
    
    const schedule: AmortizationEntry[] = [];
    let balance = loanAmount;
    let totalPaid = 0;
    let totalInterestPaid = 0;
    let month = 0;
    
    const startDate = new Date(mortgageData.startDate);
    
    while (balance > 0.01 && month < totalMonths) {
      const interestPayment = balance * monthlyRate;
      const principalPayment = Math.min(monthlyPayment - interestPayment, balance);
      
      balance -= principalPayment;
      totalPaid += monthlyPayment;
      totalInterestPaid += interestPayment;
      month++;
      
      const currentDate = new Date(startDate);
      currentDate.setMonth(currentDate.getMonth() + month);
      
      schedule.push({
        month,
        date: currentDate.toLocaleDateString(),
        payment: monthlyPayment,
        principal: principalPayment,
        interest: interestPayment,
        balance: Math.max(0, balance),
        totalPaid,
        totalInterest: totalInterestPaid,
      });
    }
    
    return schedule;
  }, [mortgageData]);

  const annualSchedule = useMemo(() => {
    if (amortizationSchedule.length === 0) return [];
    
    const yearlyData: {
      year: number;
      loanYear: number;
      startDate: string;
      endDate: string;
      totalPayments: number;
      totalPrincipal: number;
      totalInterest: number;
      endingBalance: number;
      paymentsCount: number;
    }[] = [];
    
    const startDate = new Date(mortgageData.startDate);
    
    // Group by loan years (12 months each) instead of calendar years
    for (let loanYear = 1; loanYear <= mortgageData.loanTerm; loanYear++) {
      const yearStartMonth = (loanYear - 1) * 12;
      const yearEndMonth = Math.min(loanYear * 12, amortizationSchedule.length);
      
      // If we don't have enough payments for this year, break
      if (yearStartMonth >= amortizationSchedule.length) break;
      
      let yearTotalPayments = 0;
      let yearTotalPrincipal = 0;
      let yearTotalInterest = 0;
      let paymentsInYear = 0;
      
      // Sum up payments for this loan year
      for (let monthIndex = yearStartMonth; monthIndex < yearEndMonth; monthIndex++) {
        const entry = amortizationSchedule[monthIndex];
        if (entry) {
          yearTotalPayments += entry.payment;
          yearTotalPrincipal += entry.principal;
          yearTotalInterest += entry.interest;
          paymentsInYear++;
        }
      }
      
      // Only add if we have payments in this year
      if (paymentsInYear > 0) {
        const firstEntry = amortizationSchedule[yearStartMonth];
        const lastEntry = amortizationSchedule[yearEndMonth - 1] || amortizationSchedule[amortizationSchedule.length - 1];
        
        // Calculate the calendar year for this loan year
        const yearStartDate = new Date(startDate);
        yearStartDate.setMonth(yearStartDate.getMonth() + yearStartMonth);
        const calendarYear = yearStartDate.getFullYear();
        
        yearlyData.push({
          year: calendarYear,
          loanYear,
          startDate: firstEntry?.date || '',
          endDate: lastEntry?.date || '',
          totalPayments: yearTotalPayments,
          totalPrincipal: yearTotalPrincipal,
          totalInterest: yearTotalInterest,
          endingBalance: lastEntry?.balance || 0,
          paymentsCount: paymentsInYear,
        });
      }
    }
    
    return yearlyData;
  }, [amortizationSchedule, mortgageData.startDate, mortgageData.loanTerm]);

  const handleInputChange = (field: keyof MortgageData, value: string | number | boolean) => {
    setMortgageData(prev => {
      const newData = { ...prev, [field]: value };
      
      if (field === 'homePrice' || field === 'downPayment') {
        if (field === 'homePrice') {
          const homePrice = Number(value);
          if (isDownPaymentPercent) {
            newData.downPayment = (homePrice * prev.downPaymentPercent) / 100;
          } else {
            newData.downPaymentPercent = (prev.downPayment / homePrice) * 100;
          }
        } else {
          const downPayment = Number(value);
          if (isDownPaymentPercent) {
            newData.downPaymentPercent = (downPayment / prev.homePrice) * 100;
          } else {
            newData.downPaymentPercent = (downPayment / prev.homePrice) * 100;
          }
        }
      }
      
      if (field === 'downPaymentPercent') {
        const percent = Number(value);
        newData.downPayment = (prev.homePrice * percent) / 100;
      }

      // Handle percentage/amount conversions for tax and cost fields
      const handlePercentageConversion = (
        amountField: keyof MortgageData,
        percentField: keyof MortgageData,
        isPercentField: keyof MortgageData
      ) => {
        if (field === amountField) {
          const amount = Number(value);
          const percent = (amount / prev.homePrice) * 100;
          Object.assign(newData, { [percentField]: percent });
        } else if (field === percentField) {
          const percent = Number(value);
          const amount = (prev.homePrice * percent) / 100;
          Object.assign(newData, { [amountField]: amount });
        } else if (field === isPercentField) {
          // When toggling between percent and amount, convert current value
          const isPercent = Boolean(value);
          if (isPercent) {
            // Converting from amount to percent
            const currentAmount = prev[amountField] as number;
            const percent = (currentAmount / prev.homePrice) * 100;
            Object.assign(newData, { [percentField]: percent });
          } else {
            // Converting from percent to amount  
            const currentPercent = prev[percentField] as number;
            const amount = (prev.homePrice * currentPercent) / 100;
            Object.assign(newData, { [amountField]: amount });
          }
        }
      };

      // Apply conversions for all cost fields
      handlePercentageConversion('propertyTax', 'propertyTaxPercent', 'isPropertyTaxPercent');
      handlePercentageConversion('homeInsurance', 'homeInsurancePercent', 'isHomeInsurancePercent');
      handlePercentageConversion('pmiInsurance', 'pmiInsurancePercent', 'isPmiInsurancePercent');
      handlePercentageConversion('hoaFee', 'hoaFeePercent', 'isHoaFeePercent');
      handlePercentageConversion('otherCosts', 'otherCostsPercent', 'isOtherCostsPercent');
      
      // Update URL with new data
      updateURL(newData);
      
      return newData;
    });
  };

  const handleDownPaymentToggle = () => {
    setIsDownPaymentPercent(!isDownPaymentPercent);
  };

  // Update URL when isDownPaymentPercent changes
  useEffect(() => {
    if (isLoaded) {
      updateURL(mortgageData);
    }
  }, [isDownPaymentPercent, isLoaded, updateURL, mortgageData]);

  const getCurrencyInfo = (currency: string) => {
    const currencyMap = {
      USD: { symbol: '$', locale: 'en-US', name: 'US Dollar' },
      EUR: { symbol: '€', locale: 'de-DE', name: 'Euro' },
      GBP: { symbol: '£', locale: 'en-GB', name: 'British Pound' },
      JPY: { symbol: '¥', locale: 'ja-JP', name: 'Japanese Yen' },
      CNY: { symbol: '¥', locale: 'zh-CN', name: 'Chinese Yuan' },
      CAD: { symbol: 'C$', locale: 'en-CA', name: 'Canadian Dollar' },
      AUD: { symbol: 'A$', locale: 'en-AU', name: 'Australian Dollar' },
      CHF: { symbol: 'CHF', locale: 'de-CH', name: 'Swiss Franc' },
      SEK: { symbol: 'kr', locale: 'sv-SE', name: 'Swedish Krona' },
      NOK: { symbol: 'kr', locale: 'nb-NO', name: 'Norwegian Krone' },
    };
    return currencyMap[currency as keyof typeof currencyMap] || currencyMap.USD;
  };

  const formatCurrency = (amount: number) => {
    const currencyInfo = getCurrencyInfo(mortgageData.currency);
    const formatter = new Intl.NumberFormat(currencyInfo.locale, {
      style: 'currency',
      currency: mortgageData.currency,
      minimumFractionDigits: mortgageData.currency === 'JPY' ? 0 : 0,
      maximumFractionDigits: mortgageData.currency === 'JPY' ? 0 : 0,
    });
    return formatter.format(amount);
  };

  const getCurrencySymbol = () => {
    return getCurrencyInfo(mortgageData.currency).symbol;
  };

  // Share functionality
  const [showShareMessage, setShowShareMessage] = useState(false);
  
  const handleShare = async () => {
    const url = window.location.href;
    
    try {
      await navigator.clipboard.writeText(url);
      setShowShareMessage(true);
      setTimeout(() => setShowShareMessage(false), 2000);
    } catch {
      // Fallback for older browsers
      const textArea = document.createElement('textarea');
      textArea.value = url;
      document.body.appendChild(textArea);
      textArea.select();
      document.execCommand('copy');
      document.body.removeChild(textArea);
      setShowShareMessage(true);
      setTimeout(() => setShowShareMessage(false), 2000);
    }
  };


  return (
    <>
      <div className={containerStyles.page}>
        <div className="container mx-auto px-4">
          <div className="max-w-6xl mx-auto">
            <CalculatorHeader
              title="Mortgage Calculator"
              description="Calculate your mortgage payments, interest rates, and amortization schedules with our professional home loan calculator"
              category="Financial"
            />

            {/* Currency Selector & Share */}
            <div className={cn(containerStyles.card, "mb-8")}>
              <div className="p-6">
                <div className="flex flex-col sm:flex-row items-start sm:items-center justify-between gap-4">
                  <div>
                    <h3 className={cn(textStyles.h3, "mb-2")}>Currency Settings</h3>
                    <p className={textStyles.muted}>Choose your preferred currency for all calculations</p>
                  </div>
                  <div className="flex items-center gap-3">
                    <span className="text-2xl">{getCurrencySymbol()}</span>
                    <select
                      value={mortgageData.currency}
                      onChange={(e) => handleInputChange('currency', e.target.value)}
                      className={cn(inputStyles.select, "min-w-48")}
                    >
                      <option value="USD">🇺🇸 USD - US Dollar</option>
                      <option value="EUR">🇪🇺 EUR - Euro</option>
                      <option value="GBP">🇬🇧 GBP - British Pound</option>
                      <option value="JPY">🇯🇵 JPY - Japanese Yen</option>
                      <option value="CNY">🇨🇳 CNY - Chinese Yuan</option>
                      <option value="CAD">🇨🇦 CAD - Canadian Dollar</option>
                      <option value="AUD">🇦🇺 AUD - Australian Dollar</option>
                      <option value="CHF">🇨🇭 CHF - Swiss Franc</option>
                      <option value="SEK">🇸🇪 SEK - Swedish Krona</option>
                      <option value="NOK">🇳🇴 NOK - Norwegian Krone</option>
                    </select>
                  </div>
                </div>
                
                {/* Share Button */}
                <div className="mt-4 pt-4 border-t border-gray-200 dark:border-gray-700">
                  <div className="flex items-center justify-between">
                    <div>
                      <h4 className={cn(textStyles.h4, "mb-1")}>Share Your Calculation</h4>
                      <p className="text-sm text-gray-500 dark:text-gray-400">
                        Share this mortgage calculation with others
                      </p>
                    </div>
                    <div className="relative">
                      <button
                        onClick={handleShare}
                        className="flex items-center gap-2 px-4 py-2 bg-blue-600 hover:bg-blue-700 text-white rounded-lg transition-colors font-medium"
                      >
                        <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8.684 13.342C8.886 12.938 9 12.482 9 12c0-.482-.114-.938-.316-1.342m0 2.684a3 3 0 110-2.684m0 2.684l6.632 3.316m-6.632-6l6.632-3.316m0 0a3 3 0 105.367-2.684 3 3 0 00-5.367 2.684zm0 9.316a3 3 0 105.367 2.684 3 3 0 00-5.367-2.684z" />
                        </svg>
                        Share Link
                      </button>
                      
                      {/* Success Message */}
                      {showShareMessage && (
                        <div className="absolute top-full right-0 mt-2 px-3 py-2 bg-green-600 text-white text-sm rounded-lg shadow-lg whitespace-nowrap z-10">
                          Link copied to clipboard!
                        </div>
                      )}
                    </div>
                  </div>
                </div>
              </div>
            </div>

            {/* Main Content Grid */}
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
              {/* Input Form Section */}
              <div className={containerStyles.form}>
                <h3 className={cn(textStyles.h2, "mb-6")}>Loan Information</h3>
                
                <div className="space-y-6">
                  {/* Basic Loan Details */}
                  <div className="space-y-4">
                    <div>
                      <label className={cn(textStyles.label, "block mb-2")}>Home Price</label>
                      <div className="relative">
                        <span className="absolute left-3 top-1/2 -translate-y-1/2 text-gray-900 dark:text-gray-100 font-semibold z-10">{getCurrencySymbol()}</span>
                        <input
                          type="number"
                          value={mortgageData.homePrice}
                          onChange={(e) => handleInputChange('homePrice', Number(e.target.value))}
                          className={cn(inputStyles.base, "pl-10")}
                          placeholder="400,000"
                        />
                      </div>
                    </div>

                    <div>
                      <div className="flex items-center justify-between mb-2">
                        <label className={textStyles.label}>Down Payment</label>
                        <button
                          onClick={handleDownPaymentToggle}
                          className="text-sm text-blue-600 hover:text-blue-800 font-medium"
                        >
                          {isDownPaymentPercent ? `Switch to ${getCurrencySymbol()}` : 'Switch to %'}
                        </button>
                      </div>
                      <div className="relative">
                        <span className="absolute left-3 top-1/2 -translate-y-1/2 text-gray-900 dark:text-gray-100 font-semibold z-10">
                          {isDownPaymentPercent ? '%' : getCurrencySymbol()}
                        </span>
                        <input
                          type="number"
                          value={isDownPaymentPercent ? mortgageData.downPaymentPercent : mortgageData.downPayment}
                          onChange={(e) => handleInputChange(
                            isDownPaymentPercent ? 'downPaymentPercent' : 'downPayment',
                            Number(e.target.value)
                          )}
                          className={cn(inputStyles.base, isDownPaymentPercent ? "pl-8" : "pl-10")}
                          placeholder={isDownPaymentPercent ? "20" : "80,000"}
                        />
                      </div>
                    </div>

                    <div>
                      <label className={cn(textStyles.label, "block mb-2")}>Loan Term</label>
                      <select
                        value={mortgageData.loanTerm}
                        onChange={(e) => handleInputChange('loanTerm', Number(e.target.value))}
                        className={inputStyles.select}
                      >
                        <option value={15}>15 years</option>
                        <option value={20}>20 years</option>
                        <option value={25}>25 years</option>
                        <option value={30}>30 years</option>
                      </select>
                    </div>

                    <div>
                      <label className={cn(textStyles.label, "block mb-2")}>Interest Rate (%)</label>
                      <input
                        type="number"
                        step="0.1"
                        value={mortgageData.interestRate}
                        onChange={(e) => handleInputChange('interestRate', Number(e.target.value))}
                        className={inputStyles.base}
                        placeholder="6.5"
                      />
                    </div>

                    <div>
                      <label className={cn(textStyles.label, "block mb-2")}>Start Date</label>
                      <input
                        type="date"
                        value={mortgageData.startDate}
                        onChange={(e) => handleInputChange('startDate', e.target.value)}
                        className={inputStyles.base}
                      />
                    </div>
                  </div>

                  {/* Include Taxes & Costs Toggle */}
                  <div className="border-t pt-6">
                    <div className="flex items-center justify-between mb-4">
                      <div>
                        <h4 className={cn(textStyles.h4, "mb-1")}>Include Taxes & Costs</h4>
                        <p className="text-sm text-gray-500 dark:text-gray-400">
                          Include property taxes, insurance, and other costs in your payment calculation
                        </p>
                      </div>
                      <button
                        onClick={() => handleInputChange('includeTaxesAndCosts', !mortgageData.includeTaxesAndCosts)}
                        className={`relative inline-flex h-6 w-11 items-center rounded-full ${
                          mortgageData.includeTaxesAndCosts 
                            ? 'bg-blue-600' 
                            : 'bg-gray-200 dark:bg-gray-700'
                        } transition-colors`}
                      >
                        <span
                          className={`inline-block h-4 w-4 transform rounded-full bg-white transition-transform ${
                            mortgageData.includeTaxesAndCosts ? 'translate-x-6' : 'translate-x-1'
                          }`}
                        />
                      </button>
                    </div>
                    
                    {/* Additional Costs - Conditionally Rendered */}
                    {mortgageData.includeTaxesAndCosts && (
                      <div className="space-y-4 pl-4 border-l-2 border-gray-200 dark:border-gray-700">
                        <h5 className={cn(textStyles.h4, "mb-4")}>Additional Costs (Annual)</h5>
                        <div className="space-y-4">
                          <div>
                            <div className="flex items-center justify-between mb-2">
                              <label className={cn(textStyles.label, "flex items-center")}>
                                Property Taxes
                                <span className="ml-1 text-gray-400 cursor-help" title="Annual property taxes paid to your local government, typically 1-2% of home value">?</span>
                              </label>
                              <button
                                onClick={() => handleInputChange('isPropertyTaxPercent', !mortgageData.isPropertyTaxPercent)}
                                className="text-sm text-blue-600 hover:text-blue-800 font-medium"
                              >
                                {mortgageData.isPropertyTaxPercent ? `Switch to ${getCurrencySymbol()}` : 'Switch to %'}
                              </button>
                            </div>
                            <div className="relative">
                              <span className="absolute left-3 top-1/2 -translate-y-1/2 text-gray-900 dark:text-gray-100 font-semibold z-10">
                                {mortgageData.isPropertyTaxPercent ? '%' : getCurrencySymbol()}
                              </span>
                              <input
                                type="number"
                                step={mortgageData.isPropertyTaxPercent ? "0.01" : "1"}
                                value={mortgageData.isPropertyTaxPercent ? mortgageData.propertyTaxPercent : mortgageData.propertyTax}
                                onChange={(e) => handleInputChange(
                                  mortgageData.isPropertyTaxPercent ? 'propertyTaxPercent' : 'propertyTax',
                                  Number(e.target.value)
                                )}
                                className={cn(inputStyles.base, mortgageData.isPropertyTaxPercent ? "pl-8" : "pl-10")}
                                placeholder={mortgageData.isPropertyTaxPercent ? "1.25" : "5,000"}
                              />
                            </div>
                          </div>

                          <div>
                            <div className="flex items-center justify-between mb-2">
                              <label className={cn(textStyles.label, "flex items-center")}>
                                Home Insurance
                                <span className="ml-1 text-gray-400 cursor-help" title="Annual homeowners insurance premium to protect your property">?</span>
                              </label>
                              <button
                                onClick={() => handleInputChange('isHomeInsurancePercent', !mortgageData.isHomeInsurancePercent)}
                                className="text-sm text-blue-600 hover:text-blue-800 font-medium"
                              >
                                {mortgageData.isHomeInsurancePercent ? `Switch to ${getCurrencySymbol()}` : 'Switch to %'}
                              </button>
                            </div>
                            <div className="relative">
                              <span className="absolute left-3 top-1/2 -translate-y-1/2 text-gray-900 dark:text-gray-100 font-semibold z-10">
                                {mortgageData.isHomeInsurancePercent ? '%' : getCurrencySymbol()}
                              </span>
                              <input
                                type="number"
                                step={mortgageData.isHomeInsurancePercent ? "0.01" : "1"}
                                value={mortgageData.isHomeInsurancePercent ? mortgageData.homeInsurancePercent : mortgageData.homeInsurance}
                                onChange={(e) => handleInputChange(
                                  mortgageData.isHomeInsurancePercent ? 'homeInsurancePercent' : 'homeInsurance',
                                  Number(e.target.value)
                                )}
                                className={cn(inputStyles.base, mortgageData.isHomeInsurancePercent ? "pl-8" : "pl-10")}
                                placeholder={mortgageData.isHomeInsurancePercent ? "0.30" : "1,200"}
                              />
                            </div>
                          </div>

                          <div>
                            <div className="flex items-center justify-between mb-2">
                              <label className={cn(textStyles.label, "flex items-center")}>
                                PMI Insurance
                                <span className="ml-1 text-gray-400 cursor-help" title="Private Mortgage Insurance, required when down payment is less than 20% of home value">?</span>
                              </label>
                              <button
                                onClick={() => handleInputChange('isPmiInsurancePercent', !mortgageData.isPmiInsurancePercent)}
                                className="text-sm text-blue-600 hover:text-blue-800 font-medium"
                              >
                                {mortgageData.isPmiInsurancePercent ? `Switch to ${getCurrencySymbol()}` : 'Switch to %'}
                              </button>
                            </div>
                            <div className="relative">
                              <span className="absolute left-3 top-1/2 -translate-y-1/2 text-gray-900 dark:text-gray-100 font-semibold z-10">
                                {mortgageData.isPmiInsurancePercent ? '%' : getCurrencySymbol()}
                              </span>
                              <input
                                type="number"
                                step={mortgageData.isPmiInsurancePercent ? "0.01" : "1"}
                                value={mortgageData.isPmiInsurancePercent ? mortgageData.pmiInsurancePercent : mortgageData.pmiInsurance}
                                onChange={(e) => handleInputChange(
                                  mortgageData.isPmiInsurancePercent ? 'pmiInsurancePercent' : 'pmiInsurance',
                                  Number(e.target.value)
                                )}
                                className={cn(inputStyles.base, mortgageData.isPmiInsurancePercent ? "pl-8" : "pl-10")}
                                placeholder={mortgageData.isPmiInsurancePercent ? "0.50" : "0"}
                              />
                            </div>
                          </div>

                          <div>
                            <div className="flex items-center justify-between mb-2">
                              <label className={cn(textStyles.label, "flex items-center")}>
                                HOA Fee
                                <span className="ml-1 text-gray-400 cursor-help" title="Annual Homeowners Association fees for shared amenities and maintenance">?</span>
                              </label>
                              <button
                                onClick={() => handleInputChange('isHoaFeePercent', !mortgageData.isHoaFeePercent)}
                                className="text-sm text-blue-600 hover:text-blue-800 font-medium"
                              >
                                {mortgageData.isHoaFeePercent ? `Switch to ${getCurrencySymbol()}` : 'Switch to %'}
                              </button>
                            </div>
                            <div className="relative">
                              <span className="absolute left-3 top-1/2 -translate-y-1/2 text-gray-900 dark:text-gray-100 font-semibold z-10">
                                {mortgageData.isHoaFeePercent ? '%' : getCurrencySymbol()}
                              </span>
                              <input
                                type="number"
                                step={mortgageData.isHoaFeePercent ? "0.01" : "1"}
                                value={mortgageData.isHoaFeePercent ? mortgageData.hoaFeePercent : mortgageData.hoaFee}
                                onChange={(e) => handleInputChange(
                                  mortgageData.isHoaFeePercent ? 'hoaFeePercent' : 'hoaFee',
                                  Number(e.target.value)
                                )}
                                className={cn(inputStyles.base, mortgageData.isHoaFeePercent ? "pl-8" : "pl-10")}
                                placeholder={mortgageData.isHoaFeePercent ? "0.00" : "0"}
                              />
                            </div>
                          </div>

                          <div>
                            <div className="flex items-center justify-between mb-2">
                              <label className={cn(textStyles.label, "flex items-center")}>
                                Other Costs
                                <span className="ml-1 text-gray-400 cursor-help" title="Any additional annual costs related to homeownership not covered above">?</span>
                              </label>
                              <button
                                onClick={() => handleInputChange('isOtherCostsPercent', !mortgageData.isOtherCostsPercent)}
                                className="text-sm text-blue-600 hover:text-blue-800 font-medium"
                              >
                                {mortgageData.isOtherCostsPercent ? `Switch to ${getCurrencySymbol()}` : 'Switch to %'}
                              </button>
                            </div>
                            <div className="relative">
                              <span className="absolute left-3 top-1/2 -translate-y-1/2 text-gray-900 dark:text-gray-100 font-semibold z-10">
                                {mortgageData.isOtherCostsPercent ? '%' : getCurrencySymbol()}
                              </span>
                              <input
                                type="number"
                                step={mortgageData.isOtherCostsPercent ? "0.01" : "1"}
                                value={mortgageData.isOtherCostsPercent ? mortgageData.otherCostsPercent : mortgageData.otherCosts}
                                onChange={(e) => handleInputChange(
                                  mortgageData.isOtherCostsPercent ? 'otherCostsPercent' : 'otherCosts',
                                  Number(e.target.value)
                                )}
                                className={cn(inputStyles.base, mortgageData.isOtherCostsPercent ? "pl-8" : "pl-10")}
                                placeholder={mortgageData.isOtherCostsPercent ? "0.00" : "0"}
                              />
                            </div>
                          </div>

                        </div>
                      </div>
                    )}
                  </div>
                </div>
              </div>

              {/* Results Section */}
              <div className={containerStyles.form}>
                <h3 className={cn(textStyles.h2, "mb-6")}>Calculation Results</h3>
                
                {/* Key Results */}
                <div className="grid grid-cols-2 gap-4 mb-6">
                  <div className="text-center p-4 bg-blue-50 dark:bg-blue-900/20 rounded-lg">
                    <div className="text-xl font-bold text-blue-600 dark:text-blue-400">
                      {formatCurrency(mortgageResult.principalAndInterest)}
                    </div>
                    <div className="text-sm text-gray-600 dark:text-gray-400 mt-1">
                      Principal & Interest
                    </div>
                  </div>
                  
                  <div className="text-center p-4 bg-green-50 dark:bg-green-900/20 rounded-lg">
                    <div className="text-xl font-bold text-green-600 dark:text-green-400">
                      {formatCurrency(mortgageResult.totalMonthlyPayment)}
                    </div>
                    <div className="text-sm text-gray-600 dark:text-gray-400 mt-1">
                      Total Monthly Payment
                    </div>
                  </div>
                  
                  <div className="text-center p-4 bg-purple-50 dark:bg-purple-900/20 rounded-lg">
                    <div className="text-xl font-bold text-purple-600 dark:text-purple-400">
                      {formatCurrency(mortgageResult.loanAmount)}
                    </div>
                    <div className="text-sm text-gray-600 dark:text-gray-400 mt-1">
                      Loan Amount
                    </div>
                  </div>
                  
                  <div className="text-center p-4 bg-orange-50 dark:bg-orange-900/20 rounded-lg">
                    <div className="text-xl font-bold text-orange-600 dark:text-orange-400">
                      {formatCurrency(mortgageResult.totalInterest)}
                    </div>
                    <div className="text-sm text-gray-600 dark:text-gray-400 mt-1">
                      Total Interest
                    </div>
                  </div>
                </div>

                {/* Loan Summary */}
                <div className={statusStyles.success.container}>
                  <h4 className={statusStyles.success.title}>Loan Summary</h4>
                  <div className="space-y-3">
                    <div className="flex justify-between">
                      <span className={statusStyles.success.textSmall}>Home Price:</span>
                      <span className={statusStyles.success.text}>{formatCurrency(mortgageData.homePrice)}</span>
                    </div>
                    <div className="flex justify-between">
                      <span className={statusStyles.success.textSmall}>Down Payment:</span>
                      <span className={statusStyles.success.text}>{formatCurrency(mortgageData.downPayment)}</span>
                    </div>
                    <div className="flex justify-between">
                      <span className={statusStyles.success.textSmall}>Loan Amount:</span>
                      <span className={statusStyles.success.text}>{formatCurrency(mortgageResult.loanAmount)}</span>
                    </div>
                    <div className="flex justify-between">
                      <span className={statusStyles.success.textSmall}>Total Interest:</span>
                      <span className={statusStyles.success.text}>{formatCurrency(mortgageResult.totalInterest)}</span>
                    </div>
                    <div className="flex justify-between">
                      <span className={statusStyles.success.textSmall}>Total of Payments:</span>
                      <span className={statusStyles.success.text}>{formatCurrency(mortgageResult.totalPayment)}</span>
                    </div>
                    <div className="flex justify-between">
                      <span className={statusStyles.success.textSmall}>Payoff Date:</span>
                      <span className={statusStyles.success.text}>{mortgageResult.payoffDate}</span>
                    </div>
                  </div>
                </div>

                {/* Monthly Payment Breakdown */}
                <div className={cn(statusStyles.info.container, "mt-6")}>
                  <h4 className={statusStyles.info.title}>Monthly Payment Breakdown</h4>
                  <div className="space-y-3">
                    <div className="flex justify-between">
                      <span className={statusStyles.info.textSmall}>Principal & Interest:</span>
                      <span className={statusStyles.info.text}>{formatCurrency(mortgageResult.principalAndInterest)}</span>
                    </div>
                    {mortgageData.includeTaxesAndCosts && (
                      <>
                        <div className="flex justify-between">
                          <span className={statusStyles.info.textSmall}>Property Tax:</span>
                          <span className={statusStyles.info.text}>
                            {formatCurrency((mortgageData.isPropertyTaxPercent 
                              ? (mortgageData.homePrice * mortgageData.propertyTaxPercent) / 100 
                              : mortgageData.propertyTax) / 12)}
                          </span>
                        </div>
                        <div className="flex justify-between">
                          <span className={statusStyles.info.textSmall}>Home Insurance:</span>
                          <span className={statusStyles.info.text}>
                            {formatCurrency((mortgageData.isHomeInsurancePercent 
                              ? (mortgageData.homePrice * mortgageData.homeInsurancePercent) / 100 
                              : mortgageData.homeInsurance) / 12)}
                          </span>
                        </div>
                        {((mortgageData.isPmiInsurancePercent ? mortgageData.pmiInsurancePercent : mortgageData.pmiInsurance) > 0) && (
                          <div className="flex justify-between">
                            <span className={statusStyles.info.textSmall}>PMI Insurance:</span>
                            <span className={statusStyles.info.text}>
                              {formatCurrency((mortgageData.isPmiInsurancePercent 
                                ? (mortgageData.homePrice * mortgageData.pmiInsurancePercent) / 100 
                                : mortgageData.pmiInsurance) / 12)}
                            </span>
                          </div>
                        )}
                        {((mortgageData.isHoaFeePercent ? mortgageData.hoaFeePercent : mortgageData.hoaFee) > 0) && (
                          <div className="flex justify-between">
                            <span className={statusStyles.info.textSmall}>HOA Fee:</span>
                            <span className={statusStyles.info.text}>
                              {formatCurrency((mortgageData.isHoaFeePercent 
                                ? (mortgageData.homePrice * mortgageData.hoaFeePercent) / 100 
                                : mortgageData.hoaFee) / 12)}
                            </span>
                          </div>
                        )}
                        {((mortgageData.isOtherCostsPercent ? mortgageData.otherCostsPercent : mortgageData.otherCosts) > 0) && (
                          <div className="flex justify-between">
                            <span className={statusStyles.info.textSmall}>Other Costs:</span>
                            <span className={statusStyles.info.text}>
                              {formatCurrency((mortgageData.isOtherCostsPercent 
                                ? (mortgageData.homePrice * mortgageData.otherCostsPercent) / 100 
                                : mortgageData.otherCosts) / 12)}
                            </span>
                          </div>
                        )}
                      </>
                    )}
                    <div className="border-t pt-2">
                      <div className="flex justify-between font-semibold">
                        <span className={statusStyles.info.text}>Total Monthly:</span>
                        <span className={statusStyles.info.text}>{formatCurrency(mortgageResult.totalMonthlyPayment)}</span>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>

            {/* Amortization Schedule */}
            <div className={cn(containerStyles.card, "mt-8")}>
              <div className="p-6">
                <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4 mb-6">
                  <div>
                    <h3 className={textStyles.h3}>Amortization Schedule</h3>
                    <div className="text-sm text-gray-500 mt-1">
                      {scheduleView === 'monthly' 
                        ? `${amortizationSchedule.length} total payments` 
                        : `${annualSchedule.length} years`}
                    </div>
                  </div>
                  
                  <div className="flex bg-gray-100 dark:bg-gray-800 p-1 rounded-lg">
                    <button
                      onClick={() => setScheduleView('monthly')}
                      className={`px-4 py-2 rounded-md text-sm font-medium transition-colors ${
                        scheduleView === 'monthly'
                          ? 'bg-white dark:bg-gray-700 text-gray-900 dark:text-white shadow-sm'
                          : 'text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-200'
                      }`}
                    >
                      📅 Monthly
                    </button>
                    <button
                      onClick={() => setScheduleView('annual')}
                      className={`px-4 py-2 rounded-md text-sm font-medium transition-colors ${
                        scheduleView === 'annual'
                          ? 'bg-white dark:bg-gray-700 text-gray-900 dark:text-white shadow-sm'
                          : 'text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-200'
                      }`}
                    >
                      📊 Annual
                    </button>
                  </div>
                </div>
                
                <div className="overflow-x-auto bg-white dark:bg-gray-800 rounded-lg shadow">
                  <div>
                    <table className="w-full text-sm">
                      <thead className="bg-gray-50 dark:bg-gray-700 sticky top-0">
                        {scheduleView === 'monthly' ? (
                          <tr>
                            <th className="px-6 py-3 text-left font-medium text-gray-900 dark:text-white">Payment #</th>
                            <th className="px-6 py-3 text-left font-medium text-gray-900 dark:text-white">Date</th>
                            <th className="px-6 py-3 text-right font-medium text-gray-900 dark:text-white">Payment</th>
                            <th className="px-6 py-3 text-right font-medium text-gray-900 dark:text-white">Principal</th>
                            <th className="px-6 py-3 text-right font-medium text-gray-900 dark:text-white">Interest</th>
                            <th className="px-6 py-3 text-right font-medium text-gray-900 dark:text-white">Balance</th>
                          </tr>
                        ) : (
                          <tr>
                            <th className="px-6 py-3 text-left font-medium text-gray-900 dark:text-white">Calendar Year</th>
                            <th className="px-6 py-3 text-left font-medium text-gray-900 dark:text-white">Loan Year</th>
                            <th className="px-6 py-3 text-left font-medium text-gray-900 dark:text-white">Period</th>
                            <th className="px-6 py-3 text-right font-medium text-gray-900 dark:text-white">Payments</th>
                            <th className="px-6 py-3 text-right font-medium text-gray-900 dark:text-white">Principal</th>
                            <th className="px-6 py-3 text-right font-medium text-gray-900 dark:text-white">Interest</th>
                            <th className="px-6 py-3 text-right font-medium text-gray-900 dark:text-white">Balance</th>
                          </tr>
                        )}
                      </thead>
                      <tbody className="divide-y divide-gray-200 dark:divide-gray-600">
                        {scheduleView === 'monthly' 
                          ? amortizationSchedule.map((entry) => (
                              <tr key={entry.month} className="hover:bg-gray-50 dark:hover:bg-gray-700/50">
                                <td className="px-6 py-3 text-gray-900 dark:text-white">{entry.month}</td>
                                <td className="px-6 py-3 text-gray-900 dark:text-white">{entry.date}</td>
                                <td className="px-6 py-3 text-right text-gray-900 dark:text-white">
                                  {formatCurrency(entry.payment)}
                                </td>
                                <td className="px-6 py-3 text-right text-gray-900 dark:text-white">
                                  {formatCurrency(entry.principal)}
                                </td>
                                <td className="px-6 py-3 text-right text-gray-900 dark:text-white">
                                  {formatCurrency(entry.interest)}
                                </td>
                                <td className="px-6 py-3 text-right text-gray-900 dark:text-white">
                                  {formatCurrency(entry.balance)}
                                </td>
                              </tr>
                            ))
                          : annualSchedule.map((yearData) => (
                              <tr key={yearData.year} className="hover:bg-gray-50 dark:hover:bg-gray-700/50">
                                <td className="px-6 py-3 text-gray-900 dark:text-white font-medium">{yearData.year}</td>
                                <td className="px-6 py-3 text-gray-900 dark:text-white">
                                  <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200">
                                    Year {yearData.loanYear}
                                  </span>
                                </td>
                                <td className="px-6 py-3 text-gray-900 dark:text-white text-sm">
                                  {yearData.startDate} - {yearData.endDate}
                                  <div className="text-xs text-gray-500 dark:text-gray-400">
                                    {yearData.paymentsCount} payments
                                  </div>
                                </td>
                                <td className="px-6 py-3 text-right text-gray-900 dark:text-white">
                                  {formatCurrency(yearData.totalPayments)}
                                </td>
                                <td className="px-6 py-3 text-right text-gray-900 dark:text-white">
                                  {formatCurrency(yearData.totalPrincipal)}
                                </td>
                                <td className="px-6 py-3 text-right text-gray-900 dark:text-white">
                                  {formatCurrency(yearData.totalInterest)}
                                </td>
                                <td className="px-6 py-3 text-right text-gray-900 dark:text-white">
                                  {formatCurrency(yearData.endingBalance)}
                                </td>
                              </tr>
                            ))
                        }
                      </tbody>
                    </table>
                  </div>
                </div>
                
                <div className="text-center py-2 mt-4">
                  <p className="text-sm text-gray-500 dark:text-gray-400">
                    {scheduleView === 'monthly' 
                      ? `Showing all ${amortizationSchedule.length} monthly payments`
                      : `Showing all ${annualSchedule.length} loan years (${mortgageData.loanTerm}-year loan term)`}
                  </p>
                </div>
              </div>
            </div>

            {/* Tips Section */}
            <div className={cn(statusStyles.info.container, "mt-8")}>
              <h3 className={statusStyles.info.title}>Understanding Your Mortgage</h3>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div className="space-y-3">
                  <div className="flex items-start gap-3">
                    <div className="w-2 h-2 bg-blue-500 rounded-full mt-2"></div>
                    <div>
                      <h4 className="font-medium text-gray-900 dark:text-white mb-1">Principal & Interest</h4>
                      <p className="text-sm text-gray-600 dark:text-gray-400">
                        The core loan payment that goes toward the loan balance and lender profit.
                      </p>
                    </div>
                  </div>
                  <div className="flex items-start gap-3">
                    <div className="w-2 h-2 bg-green-500 rounded-full mt-2"></div>
                    <div>
                      <h4 className="font-medium text-gray-900 dark:text-white mb-1">Property Tax</h4>
                      <p className="text-sm text-gray-600 dark:text-gray-400">
                        Annual tax paid to local government, typically 1-2% of home value.
                      </p>
                    </div>
                  </div>
                </div>
                
                <div className="space-y-3">
                  <div className="flex items-start gap-3">
                    <div className="w-2 h-2 bg-purple-500 rounded-full mt-2"></div>
                    <div>
                      <h4 className="font-medium text-gray-900 dark:text-white mb-1">Extra Payments</h4>
                      <p className="text-sm text-gray-600 dark:text-gray-400">
                        Adding extra to your monthly payment can save thousands in interest.
                      </p>
                    </div>
                  </div>
                  <div className="flex items-start gap-3">
                    <div className="w-2 h-2 bg-yellow-500 rounded-full mt-2"></div>
                    <div>
                      <h4 className="font-medium text-gray-900 dark:text-white mb-1">PMI Insurance</h4>
                      <p className="text-sm text-gray-600 dark:text-gray-400">
                        Required when down payment is less than 20% of home value.
                      </p>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
      <Footer />
    </>
  );
}

export default function MortgageCalculator() {
  return (
    <Suspense fallback={<div>Loading...</div>}>
      <MortgageCalculatorContent />
    </Suspense>
  );
}