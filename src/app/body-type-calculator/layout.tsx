import type { Metadata } from "next";
import { generatePageTitle, generateCanonicalUrl } from "@/lib/utils";

export const metadata: Metadata = {
  title: generatePageTitle("Body Type Calculator - Find Your Body Shape"),
  description: "Discover your body type with our comprehensive body type calculator. Determine if you're an ectomorph, mesomorph, or endomorph and get personalized diet and exercise recommendations.",
  keywords: "body type calculator, ectomorph, mesomorph, endomorph, body type test, somatotype calculator, fitness calculator, body composition",
  openGraph: {
    title: "Body Type Calculator - Find Your Somatotype",
    description: "Professional body type calculator to determine your somatotype. Get personalized recommendations for diet and exercise based on your body type.",
    type: "website",
    url: generateCanonicalUrl("/body-type-calculator"),
  },
  alternates: {
    canonical: generateCanonicalUrl("/body-type-calculator"),
  },
};

export default function BodyTypeCalculatorLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  return children;
}