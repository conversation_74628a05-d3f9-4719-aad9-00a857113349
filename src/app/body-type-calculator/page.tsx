"use client";

import { useState } from "react";
import Footer from "@/components/Footer";
import CalculatorHeader from '@/components/CalculatorHeader';
import { containerStyles, textStyles, inputStyles, buttonStyles, statusStyles, borderStyles, cn } from '@/components/ui/styles';

interface FormData {
  age: string;
  gender: "male" | "female" | "";
  height: string;
  weight: string;
  wristCircumference: string;
  shoulderWidth: string;
  waistSize: string;
  bodyFat: string;
  metabolismRate: "fast" | "normal" | "slow" | "";
  muscleGain: "easy" | "normal" | "difficult" | "";
  weightGain: "easy" | "normal" | "difficult" | "";
}

interface BodyTypeResult {
  type: "ectomorph" | "mesomorph" | "endomorph" | "mixed";
  percentage: {
    ectomorph: number;
    mesomorph: number;
    endomorph: number;
  };
  description: string;
  characteristics: string[];
  recommendations: string[];
}

export default function BodyTypeCalculator() {
  const [formData, setFormData] = useState<FormData>({
    age: "",
    gender: "",
    height: "",
    weight: "",
    wristCircumference: "",
    shoulderWidth: "",
    waistSize: "",
    bodyFat: "",
    metabolismRate: "",
    muscleGain: "",
    weightGain: "",
  });

  const [result, setResult] = useState<BodyTypeResult | null>(null);
  const [isCalculating, setIsCalculating] = useState(false);

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement>) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: value
    }));
  };

  const calculateBodyType = (): BodyTypeResult => {
    // Calculate frame size based on wrist circumference and height
    const height = parseFloat(formData.height);
    const wrist = parseFloat(formData.wristCircumference);
    const weight = parseFloat(formData.weight);
    const waist = parseFloat(formData.waistSize);
    const shoulder = parseFloat(formData.shoulderWidth);
    const bodyFat = parseFloat(formData.bodyFat);

    let ectomorphPoints = 0;
    let mesomorphPoints = 0;
    let endomorphPoints = 0;

    // Frame size calculation (wrist-to-height ratio)
    if (height && wrist) {
      const frameRatio = height / wrist;
      if (frameRatio > 10.4) ectomorphPoints += 2;
      else if (frameRatio > 9.6) mesomorphPoints += 2;
      else endomorphPoints += 2;
    }

    // BMI calculation
    if (height && weight) {
      const heightM = height / 100;
      const bmi = weight / (heightM * heightM);
      if (bmi < 18.5) ectomorphPoints += 2;
      else if (bmi < 25) mesomorphPoints += 2;
      else endomorphPoints += 2;
    }

    // Waist-to-shoulder ratio
    if (waist && shoulder) {
      const waistShoulderRatio = waist / shoulder;
      if (waistShoulderRatio < 0.75) ectomorphPoints += 1;
      else if (waistShoulderRatio < 0.85) mesomorphPoints += 1;
      else endomorphPoints += 1;
    }

    // Body fat percentage
    if (bodyFat) {
      const isMale = formData.gender === "male";
      if (isMale) {
        if (bodyFat < 8) ectomorphPoints += 2;
        else if (bodyFat < 15) mesomorphPoints += 2;
        else endomorphPoints += 2;
      } else {
        if (bodyFat < 16) ectomorphPoints += 2;
        else if (bodyFat < 25) mesomorphPoints += 2;
        else endomorphPoints += 2;
      }
    }

    // Metabolism rate
    if (formData.metabolismRate === "fast") ectomorphPoints += 2;
    else if (formData.metabolismRate === "normal") mesomorphPoints += 2;
    else if (formData.metabolismRate === "slow") endomorphPoints += 2;

    // Muscle gain difficulty
    if (formData.muscleGain === "difficult") ectomorphPoints += 2;
    else if (formData.muscleGain === "normal") mesomorphPoints += 2;
    else if (formData.muscleGain === "easy") endomorphPoints += 1;

    // Weight gain tendency
    if (formData.weightGain === "difficult") ectomorphPoints += 2;
    else if (formData.weightGain === "normal") mesomorphPoints += 2;
    else if (formData.weightGain === "easy") endomorphPoints += 2;

    const totalPoints = ectomorphPoints + mesomorphPoints + endomorphPoints;
    
    const ectomorphPercent = Math.round((ectomorphPoints / totalPoints) * 100) || 0;
    const mesomorphPercent = Math.round((mesomorphPoints / totalPoints) * 100) || 0;
    const endomorphPercent = Math.round((endomorphPoints / totalPoints) * 100) || 0;

    let primaryType: "ectomorph" | "mesomorph" | "endomorph" | "mixed" = "mixed";
    
    if (ectomorphPercent > mesomorphPercent && ectomorphPercent > endomorphPercent) {
      primaryType = "ectomorph";
    } else if (mesomorphPercent > ectomorphPercent && mesomorphPercent > endomorphPercent) {
      primaryType = "mesomorph";
    } else if (endomorphPercent > ectomorphPercent && endomorphPercent > mesomorphPercent) {
      primaryType = "endomorph";
    }

    const bodyTypeData = {
      ectomorph: {
        description: "Ectomorphs typically have a lean, slender build with a fast metabolism. They often struggle to gain weight and muscle mass but tend to stay naturally thin with lower body fat levels.",
        characteristics: [
          "Naturally lean and slender physique",
          "Fast metabolism - burns calories quickly",
          "Difficulty gaining weight and muscle mass",
          "Small bone structure and narrow frame",
          "Low body fat percentage naturally",
          "Long limbs relative to torso"
        ],
        recommendations: [
          "Focus on compound exercises and heavy lifting",
          "Eat frequent, calorie-dense meals",
          "Limit cardio to preserve muscle mass",
          "Include healthy fats and complex carbs",
          "Allow adequate rest between workouts",
          "Consider weight gainer supplements if needed"
        ]
      },
      mesomorph: {
        description: "Mesomorphs have a naturally athletic, muscular build with an efficient metabolism. They tend to gain muscle easily and maintain a balanced physique with moderate effort.",
        characteristics: [
          "Athletic, muscular build naturally",
          "Efficient metabolism and energy levels",
          "Gains muscle and loses fat relatively easily",
          "Medium bone structure and frame",
          "Responds well to exercise",
          "Balanced body composition"
        ],
        recommendations: [
          "Combine strength training with cardio",
          "Eat a balanced diet with adequate protein",
          "Vary workout routines to prevent plateaus",
          "Monitor portion sizes to maintain weight",
          "Include both compound and isolation exercises",
          "Focus on progressive overload training"
        ]
      },
      endomorph: {
        description: "Endomorphs tend to have a larger, rounder physique and may struggle with weight management. They typically have a slower metabolism and store fat more easily, but can build muscle effectively.",
        characteristics: [
          "Larger, rounder physique naturally",
          "Slower metabolism - efficient at storing energy",
          "Gains weight easily, loses it slowly",
          "Wider hips and shoulders",
          "Higher body fat percentage tendency",
          "Strong and powerful when trained"
        ],
        recommendations: [
          "Emphasize cardio and high-intensity training",
          "Monitor calorie intake carefully",
          "Focus on lean proteins and vegetables",
          "Limit refined carbohydrates and sugars",
          "Stay consistent with exercise routine",
          "Consider intermittent fasting approaches"
        ]
      }
    };

    const primaryData = bodyTypeData[primaryType === "mixed" ? "mesomorph" : primaryType];

    return {
      type: primaryType,
      percentage: {
        ectomorph: ectomorphPercent,
        mesomorph: mesomorphPercent,
        endomorph: endomorphPercent,
      },
      description: primaryData.description,
      characteristics: primaryData.characteristics,
      recommendations: primaryData.recommendations,
    };
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsCalculating(true);
    
    // Simulate calculation time
    await new Promise(resolve => setTimeout(resolve, 1000));
    
    const bodyTypeResult = calculateBodyType();
    setResult(bodyTypeResult);
    setIsCalculating(false);
  };

  const resetCalculator = () => {
    setFormData({
      age: "",
      gender: "",
      height: "",
      weight: "",
      wristCircumference: "",
      shoulderWidth: "",
      waistSize: "",
      bodyFat: "",
      metabolismRate: "",
      muscleGain: "",
      weightGain: "",
    });
    setResult(null);
  };

  return (
    <div className={containerStyles.page}>
      <div className="container mx-auto px-4 py-8">
        <CalculatorHeader
          title="Body Type Calculator"
          description="Discover your body type (ectomorph, mesomorph, or endomorph) with our comprehensive calculator. Get personalized recommendations for diet and exercise based on your unique physique and metabolic characteristics."
          category="Health"
          className="text-center mb-12"
        />

        <div className="max-w-6xl mx-auto">
          {!result ? (
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
              {/* Input Section */}
              <div className={containerStyles.form}>
                <h2 className={cn(textStyles.h2, "mb-6")}>Body Type Assessment</h2>
                <form onSubmit={handleSubmit} className="space-y-6">
                  {/* Basic Information */}
                  <div>
                    <h3 className={cn(textStyles.h4, "mb-4")}>Basic Information</h3>
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                      <div>
                        <label htmlFor="age" className={cn(textStyles.label, "block mb-2")}>
                          Age *
                        </label>
                        <input
                          type="number"
                          id="age"
                          name="age"
                          value={formData.age}
                          onChange={handleInputChange}
                          required
                          min="10"
                          max="100"
                          className={inputStyles.base}
                          placeholder="Enter your age"
                        />
                      </div>

                      <div>
                        <label htmlFor="gender" className={cn(textStyles.label, "block mb-2")}>
                          Gender *
                        </label>
                        <select
                          id="gender"
                          name="gender"
                          value={formData.gender}
                          onChange={handleInputChange}
                          required
                          className={inputStyles.select}
                        >
                          <option value="">Select gender</option>
                          <option value="male">Male</option>
                          <option value="female">Female</option>
                        </select>
                      </div>
                    </div>
                  </div>

                  {/* Physical Measurements */}
                  <div>
                    <h3 className={cn(textStyles.h4, "mb-4")}>Physical Measurements</h3>
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                      <div>
                        <label htmlFor="height" className={cn(textStyles.label, "block mb-2")}>
                          Height (cm) *
                        </label>
                        <input
                          type="number"
                          id="height"
                          name="height"
                          value={formData.height}
                          onChange={handleInputChange}
                          required
                          min="100"
                          max="250"
                          className={inputStyles.base}
                          placeholder="Enter height"
                        />
                      </div>

                      <div>
                        <label htmlFor="weight" className={cn(textStyles.label, "block mb-2")}>
                          Weight (kg) *
                        </label>
                        <input
                          type="number"
                          id="weight"
                          name="weight"
                          value={formData.weight}
                          onChange={handleInputChange}
                          required
                          min="30"
                          max="300"
                          className={inputStyles.base}
                          placeholder="Enter weight"
                        />
                      </div>

                      <div>
                        <label htmlFor="wristCircumference" className={cn(textStyles.label, "block mb-2")}>
                          Wrist Circumference (cm)
                        </label>
                        <input
                          type="number"
                          id="wristCircumference"
                          name="wristCircumference"
                          value={formData.wristCircumference}
                          onChange={handleInputChange}
                          min="10"
                          max="30"
                          step="0.1"
                          className={inputStyles.base}
                          placeholder="Measure around wrist bone"
                        />
                      </div>

                      <div>
                        <label htmlFor="shoulderWidth" className={cn(textStyles.label, "block mb-2")}>
                          Shoulder Width (cm)
                        </label>
                        <input
                          type="number"
                          id="shoulderWidth"
                          name="shoulderWidth"
                          value={formData.shoulderWidth}
                          onChange={handleInputChange}
                          min="20"
                          max="80"
                          className={inputStyles.base}
                          placeholder="Across shoulder points"
                        />
                      </div>

                      <div>
                        <label htmlFor="waistSize" className={cn(textStyles.label, "block mb-2")}>
                          Waist Circumference (cm)
                        </label>
                        <input
                          type="number"
                          id="waistSize"
                          name="waistSize"
                          value={formData.waistSize}
                          onChange={handleInputChange}
                          min="40"
                          max="200"
                          className={inputStyles.base}
                          placeholder="At narrowest point"
                        />
                      </div>

                      <div>
                        <label htmlFor="bodyFat" className={cn(textStyles.label, "block mb-2")}>
                          Body Fat Percentage (%)
                        </label>
                        <input
                          type="number"
                          id="bodyFat"
                          name="bodyFat"
                          value={formData.bodyFat}
                          onChange={handleInputChange}
                          min="3"
                          max="50"
                          step="0.1"
                          className={inputStyles.base}
                          placeholder="If known"
                        />
                      </div>
                    </div>
                  </div>

                  {/* Metabolic Characteristics */}
                  <div>
                    <h3 className={cn(textStyles.h4, "mb-4")}>Metabolic Characteristics</h3>
                    <div className="space-y-4">
                      <div>
                        <label htmlFor="metabolismRate" className={cn(textStyles.label, "block mb-2")}>
                          How would you describe your metabolism?
                        </label>
                        <select
                          id="metabolismRate"
                          name="metabolismRate"
                          value={formData.metabolismRate}
                          onChange={handleInputChange}
                          className={inputStyles.select}
                        >
                          <option value="">Select metabolism rate</option>
                          <option value="fast">Fast - I can eat a lot without gaining weight</option>
                          <option value="normal">Normal - I maintain weight with moderate eating</option>
                          <option value="slow">Slow - I gain weight easily</option>
                        </select>
                      </div>

                      <div>
                        <label htmlFor="muscleGain" className={cn(textStyles.label, "block mb-2")}>
                          How easily do you gain muscle?
                        </label>
                        <select
                          id="muscleGain"
                          name="muscleGain"
                          value={formData.muscleGain}
                          onChange={handleInputChange}
                          className={inputStyles.select}
                        >
                          <option value="">Select muscle gain tendency</option>
                          <option value="difficult">Difficult - Very hard to build muscle</option>
                          <option value="normal">Normal - Moderate muscle growth with training</option>
                          <option value="easy">Easy - Build muscle relatively quickly</option>
                        </select>
                      </div>

                      <div>
                        <label htmlFor="weightGain" className={cn(textStyles.label, "block mb-2")}>
                          How easily do you gain weight?
                        </label>
                        <select
                          id="weightGain"
                          name="weightGain"
                          value={formData.weightGain}
                          onChange={handleInputChange}
                          className={inputStyles.select}
                        >
                          <option value="">Select weight gain tendency</option>
                          <option value="difficult">Difficult - Very hard to gain weight</option>
                          <option value="normal">Normal - Steady weight changes</option>
                          <option value="easy">Easy - Gain weight quickly</option>
                        </select>
                      </div>
                    </div>
                  </div>

                  <button
                    type="submit"
                    disabled={isCalculating}
                    className={buttonStyles.primaryLarge}
                  >
                    {isCalculating ? "Analyzing Your Body Type..." : "Calculate My Body Type"}
                  </button>
                </form>
              </div>

              {/* Information Section */}
              <div className="space-y-6">
                <div className={statusStyles.success.container}>
                  <h3 className={statusStyles.success.title}>💡 Measurement Tips</h3>
                  <ul className={cn("space-y-2", statusStyles.success.text)}>
                    <li><strong>Wrist:</strong> Measure around the wrist bone, just below the hand</li>
                    <li><strong>Shoulders:</strong> Measure across the back from shoulder point to shoulder point</li>
                    <li><strong>Waist:</strong> Measure at the narrowest part of your torso</li>
                    <li><strong>Body Fat:</strong> Use calipers, DEXA scan, or bioelectrical impedance</li>
                  </ul>
                </div>

                <div className={statusStyles.warning.container}>
                  <h3 className={statusStyles.warning.title}>🎯 What You&apos;ll Learn</h3>
                  <ul className={cn("space-y-2", statusStyles.warning.text)}>
                    <li>✓ Your primary body type classification</li>
                    <li>✓ Percentage breakdown of all three types</li>
                    <li>✓ Key physical and metabolic characteristics</li>
                    <li>✓ Personalized diet and exercise recommendations</li>
                    <li>✓ Tips for optimizing your fitness approach</li>
                  </ul>
                </div>

                <div className={statusStyles.purple.container}>
                  <h3 className={statusStyles.purple.title}>🔬 Science-Based Assessment</h3>
                  <p className={cn(statusStyles.purple.text, "mb-3")}>
                    Our calculator uses multiple factors including:
                  </p>
                  <ul className={cn("space-y-1", statusStyles.purple.text)}>
                    <li>• Frame size calculations</li>
                    <li>• Body composition analysis</li>
                    <li>• Metabolic rate indicators</li>
                    <li>• Physical proportions</li>
                    <li>• Self-reported characteristics</li>
                  </ul>
                </div>
              </div>
            </div>
          ) : (
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
              {/* Results Summary */}
              <div className={containerStyles.form}>
                <div className="text-center mb-6">
                  <h2 className={cn(textStyles.h1, "text-3xl mb-4")}>
                    Your Body Type: {result.type.charAt(0).toUpperCase() + result.type.slice(1)}
                  </h2>
                  
                  <div className="flex justify-center space-x-6 mb-6">
                    <div className="text-center">
                      <div className="text-2xl font-bold text-blue-600">{result.percentage.ectomorph}%</div>
                      <div className={textStyles.bodySmall}>Ectomorph</div>
                    </div>
                    <div className="text-center">
                      <div className="text-2xl font-bold text-green-600">{result.percentage.mesomorph}%</div>
                      <div className={textStyles.bodySmall}>Mesomorph</div>
                    </div>
                    <div className="text-center">
                      <div className="text-2xl font-bold text-orange-600">{result.percentage.endomorph}%</div>
                      <div className={textStyles.bodySmall}>Endomorph</div>
                    </div>
                  </div>

                  <p className={cn(textStyles.body, "text-lg")}>
                    {result.description}
                  </p>
                </div>

                <button
                  onClick={resetCalculator}
                  className={buttonStyles.secondaryLarge}
                >
                  Take Assessment Again
                </button>
              </div>

              {/* Detailed Results */}
              <div className="space-y-6">
                <div className={cn(containerStyles.cardSmall, borderStyles.left.blue, "p-6")}>
                  <h3 className={cn(textStyles.h3, "mb-4")}>🧬 Key Characteristics</h3>
                  <ul className="space-y-2">
                    {result.characteristics.map((characteristic, index) => (
                      <li key={index} className="flex items-start">
                        <svg className="w-5 h-5 text-green-500 mt-0.5 mr-3 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                        </svg>
                        <span className={textStyles.body}>{characteristic}</span>
                      </li>
                    ))}
                  </ul>
                </div>

                <div className={cn(containerStyles.cardSmall, borderStyles.left.green, "p-6")}>
                  <h3 className={cn(textStyles.h3, "mb-4")}>💪 Personalized Recommendations</h3>
                  <ul className="space-y-2">
                    {result.recommendations.map((recommendation, index) => (
                      <li key={index} className="flex items-start">
                        <svg className="w-5 h-5 text-blue-500 mt-0.5 mr-3 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                        </svg>
                        <span className={textStyles.body}>{recommendation}</span>
                      </li>
                    ))}
                  </ul>
                </div>

                <div className={statusStyles.danger.container}>
                  <h3 className={cn(statusStyles.danger.title, "text-lg mb-3")}>⚠️ Important Note</h3>
                  <p className={statusStyles.danger.textSmall}>
                    Body types exist on a spectrum, and most people are a combination of types. 
                    Use these results as general guidelines and always consult with healthcare 
                    professionals for personalized advice about diet and exercise.
                  </p>
                </div>
              </div>
            </div>
          )}
        </div>

        {/* Understanding Body Types Section */}
        {!result && (
          <div className="max-w-6xl mx-auto mt-12 mb-12">
            <div className={statusStyles.info.container}>
              <h2 className={cn(statusStyles.info.title, "text-2xl mb-4")}>Understanding Body Types</h2>
              <p className={cn(statusStyles.info.text, "mb-4")}>
                Body types, also known as somatotypes, were first introduced by psychologist William Sheldon in the 1940s. 
                While genetics play a significant role, understanding your body type can help you optimize your fitness and nutrition approach.
              </p>
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                <div className={cn(containerStyles.infoCard, "p-4")}>
                  <h3 className="font-semibold text-blue-800 dark:text-blue-300 mb-2">🏃‍♂️ Ectomorph</h3>
                  <p className={textStyles.bodySmall}>Naturally lean, fast metabolism, difficulty gaining weight</p>
                </div>
                <div className={cn(containerStyles.infoCard, "p-4")}>
                  <h3 className="font-semibold text-green-800 dark:text-green-300 mb-2">💪 Mesomorph</h3>
                  <p className={textStyles.bodySmall}>Athletic build, gains muscle easily, balanced metabolism</p>
                </div>
                <div className={cn(containerStyles.infoCard, "p-4")}>
                  <h3 className="font-semibold text-orange-800 dark:text-orange-300 mb-2">🏋️‍♀️ Endomorph</h3>
                  <p className={textStyles.bodySmall}>Rounder physique, slower metabolism, gains weight easily</p>
                </div>
              </div>
            </div>
          </div>
        )}

        {/* Additional Information */}
        {!result && (
          <div className="max-w-6xl mx-auto mt-12">
            <div className={cn(containerStyles.infoCard, "p-8")}>
              <h2 className={cn(textStyles.h2, "mb-6 text-center")}>Frequently Asked Questions</h2>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                  <h3 className={cn(textStyles.h4, "mb-2")}>Can my body type change?</h3>
                  <p className={textStyles.bodySmall}>While your genetic predisposition remains constant, your body composition can change through diet, exercise, and lifestyle modifications.</p>
                </div>
                <div>
                  <h3 className={cn(textStyles.h4, "mb-2")}>Are body types scientifically valid?</h3>
                  <p className={textStyles.bodySmall}>While the original theory has limitations, understanding your body&apos;s tendencies can help guide your fitness and nutrition approach.</p>
                </div>
                <div>
                  <h3 className={cn(textStyles.h4, "mb-2")}>What if I&apos;m a combination?</h3>
                  <p className={textStyles.bodySmall}>Most people are combinations of body types. Our calculator shows your percentage breakdown to reflect this reality.</p>
                </div>
                <div>
                  <h3 className={cn(textStyles.h4, "mb-2")}>How accurate are the results?</h3>
                  <p className={textStyles.bodySmall}>Results are estimates based on multiple factors. Use them as guidelines alongside professional advice from trainers and nutritionists.</p>
                </div>
              </div>
            </div>
          </div>
        )}
      </div>
      <Footer />
    </div>
  );
}