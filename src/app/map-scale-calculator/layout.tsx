import { generatePageTitle, generateCanonicalUrl } from '@/lib/utils';

export const metadata = {
  title: generatePageTitle("Map Scale Calculator"),
  description: "Free online map scale calculator for converting between map distances and real distances. Perfect for cartographers, geographers, hikers, and navigation enthusiasts working with topographic maps, road maps, and nautical charts.",
  keywords: "map scale calculator, map distance, scale conversion, cartography, geography, navigation, topographic maps, road maps, nautical charts, map measurement, hiking maps, GPS navigation",
  robots: "index, follow",
  alternates: {
    canonical: generateCanonicalUrl("/map-scale-calculator"),
  },
  openGraph: {
    title: "Map Scale Calculator - Calculate Map Distances and Scales",
    description: "Free online map scale calculator for converting between map distances and real distances.",
    type: 'website',
    url: generateCanonicalUrl("/map-scale-calculator"),
  },
  twitter: {
    card: 'summary_large_image',
    title: "Map Scale Calculator - Calculate Map Distances and Scales",
    description: "Free online map scale calculator for converting between map distances and real distances.",
  },
};

export default function MapScaleCalculatorLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  return children;
}