'use client';

import { useState, useEffect, useCallback } from 'react';
import Footer from "@/components/Footer";
import CalculatorHeader from '@/components/CalculatorHeader';
import { containerStyles } from '@/components/ui/styles';

interface MapScaleResult {
  mapDistance: number;
  realDistance: number;
  scale: string;
  mapUnit: string;
  realUnit: string;
}

export default function MapScaleCalculator() {
  const [mapDistance, setMapDistance] = useState<string>('10');
  const [realDistance, setRealDistance] = useState<string>('');
  const [mapScale, setMapScale] = useState<string>('1:50000');
  const [mapUnit, setMapUnit] = useState<string>('cm');
  const [realUnit, setRealUnit] = useState<string>('m');
  const [result, setResult] = useState<MapScaleResult | null>(null);

  const units = [
    { value: 'mm', label: 'Millimeter (mm)' },
    { value: 'cm', label: 'Centimeter (cm)' },
    { value: 'm', label: 'Meter (m)' },
    { value: 'km', label: 'Kilometer (km)' },
    { value: 'in', label: 'Inch (in)' },
    { value: 'ft', label: 'Foot (ft)' },
    { value: 'yd', label: 'Yard (yd)' },
    { value: 'mi', label: 'Mile (mi)' }
  ];

  const commonMapScales = [
    { scale: '1:1000', label: '1:1,000 (City blocks)' },
    { scale: '1:2500', label: '1:2,500 (Detailed city)' },
    { scale: '1:5000', label: '1:5,000 (City districts)' },
    { scale: '1:10000', label: '1:10,000 (Town maps)' },
    { scale: '1:25000', label: '1:25,000 (Topographic)' },
    { scale: '1:50000', label: '1:50,000 (Regional)' },
    { scale: '1:100000', label: '1:100,000 (Road maps)' },
    { scale: '1:250000', label: '1:250,000 (State maps)' },
    { scale: '1:500000', label: '1:500,000 (Country maps)' }
  ];

  const parseScale = (scale: string): number => {
    const parts = scale.split(':');
    if (parts.length === 2) {
      const numerator = parseFloat(parts[0]);
      const denominator = parseFloat(parts[1]);
      return denominator / numerator;
    }
    return 1;
  };

  const convertToMM = (value: number, fromUnit: string): number => {
    const conversions: { [key: string]: number } = {
      'mm': 1,
      'cm': 10,
      'm': 1000,
      'km': 1000000,
      'in': 25.4,
      'ft': 304.8,
      'yd': 914.4,
      'mi': 1609344
    };
    return value * conversions[fromUnit];
  };

  const convertFromMM = (value: number, toUnit: string): number => {
    const conversions: { [key: string]: number } = {
      'mm': 1,
      'cm': 10,
      'm': 1000,
      'km': 1000000,
      'in': 25.4,
      'ft': 304.8,
      'yd': 914.4,
      'mi': 1609344
    };
    return value / conversions[toUnit];
  };

  const calculateMapScale = useCallback(() => {
    if (!mapDistance && !realDistance) {
      setResult(null);
      return;
    }

    const mapScaleRatio = parseScale(mapScale);
    let calculatedResult: MapScaleResult;

    if (mapDistance && !realDistance) {
      // Calculate real distance from map distance
      const mapDistanceNum = parseFloat(mapDistance);
      const mapDistanceMM = convertToMM(mapDistanceNum, mapUnit);
      const realDistanceMM = mapDistanceMM * mapScaleRatio;
      const realDistanceConverted = convertFromMM(realDistanceMM, realUnit);
      
      calculatedResult = {
        mapDistance: mapDistanceNum,
        realDistance: parseFloat(realDistanceConverted.toFixed(4)),
        scale: mapScale,
        mapUnit: mapUnit,
        realUnit: realUnit
      };
      setRealDistance(realDistanceConverted.toFixed(4));
    } else if (realDistance && !mapDistance) {
      // Calculate map distance from real distance
      const realDistanceNum = parseFloat(realDistance);
      const realDistanceMM = convertToMM(realDistanceNum, realUnit);
      const mapDistanceMM = realDistanceMM / mapScaleRatio;
      const mapDistanceConverted = convertFromMM(mapDistanceMM, mapUnit);
      
      calculatedResult = {
        mapDistance: parseFloat(mapDistanceConverted.toFixed(4)),
        realDistance: realDistanceNum,
        scale: mapScale,
        mapUnit: mapUnit,
        realUnit: realUnit
      };
      setMapDistance(mapDistanceConverted.toFixed(4));
    } else {
      // Both values provided, calculate scale
      const mapDistanceNum = parseFloat(mapDistance);
      const realDistanceNum = parseFloat(realDistance);
      const mapDistanceMM = convertToMM(mapDistanceNum, mapUnit);
      const realDistanceMM = convertToMM(realDistanceNum, realUnit);
      const ratio = realDistanceMM / mapDistanceMM;
      const calculatedScale = `1:${Math.round(ratio)}`;
      
      calculatedResult = {
        mapDistance: mapDistanceNum,
        realDistance: realDistanceNum,
        scale: calculatedScale,
        mapUnit: mapUnit,
        realUnit: realUnit
      };
      setMapScale(calculatedScale);
    }

    setResult(calculatedResult);
  }, [mapDistance, realDistance, mapScale, mapUnit, realUnit]);

  useEffect(() => {
    calculateMapScale();
  }, [calculateMapScale]);

  const clearInputs = () => {
    setMapDistance('');
    setRealDistance('');
    setResult(null);
  };

  return (
    <>
      <script
        type="application/ld+json"
        dangerouslySetInnerHTML={{
          __html: JSON.stringify({
            "@context": "https://schema.org",
            "@type": "WebApplication",
            "name": "Map Scale Calculator",
            "description": "Free online map scale calculator for converting between map distances and real distances. Perfect for cartographers, geographers, hikers, and navigation enthusiasts.",
            "applicationCategory": "UtilityApplication",
            "operatingSystem": "Web Browser",
            "url": "https://calc9.com/map-scale-calculator",
            "offers": {
              "@type": "Offer",
              "price": "0",
              "priceCurrency": "USD"
            },
            "featureList": [
              "Convert map distance to real distance",
              "Convert real distance to map distance",
              "Calculate map scales",
              "Support for 8 different units",
              "Common map scales presets",
              "Real-time calculations"
            ],
            "keywords": "map scale calculator, map distance, cartography, geography, navigation, topographic maps, road maps"
          })
        }}
      />
      <div className={containerStyles.page}>
        <div className="container mx-auto px-4">
          <div className="max-w-6xl mx-auto">
            <CalculatorHeader
              title="Map Scale Calculator"
              description="Calculate real distances from map measurements or determine map scales. Perfect for cartographers, geographers, hikers, and navigation enthusiasts."
              category="Math"
              className="text-center mb-12"
            />

            <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
              {/* Calculator Section */}
              <div className="bg-white dark:bg-gray-800 rounded-lg shadow-lg dark:shadow-gray-700/50 p-6 border border-gray-200 dark:border-gray-700">
                <h2 className="text-2xl font-semibold text-gray-900 dark:text-white mb-6">Map Scale Calculation</h2>

                <div className="space-y-6">
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                      <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                        Map Distance
                      </label>
                      <input
                        type="number"
                        value={mapDistance}
                        onChange={(e) => setMapDistance(e.target.value)}
                        placeholder="Enter map distance"
                        className="w-full px-3 py-2 bg-white dark:bg-gray-700 border border-gray-300 dark:border-gray-600 rounded-md text-gray-900 dark:text-white placeholder-gray-500 dark:placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-blue-500 dark:focus:ring-blue-400 focus:border-blue-500 dark:focus:border-blue-400 transition-colors"
                      />
                    </div>
                    <div>
                      <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                        Real Distance
                      </label>
                      <input
                        type="number"
                        value={realDistance}
                        onChange={(e) => setRealDistance(e.target.value)}
                        placeholder="Enter real distance"
                        className="w-full px-3 py-2 bg-white dark:bg-gray-700 border border-gray-300 dark:border-gray-600 rounded-md text-gray-900 dark:text-white placeholder-gray-500 dark:placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-blue-500 dark:focus:ring-blue-400 focus:border-blue-500 dark:focus:border-blue-400 transition-colors"
                      />
                    </div>
                  </div>

                  <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                    <div>
                      <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                        Map Scale
                      </label>
                      <input
                        type="text"
                        value={mapScale}
                        onChange={(e) => setMapScale(e.target.value)}
                        placeholder="1:50000"
                        className="w-full px-3 py-2 bg-white dark:bg-gray-700 border border-gray-300 dark:border-gray-600 rounded-md text-gray-900 dark:text-white placeholder-gray-500 dark:placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-blue-500 dark:focus:ring-blue-400 focus:border-blue-500 dark:focus:border-blue-400 transition-colors"
                      />
                    </div>
                    <div>
                      <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                        Map Unit
                      </label>
                      <select
                        value={mapUnit}
                        onChange={(e) => setMapUnit(e.target.value)}
                        className="w-full px-3 py-2 bg-white dark:bg-gray-700 border border-gray-300 dark:border-gray-600 rounded-md text-gray-900 dark:text-white focus:outline-none focus:ring-2 focus:ring-blue-500 dark:focus:ring-blue-400 focus:border-blue-500 dark:focus:border-blue-400 transition-colors"
                      >
                        {units.map(u => (
                          <option key={u.value} value={u.value}>{u.label}</option>
                        ))}
                      </select>
                    </div>
                    <div>
                      <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                        Real Unit
                      </label>
                      <select
                        value={realUnit}
                        onChange={(e) => setRealUnit(e.target.value)}
                        className="w-full px-3 py-2 bg-white dark:bg-gray-700 border border-gray-300 dark:border-gray-600 rounded-md text-gray-900 dark:text-white focus:outline-none focus:ring-2 focus:ring-blue-500 dark:focus:ring-blue-400 focus:border-blue-500 dark:focus:border-blue-400 transition-colors"
                      >
                        {units.map(u => (
                          <option key={u.value} value={u.value}>{u.label}</option>
                        ))}
                      </select>
                    </div>
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                      Common Map Scales
                    </label>
                    <div className="grid grid-cols-2 md:grid-cols-3 gap-2">
                      {commonMapScales.map(item => (
                        <button
                          key={item.scale}
                          onClick={() => {
                            setMapScale(item.scale);
                            // Clear real distance to prevent auto-recalculation of scale
                            setRealDistance('');
                          }}
                          className="px-2 py-1 bg-gray-200 dark:bg-gray-700 text-gray-700 dark:text-gray-300 rounded-md hover:bg-gray-300 dark:hover:bg-gray-600 transition-colors text-xs text-left border border-gray-300 dark:border-gray-600"
                          title={item.label}
                        >
                          {item.scale}
                        </button>
                      ))}
                    </div>
                  </div>

                  <button
                    onClick={clearInputs}
                    className="w-full bg-gray-600 dark:bg-gray-700 hover:bg-gray-700 dark:hover:bg-gray-600 text-white font-medium py-2 px-4 rounded-md transition-colors"
                  >
                    Clear All
                  </button>
                </div>
              </div>

              {/* Results and Information Section */}
              <div className="space-y-6">
                {result && (
                  <div className="bg-white dark:bg-gray-800 rounded-lg shadow-lg dark:shadow-gray-700/50 p-6 border-l-4 border-green-500 dark:border-green-400 border border-gray-200 dark:border-gray-700">
                    <h3 className="text-xl font-semibold text-gray-900 dark:text-white mb-4">
                      🗺️ Map Scale Result
                    </h3>
                    <div className="space-y-3">
                      <div className="flex justify-between items-center p-3 bg-gray-50 dark:bg-gray-700 rounded-lg border border-gray-200 dark:border-gray-600">
                        <span className="text-gray-600 dark:text-gray-300">Map Distance:</span>
                        <span className="font-medium text-lg text-gray-900 dark:text-white">{result.mapDistance} {result.mapUnit}</span>
                      </div>
                      <div className="flex justify-between items-center p-3 bg-gray-50 dark:bg-gray-700 rounded-lg border border-gray-200 dark:border-gray-600">
                        <span className="text-gray-600 dark:text-gray-300">Real Distance:</span>
                        <span className="font-medium text-lg text-gray-900 dark:text-white">{result.realDistance} {result.realUnit}</span>
                      </div>
                      <div className="flex justify-between items-center p-3 bg-green-50 dark:bg-green-900/50 rounded-lg border border-green-200 dark:border-green-700">
                        <span className="text-gray-600 dark:text-gray-300">Map Scale:</span>
                        <span className="font-bold text-lg text-green-600 dark:text-green-300">{result.scale}</span>
                      </div>
                    </div>
                  </div>
                )}

                {/* Map Scale Information */}
                <div className="bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-700 rounded-lg p-6">
                  <h3 className="text-xl font-semibold text-blue-800 dark:text-blue-200 mb-4">🗺️ Understanding Map Scales</h3>
                  <div className="space-y-3 text-blue-700 dark:text-blue-300">
                    <p className="text-sm">
                      <strong>Map Scale:</strong> A scale of 1:50,000 means 1 unit on the map represents 50,000 units in reality.
                    </p>
                    <p className="text-sm">
                      <strong>Large Scale:</strong> 1:25,000 or smaller ratios show more detail but cover less area.
                    </p>
                    <p className="text-sm">
                      <strong>Small Scale:</strong> 1:500,000 or larger ratios show less detail but cover more area.
                    </p>
                    <p className="text-sm">
                      <strong>Representative Fraction:</strong> The scale ratio can also be written as a fraction (1/50,000).
                    </p>
                  </div>
                </div>

                {/* How to Use */}
                <div className="bg-green-50 dark:bg-green-900/20 border border-green-200 dark:border-green-700 rounded-lg p-6">
                  <h3 className="text-xl font-semibold text-green-800 dark:text-green-200 mb-4">❓ How to Use</h3>
                  <div className="space-y-2 text-green-700 dark:text-green-300">
                    <p className="text-sm">• Measure distance on map with a ruler</p>
                    <p className="text-sm">• Enter map distance and scale to find real distance</p>
                    <p className="text-sm">• Or enter real distance to find map distance</p>
                    <p className="text-sm">• Choose appropriate units for accuracy</p>
                    <p className="text-sm">• Use preset scales for common map types</p>
                  </div>
                </div>

                {/* Map Types */}
                <div className="bg-purple-50 dark:bg-purple-900/20 border border-purple-200 dark:border-purple-700 rounded-lg p-6">
                  <h3 className="text-xl font-semibold text-purple-800 dark:text-purple-200 mb-4">📋 Map Types & Scales</h3>
                  <div className="text-purple-700 dark:text-purple-300 space-y-2">
                    <p className="text-sm"><strong>City Maps:</strong> 1:1,000 to 1:10,000 for detailed navigation</p>
                    <p className="text-sm"><strong>Topographic Maps:</strong> 1:25,000 to 1:100,000 for hiking</p>
                    <p className="text-sm"><strong>Road Maps:</strong> 1:100,000 to 1:500,000 for driving</p>
                    <p className="text-sm"><strong>Nautical Charts:</strong> Various scales for marine navigation</p>
                    <p className="text-sm"><strong>Aviation Charts:</strong> Specialized scales for flight planning</p>
                  </div>
                </div>

                {/* Examples */}
                <div className="bg-yellow-50 dark:bg-yellow-900/20 border border-yellow-200 dark:border-yellow-700 rounded-lg p-6">
                  <h3 className="text-xl font-semibold text-yellow-800 dark:text-yellow-200 mb-4">💡 Practical Examples</h3>
                  <div className="text-yellow-700 dark:text-yellow-300 space-y-2">
                    <p className="text-sm"><strong>Hiking:</strong> 2cm on 1:50,000 map = 1km real distance</p>
                    <p className="text-sm"><strong>City Navigation:</strong> 1cm on 1:10,000 map = 100m real distance</p>
                    <p className="text-sm"><strong>Road Trip:</strong> 1inch on 1:250,000 map ≈ 4 miles real distance</p>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
        <Footer />
      </div>
    </>
  );
}