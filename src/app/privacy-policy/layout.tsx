import { generatePageTitle, generateCanonicalUrl } from '@/lib/utils';

export const metadata = {
  title: generatePageTitle("Privacy Policy"),
  description: "Read our privacy policy to understand how we collect, use, and protect your personal information when using our calculator tools.",
  keywords: "privacy policy, data protection, user privacy, terms of use",
  robots: "index, follow",
  alternates: {
    canonical: generateCanonicalUrl("/privacy-policy"),
  },
};

export default function PrivacyPolicyLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  return children;
}