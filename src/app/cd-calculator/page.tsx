'use client';

import { useState } from 'react';
import Footer from '@/components/Footer';
import CalculatorHeader from '@/components/CalculatorHeader';
import { containerStyles, textStyles, inputStyles, buttonStyles, statusStyles, cn } from '@/components/ui/styles';

interface CDForm {
  initialDeposit: string;
  termLength: string;
  termUnit: 'months' | 'years';
  apy: string;
  compoundingFrequency: 'daily' | 'monthly' | 'quarterly' | 'annually';
}

interface CDResult {
  finalAmount: number;
  totalInterest: number;
  effectiveYield: number;
  monthlyBreakdown: Array<{
    month: number;
    balance: number;
    interestEarned: number;
    cumulativeInterest: number;
  }>;
}

export default function CDCalculator() {
  const [form, setForm] = useState<CDForm>({
    initialDeposit: '',
    termLength: '',
    termUnit: 'years',
    apy: '',
    compoundingFrequency: 'monthly'
  });

  const [result, setResult] = useState<CDResult | null>(null);
  const [isCalculating, setIsCalculating] = useState(false);

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement>) => {
    const { name, value } = e.target;
    setForm(prev => ({ ...prev, [name]: value }));
  };

  const calculateCD = () => {
    setIsCalculating(true);
    
    const principal = parseFloat(form.initialDeposit);
    const annualRate = parseFloat(form.apy) / 100;
    const termInYears = form.termUnit === 'years' 
      ? parseFloat(form.termLength)
      : parseFloat(form.termLength) / 12;

    // Compounding frequency
    const compoundingFreq = {
      daily: 365,
      monthly: 12,
      quarterly: 4,
      annually: 1
    }[form.compoundingFrequency];

    // Calculate final amount using compound interest formula
    // A = P(1 + r/n)^(nt)
    const finalAmount = principal * Math.pow(1 + annualRate / compoundingFreq, compoundingFreq * termInYears);
    const totalInterest = finalAmount - principal;
    const effectiveYield = (totalInterest / principal) * 100;

    // Generate monthly breakdown
    const monthlyBreakdown: CDResult['monthlyBreakdown'] = [];
    const totalMonths = Math.ceil(termInYears * 12);
    
    for (let month = 1; month <= totalMonths; month++) {
      const timeInYears = month / 12;
      const balance = principal * Math.pow(1 + annualRate / compoundingFreq, compoundingFreq * timeInYears);
      const cumulativeInterest = balance - principal;
      const monthlyInterest = month === 1 
        ? cumulativeInterest 
        : cumulativeInterest - monthlyBreakdown[month - 2].cumulativeInterest;

      monthlyBreakdown.push({
        month,
        balance: Math.round(balance * 100) / 100,
        interestEarned: Math.round(monthlyInterest * 100) / 100,
        cumulativeInterest: Math.round(cumulativeInterest * 100) / 100
      });
    }

    const cdResult: CDResult = {
      finalAmount: Math.round(finalAmount * 100) / 100,
      totalInterest: Math.round(totalInterest * 100) / 100,
      effectiveYield: Math.round(effectiveYield * 100) / 100,
      monthlyBreakdown
    };

    setTimeout(() => {
      setResult(cdResult);
      setIsCalculating(false);
    }, 500);
  };

  const resetForm = () => {
    setForm({
      initialDeposit: '',
      termLength: '',
      termUnit: 'years',
      apy: '',
      compoundingFrequency: 'monthly'
    });
    setResult(null);
  };

  const isFormValid = form.initialDeposit && form.termLength && form.apy;

  return (
    <>
      <div className={containerStyles.page}>
        <div className="container mx-auto px-4 py-8">
          <div className="max-w-4xl mx-auto">
            <CalculatorHeader
              title="CD Calculator"
              description="Calculate how much you could earn from a Certificate of Deposit. Enter your initial deposit, CD term, and APY to see your potential earnings at maturity."
              category="Financial"
            />

            <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
              {/* Input Form */}
              <div className={containerStyles.form}>
                <h2 className={cn(textStyles.h2, "mb-6")}>CD Details</h2>
                
                <div className="space-y-4">
                  {/* Initial Deposit */}
                  <div>
                    <label className={cn(textStyles.label, "block mb-2")}>
                      Initial Deposit ($) *
                    </label>
                    <input
                      type="number"
                      name="initialDeposit"
                      value={form.initialDeposit}
                      onChange={handleInputChange}
                      min="0"
                      step="0.01"
                      className={inputStyles.base}
                      placeholder="Enter your initial deposit"
                    />
                  </div>

                  {/* Term Length */}
                  <div>
                    <label className={cn(textStyles.label, "block mb-2")}>
                      CD Term *
                    </label>
                    <div className="grid grid-cols-3 gap-2">
                      <input
                        type="number"
                        name="termLength"
                        value={form.termLength}
                        onChange={handleInputChange}
                        min="1"
                        step="0.1"
                        className={cn("col-span-2", inputStyles.base)}
                        placeholder="Enter term length"
                      />
                      <select
                        name="termUnit"
                        value={form.termUnit}
                        onChange={handleInputChange}
                        className={inputStyles.select}
                      >
                        <option value="months">Months</option>
                        <option value="years">Years</option>
                      </select>
                    </div>
                  </div>

                  {/* APY */}
                  <div>
                    <label className={cn(textStyles.label, "block mb-2")}>
                      Annual Percentage Yield (APY) % *
                    </label>
                    <input
                      type="number"
                      name="apy"
                      value={form.apy}
                      onChange={handleInputChange}
                      min="0"
                      max="20"
                      step="0.01"
                      className={inputStyles.base}
                      placeholder="Enter APY (e.g., 4.5)"
                    />
                  </div>

                  {/* Compounding Frequency */}
                  <div>
                    <label className={cn(textStyles.label, "block mb-2")}>
                      Compounding Frequency
                    </label>
                    <select
                      name="compoundingFrequency"
                      value={form.compoundingFrequency}
                      onChange={handleInputChange}
                      className={inputStyles.select}
                    >
                      <option value="daily">Daily</option>
                      <option value="monthly">Monthly</option>
                      <option value="quarterly">Quarterly</option>
                      <option value="annually">Annually</option>
                    </select>
                  </div>

                  {/* Buttons */}
                  <div className="grid grid-cols-3 gap-4 pt-4">
                    <button
                      onClick={calculateCD}
                      disabled={!isFormValid || isCalculating}
                      className={cn("col-span-2", buttonStyles.primaryLarge)}
                    >
                      {isCalculating ? 'Calculating...' : 'Calculate CD'}
                    </button>
                    <button
                      onClick={resetForm}
                      className={buttonStyles.secondaryLarge}
                    >
                      Reset
                    </button>
                  </div>
                </div>
              </div>

              {/* Results */}
              <div className={containerStyles.form}>
                <h2 className={cn(textStyles.h2, "mb-6")}>Your CD Results</h2>
                
                {!result ? (
                  <div className="text-center py-8">
                    <div className="w-16 h-16 bg-gray-200 dark:bg-gray-600 rounded-full flex items-center justify-center mx-auto mb-4">
                      <svg className={cn("w-8 h-8", textStyles.muted)} fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1" />
                      </svg>
                    </div>
                    <p className={textStyles.muted}>Enter your CD details to see potential earnings at maturity.</p>
                  </div>
                ) : (
                  <div className="space-y-6">
                    {/* Summary */}
                    <div className="grid grid-cols-1 gap-4">
                      <div className={containerStyles.cardSmall + " p-4"}>
                        <div className={cn("mb-1", textStyles.bodySmall)}>Final Amount at Maturity</div>
                        <div className="text-2xl font-bold text-green-600 dark:text-green-400">${result.finalAmount.toLocaleString()}</div>
                      </div>
                      
                      <div className={containerStyles.cardSmall + " p-4"}>
                        <div className={cn("mb-1", textStyles.bodySmall)}>Total Interest Earned</div>
                        <div className="text-xl font-bold text-blue-600 dark:text-blue-400">${result.totalInterest.toLocaleString()}</div>
                      </div>

                      <div className={containerStyles.cardSmall + " p-4"}>
                        <div className={cn("mb-1", textStyles.bodySmall)}>Effective Yield</div>
                        <div className="text-xl font-bold text-purple-600 dark:text-purple-400">{result.effectiveYield}%</div>
                      </div>
                    </div>

                    {/* Breakdown Chart */}
                    <div>
                      <h3 className={cn(textStyles.h3, "mb-4")}>Growth Over Time</h3>
                      <div className={containerStyles.cardSmall + " p-4"}>
                        <div className="space-y-2 max-h-64 overflow-y-auto">
                          {result.monthlyBreakdown.filter((_, index) => 
                            index === 0 || 
                            index === result.monthlyBreakdown.length - 1 || 
                            (index + 1) % 3 === 0 || 
                            (index + 1) % 6 === 0 || 
                            (index + 1) % 12 === 0
                          ).map((month) => (
                            <div key={month.month} className="flex justify-between items-center py-2 border-b border-gray-100 dark:border-gray-600">
                              <span className={cn("text-sm font-medium", textStyles.body)}>
                                Month {month.month}
                              </span>
                              <div className="text-right">
                                <div className={cn("font-semibold", textStyles.body)}>${month.balance.toLocaleString()}</div>
                                <div className="text-xs text-green-600 dark:text-green-400">
                                  +${month.cumulativeInterest.toLocaleString()} interest
                                </div>
                              </div>
                            </div>
                          ))}
                        </div>
                      </div>
                    </div>

                    {/* Key Facts */}
                    <div className={containerStyles.cardSmall + " p-4"}>
                      <h3 className={cn("font-semibold mb-3", textStyles.h4)}>CD Summary</h3>
                      <div className={cn("space-y-2 text-sm", textStyles.body)}>
                        <div className="flex justify-between">
                          <span>Initial Deposit:</span>
                          <span className="font-semibold">${parseFloat(form.initialDeposit).toLocaleString()}</span>
                        </div>
                        <div className="flex justify-between">
                          <span>Term:</span>
                          <span className="font-semibold">{form.termLength} {form.termUnit}</span>
                        </div>
                        <div className="flex justify-between">
                          <span>APY:</span>
                          <span className="font-semibold">{form.apy}%</span>
                        </div>
                        <div className="flex justify-between">
                          <span>Compounding:</span>
                          <span className="font-semibold capitalize">{form.compoundingFrequency}</span>
                        </div>
                      </div>
                    </div>
                  </div>
                )}
              </div>
            </div>

            {/* Information */}
            <div className={cn("mt-8", statusStyles.info.container)}>
              <h3 className={statusStyles.info.title}>About Certificates of Deposit</h3>
              <ul className={cn(statusStyles.info.text, "space-y-1 text-sm")}>
                <li>• CDs are time deposits with fixed terms and interest rates</li>
                <li>• Early withdrawal typically results in penalties</li>
                <li>• FDIC insured up to $250,000 per depositor, per bank</li>
                <li>• Higher rates often require larger minimum deposits</li>
                <li>• Consider laddering CDs for better liquidity and rate opportunities</li>
              </ul>
            </div>
          </div>
        </div>
        <Footer />
      </div>
    </>
  );
}