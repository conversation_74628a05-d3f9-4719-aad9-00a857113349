'use client';

import { useState, useEffect, useCallback, useMemo, Suspense } from 'react';
import { useRouter, useSearchParams } from 'next/navigation';
import Footer from "@/components/Footer";
import CalculatorHeader from '@/components/CalculatorHeader';
import { containerStyles } from '@/components/ui/styles';

interface PPIResult {
  ppi: number;
  dpi: number;
  horizontalResolution: number;
  verticalResolution: number;
  diagonalSize: number;
  pixelWidth: number;
  pixelHeight: number;
  totalPixels: number;
  aspectRatio: string;
}

interface CommonDevice {
  name: string;
  width: number;
  height: number;
  diagonal: number;
  category: string;
}

function PPICalculatorContent() {
  const router = useRouter();
  const searchParams = useSearchParams();
  
  const [horizontalRes, setHorizontalRes] = useState<string>('');
  const [verticalRes, setVerticalRes] = useState<string>('');
  const [diagonalSize, setDiagonalSize] = useState<string>('');
  const [unit, setUnit] = useState<string>('inches');
  const [result, setResult] = useState<PPIResult | null>(null);
  const [isInitialized, setIsInitialized] = useState<boolean>(false);

  const units = useMemo(() => [
    { value: 'inches', label: 'Inches (in)', conversionFactor: 1 },
    { value: 'cm', label: 'Centimeters (cm)', conversionFactor: 2.54 },
    { value: 'mm', label: 'Millimeters (mm)', conversionFactor: 25.4 }
  ], []);

  const commonDevices: CommonDevice[] = [
    // Smartphones
    { name: 'iPhone 15 Pro Max', width: 1290, height: 2796, diagonal: 6.7, category: 'Smartphone' },
    { name: 'iPhone 15 Pro', width: 1179, height: 2556, diagonal: 6.1, category: 'Smartphone' },
    { name: 'Samsung Galaxy S24 Ultra', width: 1440, height: 3120, diagonal: 6.8, category: 'Smartphone' },
    { name: 'Google Pixel 8 Pro', width: 1344, height: 2992, diagonal: 6.7, category: 'Smartphone' },
    
    // Tablets
    { name: 'iPad Pro 12.9"', width: 2048, height: 2732, diagonal: 12.9, category: 'Tablet' },
    { name: 'iPad Air 11"', width: 1640, height: 2360, diagonal: 11, category: 'Tablet' },
    { name: 'Surface Pro 9', width: 1920, height: 1280, diagonal: 13, category: 'Tablet' },
    
    // Monitors
    { name: '24" 1080p Monitor', width: 1920, height: 1080, diagonal: 24, category: 'Monitor' },
    { name: '27" 1440p Monitor', width: 2560, height: 1440, diagonal: 27, category: 'Monitor' },
    { name: '32" 4K Monitor', width: 3840, height: 2160, diagonal: 32, category: 'Monitor' },
    { name: '34" Ultrawide', width: 3440, height: 1440, diagonal: 34, category: 'Monitor' },
    
    // TVs
    { name: '55" 4K TV', width: 3840, height: 2160, diagonal: 55, category: 'TV' },
    { name: '65" 4K TV', width: 3840, height: 2160, diagonal: 65, category: 'TV' },
    { name: '77" 4K TV', width: 3840, height: 2160, diagonal: 77, category: 'TV' },
  ];

  // Function to detect current display information
  const detectDisplayInfo = useCallback(() => {
    try {
      const width = window.screen.width;
      const height = window.screen.height;
      
      // Estimate diagonal size based on screen resolution and common device types
      const estimateDiagonal = (w: number, h: number): number => {
        // Mobile devices (high pixel density)
        if (w <= 500 || h <= 900) return 6.1;
        // Tablets
        if (w <= 1100 || h <= 1400) return 10.9;
        // Small laptops
        if (w <= 1600 || h <= 1200) return 13.3;
        // Standard monitors
        if (w <= 2000 || h <= 1500) return 24;
        // Large monitors
        if (w <= 3000 || h <= 2000) return 27;
        // 4K monitors
        return 32;
      };
      
      const estimatedDiagonal = estimateDiagonal(width, height);
      
      return {
        width: width.toString(),
        height: height.toString(),
        diagonal: estimatedDiagonal.toString()
      };
    } catch (error) {
      console.warn('Could not detect display info:', error);
      return null;
    }
  }, []);

  // Function to update URL parameters
  const updateURL = useCallback((width: string, height: string, diagonal: string, currentUnit: string) => {
    if (!isInitialized) return;
    
    const params = new URLSearchParams();
    if (width) params.set('width', width);
    if (height) params.set('height', height);
    if (diagonal) params.set('diagonal', diagonal);
    if (currentUnit && currentUnit !== 'inches') params.set('unit', currentUnit);
    
    const newURL = params.toString() ? `?${params.toString()}` : '/ppi-calculator';
    router.replace(newURL, { scroll: false });
  }, [router, isInitialized]);

  const calculatePPI = useCallback(() => {
    const hRes = parseFloat(horizontalRes);
    const vRes = parseFloat(verticalRes);
    const diagonal = parseFloat(diagonalSize);
    const selectedUnit = units.find(u => u.value === unit);

    if (!hRes || !vRes || !diagonal || !selectedUnit || hRes <= 0 || vRes <= 0 || diagonal <= 0) {
      setResult(null);
      return;
    }

    // Convert diagonal to inches
    const diagonalInches = diagonal / selectedUnit.conversionFactor;
    
    // Calculate diagonal resolution using Pythagorean theorem
    const diagonalPixels = Math.sqrt(hRes * hRes + vRes * vRes);
    
    // Calculate PPI
    const ppi = diagonalPixels / diagonalInches;
    
    // Calculate pixel dimensions (in inches, then convert to mm)
    const pixelWidthInches = diagonalInches / diagonalPixels * (hRes / diagonalPixels);
    const pixelHeightInches = diagonalInches / diagonalPixels * (vRes / diagonalPixels);
    const pixelWidthMM = pixelWidthInches * 25.4;
    const pixelHeightMM = pixelHeightInches * 25.4;
    
    // Calculate aspect ratio
    const gcd = (a: number, b: number): number => b === 0 ? a : gcd(b, a % b);
    const aspectGCD = gcd(hRes, vRes);
    const aspectRatio = `${hRes / aspectGCD}:${vRes / aspectGCD}`;
    
    const calculatedResult: PPIResult = {
      ppi: parseFloat(ppi.toFixed(2)),
      dpi: parseFloat(ppi.toFixed(2)), // PPI and DPI are the same for displays
      horizontalResolution: hRes,
      verticalResolution: vRes,
      diagonalSize: diagonal,
      pixelWidth: parseFloat(pixelWidthMM.toFixed(4)),
      pixelHeight: parseFloat(pixelHeightMM.toFixed(4)),
      totalPixels: hRes * vRes,
      aspectRatio: aspectRatio
    };

    setResult(calculatedResult);
    
    // Update URL with current values
    updateURL(horizontalRes, verticalRes, diagonalSize, unit);
  }, [horizontalRes, verticalRes, diagonalSize, unit, units, updateURL]);

  // Initialize from URL parameters or detect display info
  useEffect(() => {
    const initializeValues = () => {
      // First try to load from URL parameters
      const urlWidth = searchParams.get('width');
      const urlHeight = searchParams.get('height');
      const urlDiagonal = searchParams.get('diagonal');
      const urlUnit = searchParams.get('unit') || 'inches';

      if (urlWidth && urlHeight && urlDiagonal) {
        // Load from URL
        setHorizontalRes(urlWidth);
        setVerticalRes(urlHeight);
        setDiagonalSize(urlDiagonal);
        setUnit(urlUnit);
      } else {
        // Try to detect current display
        const detected = detectDisplayInfo();
        if (detected) {
          setHorizontalRes(detected.width);
          setVerticalRes(detected.height);
          setDiagonalSize(detected.diagonal);
          setUnit('inches');
        } else {
          // Fallback to default values
          setHorizontalRes('1920');
          setVerticalRes('1080');
          setDiagonalSize('24');
          setUnit('inches');
        }
      }
      
      setIsInitialized(true);
    };

    initializeValues();
  }, [searchParams, detectDisplayInfo]);

  useEffect(() => {
    if (isInitialized) {
      calculatePPI();
    }
  }, [calculatePPI, isInitialized]);

  // Custom setters that also update URL
  const setHorizontalResWithURL = useCallback((value: string) => {
    setHorizontalRes(value);
    if (isInitialized) {
      setTimeout(() => updateURL(value, verticalRes, diagonalSize, unit), 100);
    }
  }, [updateURL, isInitialized, verticalRes, diagonalSize, unit]);

  const setVerticalResWithURL = useCallback((value: string) => {
    setVerticalRes(value);
    if (isInitialized) {
      setTimeout(() => updateURL(horizontalRes, value, diagonalSize, unit), 100);
    }
  }, [updateURL, isInitialized, horizontalRes, diagonalSize, unit]);

  const setDiagonalSizeWithURL = useCallback((value: string) => {
    setDiagonalSize(value);
    if (isInitialized) {
      setTimeout(() => updateURL(horizontalRes, verticalRes, value, unit), 100);
    }
  }, [updateURL, isInitialized, horizontalRes, verticalRes, unit]);

  const setUnitWithURL = useCallback((value: string) => {
    setUnit(value);
    if (isInitialized) {
      setTimeout(() => updateURL(horizontalRes, verticalRes, diagonalSize, value), 100);
    }
  }, [updateURL, isInitialized, horizontalRes, verticalRes, diagonalSize]);

  const setDeviceSpecs = (device: CommonDevice) => {
    setHorizontalRes(device.width.toString());
    setVerticalRes(device.height.toString());
    setDiagonalSize(device.diagonal.toString());
    setUnit('inches');
    
    if (isInitialized) {
      setTimeout(() => updateURL(device.width.toString(), device.height.toString(), device.diagonal.toString(), 'inches'), 100);
    }
  };

  const clearInputs = () => {
    setHorizontalRes('');
    setVerticalRes('');
    setDiagonalSize('');
    setResult(null);
    
    if (isInitialized) {
      router.replace('/ppi-calculator', { scroll: false });
    }
  };

  const detectCurrentDisplay = () => {
    const detected = detectDisplayInfo();
    if (detected) {
      setHorizontalRes(detected.width);
      setVerticalRes(detected.height);
      setDiagonalSize(detected.diagonal);
      setUnit('inches');
      
      if (isInitialized) {
        setTimeout(() => updateURL(detected.width, detected.height, detected.diagonal, 'inches'), 100);
      }
    }
  };

  const getPPIQuality = (ppi: number): { quality: string; color: string; description: string } => {
    if (ppi < 100) return { quality: 'Low', color: 'text-red-600', description: 'Pixels may be visible to the naked eye' };
    if (ppi < 150) return { quality: 'Standard', color: 'text-orange-600', description: 'Acceptable for most uses' };
    if (ppi < 200) return { quality: 'Good', color: 'text-yellow-600', description: 'Good quality for general use' };
    if (ppi < 300) return { quality: 'High', color: 'text-green-600', description: 'High quality, sharp text and images' };
    if (ppi < 400) return { quality: 'Very High', color: 'text-blue-600', description: 'Very high quality, "Retina" level' };
    return { quality: 'Ultra High', color: 'text-purple-600', description: 'Ultra high quality, professional grade' };
  };

  return (
    <>
      <script
        type="application/ld+json"
        dangerouslySetInnerHTML={{
          __html: JSON.stringify({
            "@context": "https://schema.org",
            "@type": "WebApplication",
            "name": "PPI Calculator",
            "description": "Calculate screen PPI (Pixels Per Inch) and DPI for displays, smartphones, tablets, monitors, and TVs. Determine pixel density and display quality.",
            "applicationCategory": "UtilityApplication",
            "operatingSystem": "Web Browser",
            "url": "https://calc9.com/ppi-calculator",
            "offers": {
              "@type": "Offer",
              "price": "0",
              "priceCurrency": "USD"
            },
            "featureList": [
              "Calculate PPI and DPI",
              "Support multiple units",
              "Common device presets",
              "Pixel size calculation",
              "Aspect ratio detection",
              "Display quality assessment"
            ],
            "keywords": "PPI calculator, pixels per inch, DPI calculator, screen density, display resolution"
          })
        }}
      />
      <div className={containerStyles.page}>
        <div className="container mx-auto px-4">
          <div className="max-w-6xl mx-auto">
            <CalculatorHeader
              title="PPI Calculator"
              description="Calculate screen PPI (Pixels Per Inch) and DPI for displays, smartphones, tablets, monitors, and TVs. Determine pixel density and display quality."
              category="Other"
              className="text-center mb-12"
            />

            {/* Main Calculator and Result Section */}
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-8 mb-12">
              {/* Calculator Section */}
              <div className="bg-white dark:bg-gray-800 rounded-lg shadow-lg dark:shadow-gray-700/50 p-6 border border-gray-200 dark:border-gray-700">
                <h2 className="text-2xl font-semibold text-gray-900 dark:text-white mb-6">Screen Specifications</h2>

                <div className="space-y-6">
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                      <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                        Horizontal Resolution (pixels)
                      </label>
                      <input
                        type="number"
                        value={horizontalRes}
                        onChange={(e) => setHorizontalResWithURL(e.target.value)}
                        placeholder="1920"
                        className="w-full px-3 py-2 bg-white dark:bg-gray-700 border border-gray-300 dark:border-gray-600 rounded-md text-gray-900 dark:text-white placeholder-gray-500 dark:placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-blue-500 dark:focus:ring-blue-400 focus:border-blue-500 dark:focus:border-blue-400 transition-colors"
                      />
                    </div>
                    <div>
                      <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                        Vertical Resolution (pixels)
                      </label>
                      <input
                        type="number"
                        value={verticalRes}
                        onChange={(e) => setVerticalResWithURL(e.target.value)}
                        placeholder="1080"
                        className="w-full px-3 py-2 bg-white dark:bg-gray-700 border border-gray-300 dark:border-gray-600 rounded-md text-gray-900 dark:text-white placeholder-gray-500 dark:placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-blue-500 dark:focus:ring-blue-400 focus:border-blue-500 dark:focus:border-blue-400 transition-colors"
                      />
                    </div>
                  </div>

                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                      <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                        Diagonal Size
                      </label>
                      <input
                        type="number"
                        step="0.1"
                        value={diagonalSize}
                        onChange={(e) => setDiagonalSizeWithURL(e.target.value)}
                        placeholder="24"
                        className="w-full px-3 py-2 bg-white dark:bg-gray-700 border border-gray-300 dark:border-gray-600 rounded-md text-gray-900 dark:text-white placeholder-gray-500 dark:placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-blue-500 dark:focus:ring-blue-400 focus:border-blue-500 dark:focus:border-blue-400 transition-colors"
                      />
                    </div>
                    <div>
                      <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                        Unit
                      </label>
                      <select
                        value={unit}
                        onChange={(e) => setUnitWithURL(e.target.value)}
                        className="w-full px-3 py-2 bg-white dark:bg-gray-700 border border-gray-300 dark:border-gray-600 rounded-md text-gray-900 dark:text-white focus:outline-none focus:ring-2 focus:ring-blue-500 dark:focus:ring-blue-400 focus:border-blue-500 dark:focus:border-blue-400 transition-colors"
                      >
                        {units.map(u => (
                          <option key={u.value} value={u.value}>{u.label}</option>
                        ))}
                      </select>
                    </div>
                  </div>

                  {/* Auto-detect Button */}
                  <div>
                    <button
                      onClick={detectCurrentDisplay}
                      className="w-full bg-green-600 dark:bg-green-700 hover:bg-green-700 dark:hover:bg-green-600 text-white font-medium py-3 px-4 rounded-md transition-colors flex items-center justify-center space-x-2"
                    >
                      <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9.663 17h4.673M12 3v1m6.364 1.636l-.707.707M21 12h-1M4 12H3m3.343-5.657l-.707-.707m2.828 9.9a5 5 0 117.072 0l-.548.547A3.374 3.374 0 0014 18.469V19a2 2 0 11-4 0v-.531c0-.895-.356-1.754-.988-2.386l-.548-.547z" />
                      </svg>
                      <span>Detect Current Display</span>
                    </button>
                    <p className="text-xs text-gray-500 dark:text-gray-400 mt-2 text-center">
                      Automatically detect your current screen resolution and estimated size
                    </p>
                  </div>

                  {/* Common Devices */}
                  <div>
                    <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-3">
                      Common Devices
                    </label>
                    <div className="grid grid-cols-1 gap-2 max-h-64 overflow-y-auto">
                      {['Smartphone', 'Tablet', 'Monitor', 'TV'].map(category => (
                        <div key={category}>
                          <h4 className="text-xs font-semibold text-gray-600 dark:text-gray-400 mb-1 mt-3 first:mt-0">{category}</h4>
                          {commonDevices.filter(device => device.category === category).map(device => (
                            <button
                              key={device.name}
                              onClick={() => setDeviceSpecs(device)}
                              className="w-full text-left px-3 py-2 bg-gray-100 dark:bg-gray-700 border border-gray-200 dark:border-gray-600 rounded-md hover:bg-gray-200 dark:hover:bg-gray-600 transition-colors text-sm"
                            >
                              <div className="flex justify-between items-center">
                                <span className="text-gray-900 dark:text-white">{device.name}</span>
                                <span className="text-xs text-gray-500 dark:text-gray-400">
                                  {device.width}×{device.height} • {device.diagonal}&quot;
                                </span>
                              </div>
                            </button>
                          ))}
                        </div>
                      ))}
                    </div>
                  </div>

                  {/* Action Buttons */}
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
                    <button
                      onClick={() => {
                        const currentURL = window.location.href;
                        navigator.clipboard.writeText(currentURL).then(() => {
                          // You could add a toast notification here
                          alert('URL copied to clipboard!');
                        }).catch(() => {
                          // Fallback for older browsers
                          const textArea = document.createElement('textarea');
                          textArea.value = currentURL;
                          document.body.appendChild(textArea);
                          textArea.select();
                          document.execCommand('copy');
                          document.body.removeChild(textArea);
                          alert('URL copied to clipboard!');
                        });
                      }}
                      className="bg-blue-600 dark:bg-blue-700 hover:bg-blue-700 dark:hover:bg-blue-600 text-white font-medium py-2 px-4 rounded-md transition-colors flex items-center justify-center space-x-2"
                    >
                      <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8.684 13.342C8.886 12.938 9 12.482 9 12c0-.482-.114-.938-.316-1.342m0 2.684a3 3 0 110-2.684m0 2.684l6.632 3.316m-6.632-6l6.632-3.316m0 0a3 3 0 105.367-2.684 3 3 0 00-5.367 2.684zm0 9.316a3 3 0 105.367 2.684 3 3 0 00-5.367-2.684z" />
                      </svg>
                      <span>Share URL</span>
                    </button>
                    <button
                      onClick={clearInputs}
                      className="bg-gray-600 dark:bg-gray-700 hover:bg-gray-700 dark:hover:bg-gray-600 text-white font-medium py-2 px-4 rounded-md transition-colors"
                    >
                      Clear All
                    </button>
                  </div>
                </div>
              </div>

              {/* Results Section */}
              <div className="flex flex-col">
                {result && (
                  <div className="bg-white dark:bg-gray-800 rounded-lg shadow-lg dark:shadow-gray-700/50 p-6 border-l-4 border-blue-500 dark:border-blue-400 border border-gray-200 dark:border-gray-700 h-full">
                    <h3 className="text-xl font-semibold text-gray-900 dark:text-white mb-4">
                      📱 PPI Calculation Result
                    </h3>
                    <div className="space-y-3">
                      <div className="flex justify-between items-center p-3 bg-blue-50 dark:bg-blue-900/50 rounded-lg border border-blue-200 dark:border-blue-700">
                        <span className="text-gray-600 dark:text-gray-300">PPI (Pixels Per Inch):</span>
                        <span className="font-bold text-lg text-blue-600 dark:text-blue-300">{result.ppi}</span>
                      </div>
                      <div className="flex justify-between items-center p-3 bg-gray-50 dark:bg-gray-700 rounded-lg border border-gray-200 dark:border-gray-600">
                        <span className="text-gray-600 dark:text-gray-300">DPI (Dots Per Inch):</span>
                        <span className="font-medium text-lg text-gray-900 dark:text-white">{result.dpi}</span>
                      </div>
                      <div className="flex justify-between items-center p-3 bg-gray-50 dark:bg-gray-700 rounded-lg border border-gray-200 dark:border-gray-600">
                        <span className="text-gray-600 dark:text-gray-300">Total Pixels:</span>
                        <span className="font-medium text-lg text-gray-900 dark:text-white">{result.totalPixels.toLocaleString()}</span>
                      </div>
                      <div className="flex justify-between items-center p-3 bg-gray-50 dark:bg-gray-700 rounded-lg border border-gray-200 dark:border-gray-600">
                        <span className="text-gray-600 dark:text-gray-300">Aspect Ratio:</span>
                        <span className="font-medium text-lg text-gray-900 dark:text-white">{result.aspectRatio}</span>
                      </div>
                      <div className="flex justify-between items-center p-3 bg-gray-50 dark:bg-gray-700 rounded-lg border border-gray-200 dark:border-gray-600">
                        <span className="text-gray-600 dark:text-gray-300">Pixel Size:</span>
                        <span className="font-medium text-lg text-gray-900 dark:text-white">
                          {result.pixelWidth} × {result.pixelHeight} mm
                        </span>
                      </div>
                      
                      {(() => {
                        const quality = getPPIQuality(result.ppi);
                        return (
                          <div className="flex justify-between items-center p-3 bg-gradient-to-r from-gray-50 to-gray-100 dark:from-gray-700 dark:to-gray-600 rounded-lg border border-gray-200 dark:border-gray-600">
                            <span className="text-gray-600 dark:text-gray-300">Display Quality:</span>
                            <div className="text-right">
                              <span className={`font-bold text-lg ${quality.color} dark:${quality.color.replace('text-', 'text-').replace('-600', '-400')}`}>{quality.quality}</span>
                              <p className="text-xs text-gray-500 dark:text-gray-400 mt-1">{quality.description}</p>
                            </div>
                          </div>
                        );
                      })()}
                    </div>
                  </div>
                )}
                
                {/* Placeholder when no result */}
                {!result && (
                  <div className="bg-gray-100 dark:bg-gray-700 rounded-lg shadow-lg dark:shadow-gray-700/50 p-6 border-l-4 border-gray-300 dark:border-gray-600 border border-gray-200 dark:border-gray-700 h-full flex items-center justify-center">
                    <div className="text-center text-gray-500 dark:text-gray-400">
                      <div className="text-4xl mb-4">📱</div>
                      <h3 className="text-lg font-medium mb-2">Enter Screen Specifications</h3>
                      <p className="text-sm">Fill in the form to calculate PPI and display quality</p>
                    </div>
                  </div>
                )}
              </div>
            </div>

            {/* Information Sections */}
            <div className="space-y-6">
              {/* PPI Information */}
              <div className="bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-700 rounded-lg p-6">
                <h3 className="text-xl font-semibold text-blue-800 dark:text-blue-200 mb-4">📐 About PPI & DPI</h3>
                <div className="space-y-3 text-blue-700 dark:text-blue-300">
                  <p className="text-sm">
                    <strong>PPI (Pixels Per Inch):</strong> Measures pixel density on digital displays. Higher PPI means sharper, more detailed images.
                  </p>
                  <p className="text-sm">
                    <strong>DPI (Dots Per Inch):</strong> Originally for printers, but often used interchangeably with PPI for displays.
                  </p>
                  <p className="text-sm">
                    <strong>Retina Display:</strong> Apple&apos;s term for displays with PPI high enough that individual pixels are indistinguishable at normal viewing distance (~300+ PPI).
                  </p>
                  <p className="text-sm">
                    <strong>Viewing Distance:</strong> The optimal PPI depends on how close you view the screen. Phones need higher PPI than TVs.
                  </p>
                </div>
              </div>

              {/* PPI Guidelines and Common Resolutions - Same Row */}
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                {/* PPI Guidelines */}
                <div className="bg-green-50 dark:bg-green-900/20 border border-green-200 dark:border-green-700 rounded-lg p-6">
                  <h3 className="text-xl font-semibold text-green-800 dark:text-green-200 mb-4">✅ PPI Guidelines</h3>
                  <div className="space-y-2 text-green-700 dark:text-green-300">
                    <p className="text-sm"><strong>Smartphones:</strong> 300-500+ PPI (high density for close viewing)</p>
                    <p className="text-sm"><strong>Tablets:</strong> 200-300 PPI (balance of sharpness and cost)</p>
                    <p className="text-sm"><strong>Monitors:</strong> 80-150 PPI (depends on size and distance)</p>
                    <p className="text-sm"><strong>TVs:</strong> 30-80 PPI (viewed from greater distance)</p>
                    <p className="text-sm"><strong>Professional:</strong> 200+ PPI (photo editing, design work)</p>
                  </div>
                </div>

                {/* Common Resolutions */}
                <div className="bg-purple-50 dark:bg-purple-900/20 border border-purple-200 dark:border-purple-700 rounded-lg p-6">
                  <h3 className="text-xl font-semibold text-purple-800 dark:text-purple-200 mb-4">📊 Common Resolutions</h3>
                  <div className="text-purple-700 dark:text-purple-300 space-y-2">
                    <p className="text-sm"><strong>HD:</strong> 1280×720 (720p)</p>
                    <p className="text-sm"><strong>Full HD:</strong> 1920×1080 (1080p)</p>
                    <p className="text-sm"><strong>QHD:</strong> 2560×1440 (1440p)</p>
                    <p className="text-sm"><strong>4K UHD:</strong> 3840×2160 (2160p)</p>
                    <p className="text-sm"><strong>8K UHD:</strong> 7680×4320 (4320p)</p>
                  </div>
                </div>
              </div>

              {/* Calculation Formula */}
              <div className="bg-yellow-50 dark:bg-yellow-900/20 border border-yellow-200 dark:border-yellow-700 rounded-lg p-6">
                <h3 className="text-xl font-semibold text-yellow-800 dark:text-yellow-200 mb-4">🔢 Calculation Formula</h3>
                <div className="text-yellow-700 dark:text-yellow-300 space-y-2">
                  <p className="text-sm font-mono bg-white dark:bg-gray-800 px-2 py-1 rounded border border-gray-200 dark:border-gray-600">
                    PPI = √(width² + height²) / diagonal
                  </p>
                  <p className="text-sm">Where width and height are in pixels, and diagonal is in inches.</p>
                  <p className="text-sm">Example: 1920×1080 on 24&quot; = √(1920² + 1080²) / 24 = 91.79 PPI</p>
                </div>
              </div>
            </div>
          </div>
        </div>
        <Footer />
      </div>
    </>
  );
}

// Suspense wrapper for useSearchParams
export default function PPICalculator() {
  return (
    <Suspense fallback={
      <div className={containerStyles.page}>
        <div className="container mx-auto px-4">
          <div className="max-w-6xl mx-auto">
            <CalculatorHeader
              title="PPI Calculator"
              description="Calculate screen PPI (Pixels Per Inch) and DPI for displays, smartphones, tablets, monitors, and TVs. Determine pixel density and display quality."
              category="Other"
              className="text-center mb-12"
            />
            <div className="flex items-center justify-center h-64">
              <div className="text-center text-gray-500 dark:text-gray-400">
                <div className="text-4xl mb-4">📱</div>
                <h3 className="text-lg font-medium mb-2">Loading Calculator...</h3>
                <p className="text-sm">Please wait while we initialize the PPI calculator</p>
              </div>
            </div>
          </div>
        </div>
        <Footer />
      </div>
    }>
      <PPICalculatorContent />
    </Suspense>
  );
}
