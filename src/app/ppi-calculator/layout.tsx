import { generatePageTitle, generateCanonicalUrl } from '@/lib/utils';

export const metadata = {
  title: generatePageTitle("PPI Calculator"),
  description: "Calculate screen PPI (Pixels Per Inch) and DPI for displays, smartphones, tablets, monitors, and TVs. Determine pixel density and display quality with diagonal size and resolution.",
  keywords: "PPI calculator, pixels per inch, DPI calculator, screen density, display resolution, pixel density, monitor PPI, smartphone PPI, tablet PPI, screen quality, display calculator",
  robots: "index, follow",
  alternates: {
    canonical: generateCanonicalUrl("/ppi-calculator"),
  },
  openGraph: {
    title: "PPI Calculator - Calculate Screen Pixels Per Inch",
    description: "Calculate screen PPI (Pixels Per Inch) and DPI for displays, smartphones, tablets, monitors, and TVs.",
    type: 'website',
    url: generateCanonicalUrl("/ppi-calculator"),
  },
  twitter: {
    card: 'summary_large_image',
    title: "PPI Calculator - Calculate Screen Pixels Per Inch",
    description: "Calculate screen PPI (Pixels Per Inch) and DPI for displays, smartphones, tablets, monitors, and TVs.",
  },
};

export default function PPICalculatorLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  return children;
}