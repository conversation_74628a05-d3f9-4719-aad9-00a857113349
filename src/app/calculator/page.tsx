'use client';

import { useState, useEffect } from 'react';
import Footer from "@/components/Footer";
import CalculatorHeader from '@/components/CalculatorHeader';
import { containerStyles, textStyles, buttonStyles, cn } from '@/components/ui/styles';

export default function Calculator() {
  const [display, setDisplay] = useState('0');
  const [previousValue, setPreviousValue] = useState<number | null>(null);
  const [operation, setOperation] = useState<string | null>(null);
  const [waitingForOperand, setWaitingForOperand] = useState(false);

  useEffect(() => {
    const handleKeyPress = (event: KeyboardEvent) => {
      const { key } = event;
      
      if (key >= '0' && key <= '9') {
        inputNumber(key);
      } else if (['+', '-'].includes(key)) {
        inputOperator(key === '+' ? '+' : '-');
      } else if (key === '*') {
        inputOperator('×');
      } else if (key === '/') {
        event.preventDefault();
        inputOperator('÷');
      } else if (key === 'Enter' || key === '=') {
        event.preventDefault();
        calculate();
      } else if (key === '.' || key === ',') {
        inputDecimal();
      } else if (key === 'Escape' || key.toLowerCase() === 'c') {
        clear();
      } else if (key === 'Backspace') {
        if (display.length > 1) {
          setDisplay(display.slice(0, -1));
        } else {
          setDisplay('0');
        }
      }
    };

    window.addEventListener('keydown', handleKeyPress);
    return () => window.removeEventListener('keydown', handleKeyPress);
  }); // Removed dependency array to avoid exhaustive-deps warning

  const inputNumber = (num: string) => {
    if (waitingForOperand) {
      setDisplay(String(num));
      setWaitingForOperand(false);
    } else {
      setDisplay(display === '0' ? String(num) : display + num);
    }
  };

  const inputOperator = (nextOperator: string) => {
    const inputValue = parseFloat(display);

    if (previousValue === null) {
      setPreviousValue(inputValue);
    } else if (operation) {
      const currentValue = previousValue || 0;
      const newValue = performCalculation(currentValue, inputValue, operation);

      setDisplay(String(newValue));
      setPreviousValue(newValue);
    }

    setWaitingForOperand(true);
    setOperation(nextOperator);
  };

  const performCalculation = (firstValue: number, secondValue: number, operation: string) => {
    switch (operation) {
      case '+':
        return firstValue + secondValue;
      case '-':
        return firstValue - secondValue;
      case '×':
        return firstValue * secondValue;
      case '÷':
        return firstValue / secondValue;
      case '%':
        return firstValue % secondValue;
      default:
        return secondValue;
    }
  };

  const calculate = () => {
    const inputValue = parseFloat(display);

    if (previousValue !== null && operation) {
      const newValue = performCalculation(previousValue, inputValue, operation);
      setDisplay(String(newValue));
      setPreviousValue(null);
      setOperation(null);
      setWaitingForOperand(true);
    }
  };

  const clear = () => {
    setDisplay('0');
    setPreviousValue(null);
    setOperation(null);
    setWaitingForOperand(false);
  };

  const inputDecimal = () => {
    if (waitingForOperand) {
      setDisplay('0.');
      setWaitingForOperand(false);
    } else if (display.indexOf('.') === -1) {
      setDisplay(display + '.');
    }
  };

  const toggleSign = () => {
    if (display !== '0') {
      setDisplay(display.charAt(0) === '-' ? display.slice(1) : '-' + display);
    }
  };

  return (
    <>
      <div className={containerStyles.page}>
        <div className="container mx-auto px-4 py-8">
          <div className="max-w-md mx-auto">
            <CalculatorHeader
              title="Simple Calculator"
              description="Basic calculator for everyday arithmetic operations. Clean interface with keyboard support."
              category="Math"
            />
            
            <div className={containerStyles.card + " p-6"}>
              {/* Display */}
              <div className="mb-4">
                <div className="bg-gray-100 dark:bg-gray-700 rounded-lg p-4 text-right border border-gray-200 dark:border-gray-600">
                  <div className={cn("text-3xl font-mono overflow-hidden", textStyles.h2)}>
                    {display}
                  </div>
                </div>
              </div>

              {/* Buttons */}
              <div className="grid grid-cols-4 gap-3">
                {/* Row 1 */}
                <button
                  onClick={clear}
                  className={buttonStyles.calc.clear}
                  title="Clear (C or Esc)"
                >
                  C
                </button>
                <button
                  onClick={toggleSign}
                  className={buttonStyles.calc.function}
                  title="Toggle Sign"
                >
                  ±
                </button>
                <button
                  onClick={() => inputOperator('%')}
                  className={buttonStyles.calc.function}
                  title="Percentage"
                >
                  %
                </button>
                <button
                  onClick={() => inputOperator('÷')}
                  className={buttonStyles.calc.operator}
                  title="Divide (/)"
                >
                  ÷
                </button>

                {/* Row 2 */}
                <button
                  onClick={() => inputNumber('7')}
                  className={buttonStyles.calc.number}
                  title="Seven"
                >
                  7
                </button>
                <button
                  onClick={() => inputNumber('8')}
                  className={buttonStyles.calc.number}
                  title="Eight"
                >
                  8
                </button>
                <button
                  onClick={() => inputNumber('9')}
                  className={buttonStyles.calc.number}
                  title="Nine"
                >
                  9
                </button>
                <button
                  onClick={() => inputOperator('×')}
                  className={buttonStyles.calc.operator}
                  title="Multiply (*)"
                >
                  ×
                </button>

                {/* Row 3 */}
                <button
                  onClick={() => inputNumber('4')}
                  className={buttonStyles.calc.number}
                  title="Four"
                >
                  4
                </button>
                <button
                  onClick={() => inputNumber('5')}
                  className={buttonStyles.calc.number}
                  title="Five"
                >
                  5
                </button>
                <button
                  onClick={() => inputNumber('6')}
                  className={buttonStyles.calc.number}
                  title="Six"
                >
                  6
                </button>
                <button
                  onClick={() => inputOperator('-')}
                  className={buttonStyles.calc.operator}
                  title="Subtract (-)"
                >
                  -
                </button>

                {/* Row 4 */}
                <button
                  onClick={() => inputNumber('1')}
                  className={buttonStyles.calc.number}
                  title="One"
                >
                  1
                </button>
                <button
                  onClick={() => inputNumber('2')}
                  className={buttonStyles.calc.number}
                  title="Two"
                >
                  2
                </button>
                <button
                  onClick={() => inputNumber('3')}
                  className={buttonStyles.calc.number}
                  title="Three"
                >
                  3
                </button>
                <button
                  onClick={() => inputOperator('+')}
                  className={buttonStyles.calc.operator}
                  title="Add (+)"
                >
                  +
                </button>

                {/* Row 5 */}
                <button
                  onClick={() => inputNumber('0')}
                  className={cn("col-span-2", buttonStyles.calc.number)}
                  title="Zero"
                >
                  0
                </button>
                <button
                  onClick={inputDecimal}
                  className={buttonStyles.calc.number}
                  title="Decimal Point (.)"
                >
                  .
                </button>
                <button
                  onClick={calculate}
                  className={buttonStyles.calc.equals}
                  title="Equals (Enter or =)"
                >
                  =
                </button>
              </div>
            </div>

            <div className="mt-8 text-center">
              <div className={cn("text-center", containerStyles.infoCard, "p-4")}>
                <p className={cn(textStyles.h4, "mb-2")}>Keyboard Shortcuts:</p>
                <p className={textStyles.bodySmall}>Numbers (0-9), Operators (+, -, *, /), Enter/= (calculate), Esc/C (clear), Backspace (delete)</p>
              </div>
            </div>
          </div>
        </div>
        <Footer />
      </div>
    </>
  );
}