import { generatePageTitle, generateCanonicalUrl } from '@/lib/utils';

export const metadata = {
  title: generatePageTitle("Terms of Service"),
  description: "Review our terms of service and user agreement for using our calculator tools and website services.",
  keywords: "terms of service, user agreement, terms of use, calculator terms",
  robots: "index, follow",
  alternates: {
    canonical: generateCanonicalUrl("/terms-of-service"),
  },
};

export default function TermsOfServiceLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  return children;
}