import Link from 'next/link';
import Footer from '@/components/Footer';
import PageHeader from '@/components/PageHeader';
import EmailProtected from '@/components/EmailProtected';
import { containerStyles, textStyles, statusStyles, cn } from '@/components/ui/styles';

export default function TermsOfService() {
  return (
    <>
      <div className={containerStyles.page}>
        <div className="container mx-auto px-4">
          <div className="max-w-4xl mx-auto">
            <PageHeader
              title="Terms of Service"
              lastUpdated="December 2024"
            />

            <div className={cn(containerStyles.card, "p-8")}>
              <div className="prose prose-lg max-w-none">
                <section className="mb-8">
                  <h2 className={cn(textStyles.h2, "mb-4")}>1. Acceptance of Terms</h2>
                  <p className={cn(textStyles.body, "mb-4")}>
                    Welcome to Calc9. These Terms of Service (&quot;Terms&quot;) govern your use of our website located at calc9.com and all related calculator tools and services (collectively, the &quot;Service&quot;) operated by Calc9 (&quot;us,&quot; &quot;we,&quot; or &quot;our&quot;).
                  </p>
                  <p className={textStyles.body}>
                    By accessing or using our Service, you agree to be bound by these Terms. If you disagree with any part of these terms, then you may not access the Service.
                  </p>
                </section>

                <section className="mb-8">
                  <h2 className={cn(textStyles.h2, "mb-4")}>2. Description of Service</h2>
                  <p className={cn(textStyles.body, "mb-4")}>
                    Calc9 provides online calculator tools including but not limited to:
                  </p>
                  <ul className={cn("list-disc pl-6 space-y-2", textStyles.body)}>
                    <li>Basic and scientific calculators</li>
                    <li>Health calculators (BMI, calorie, body type)</li>
                    <li>Construction calculators (concrete)</li>
                    <li>Financial calculators (Certificate of Deposit)</li>
                    <li>Related educational content and resources</li>
                  </ul>
                  <p className={cn(textStyles.body, "mt-4")}>
                    Our Service is provided free of charge and is intended for informational and educational purposes.
                  </p>
                </section>

                <section className="mb-8">
                  <h2 className={cn(textStyles.h2, "mb-4")}>3. User Responsibilities</h2>
                  <p className={cn(textStyles.body, "mb-4")}>
                    When using our Service, you agree to:
                  </p>
                  <ul className={cn("list-disc pl-6 space-y-2", textStyles.body)}>
                    <li>Use the Service only for lawful purposes and in accordance with these Terms</li>
                    <li>Not attempt to gain unauthorized access to any part of the Service</li>
                    <li>Not transmit any malicious code, viruses, or harmful content</li>
                    <li>Not use automated systems to access the Service excessively</li>
                    <li>Respect intellectual property rights</li>
                    <li>Provide accurate information when submitting feedback or inquiries</li>
                  </ul>
                </section>

                <section className="mb-8">
                  <h2 className={cn(textStyles.h2, "mb-4")}>4. Accuracy and Reliability</h2>
                  <div className={cn(statusStyles.warning.container, "p-4 mb-4")}>
                    <h3 className={cn(statusStyles.warning.title, "text-lg mb-2")}>Important Disclaimer</h3>
                    <p className={statusStyles.warning.text}>
                      While we strive to provide accurate calculations, all results are provided for informational purposes only and should not be considered as professional advice.
                    </p>
                  </div>
                  <ul className={cn("list-disc pl-6 space-y-2", textStyles.body)}>
                    <li><strong>Health Calculators:</strong> Results are estimates and not medical advice. Consult healthcare professionals for medical decisions.</li>
                    <li><strong>Financial Calculators:</strong> Results are estimates and not financial advice. Consult financial advisors for investment decisions.</li>
                    <li><strong>Construction Calculators:</strong> Results are estimates. Always verify with professionals for actual construction projects.</li>
                    <li><strong>General Calculators:</strong> While we ensure accuracy, users should verify critical calculations independently.</li>
                  </ul>
                </section>

                <section className="mb-8">
                  <h2 className={cn(textStyles.h2, "mb-4")}>5. Intellectual Property Rights</h2>
                  <p className={cn(textStyles.body, "mb-4")}>
                    The Service and its original content, features, and functionality are and will remain the exclusive property of Calc9 and its licensors. The Service is protected by copyright, trademark, and other laws.
                  </p>
                  <ul className={cn("list-disc pl-6 space-y-2", textStyles.body)}>
                    <li>You may not copy, modify, distribute, or create derivative works based on our Service</li>
                    <li>You may not reverse engineer or attempt to extract source code</li>
                    <li>Personal use of calculators for individual calculations is permitted</li>
                    <li>Commercial use requires our written permission</li>
                  </ul>
                </section>

                <section className="mb-8">
                  <h2 className={cn(textStyles.h2, "mb-4")}>6. Privacy and Data Collection</h2>
                  <p className={cn(textStyles.body, "mb-4")}>
                    Your privacy is important to us. Our Privacy Policy explains how we collect, use, and protect your information when you use our Service. Key points:
                  </p>
                  <ul className={cn("list-disc pl-6 space-y-2", textStyles.body)}>
                    <li>Calculator inputs are processed locally in your browser</li>
                    <li>We collect anonymous usage analytics to improve our Service</li>
                    <li>We do not store your calculation data on our servers</li>
                    <li>Contact information is only collected when you voluntarily provide it</li>
                  </ul>
                  <p className={cn(textStyles.body, "mt-4")}>
                    Please review our <Link href="/privacy-policy" className={cn("hover:underline", statusStyles.info.text)}>Privacy Policy</Link> for complete details.
                  </p>
                </section>

                <section className="mb-8">
                  <h2 className={cn(textStyles.h2, "mb-4")}>7. Limitation of Liability</h2>
                  <p className={cn(textStyles.body, "mb-4")}>
                    To the maximum extent permitted by applicable law, Calc9 shall not be liable for any indirect, incidental, special, consequential, or punitive damages, including but not limited to:
                  </p>
                  <ul className={cn("list-disc pl-6 space-y-2", textStyles.body)}>
                    <li>Loss of profits, data, use, goodwill, or other intangible losses</li>
                    <li>Damages resulting from reliance on calculator results</li>
                    <li>Damages resulting from unauthorized access to or alteration of your data</li>
                    <li>Damages resulting from any conduct of third parties</li>
                  </ul>
                  <p className={cn(textStyles.body, "mt-4")}>
                    Our total liability to you for all claims arising from the use of the Service shall not exceed $100.
                  </p>
                </section>

                <section className="mb-8">
                  <h2 className={cn(textStyles.h2, "mb-4")}>8. Disclaimers</h2>
                  <p className={cn(textStyles.body, "mb-4")}>
                    The Service is provided on an &quot;AS IS&quot; and &quot;AS AVAILABLE&quot; basis. Calc9 makes no warranties, expressed or implied, including but not limited to:
                  </p>
                  <ul className={cn("list-disc pl-6 space-y-2", textStyles.body)}>
                    <li>Warranties of merchantability, fitness for a particular purpose, or non-infringement</li>
                    <li>That the Service will be uninterrupted, secure, or error-free</li>
                    <li>That defects will be corrected</li>
                    <li>That the Service is free of viruses or other harmful components</li>
                  </ul>
                </section>

                <section className="mb-8">
                  <h2 className={cn(textStyles.h2, "mb-4")}>9. Indemnification</h2>
                  <p className={textStyles.body}>
                    You agree to defend, indemnify, and hold harmless Calc9, its officers, directors, employees, and agents from and against any claims, damages, obligations, losses, liabilities, costs, or debt arising from your use of and access to the Service or your violation of these Terms.
                  </p>
                </section>

                <section className="mb-8">
                  <h2 className={cn(textStyles.h2, "mb-4")}>10. Termination</h2>
                  <p className={cn(textStyles.body, "mb-4")}>
                    We may terminate or suspend your access to the Service immediately, without prior notice or liability, for any reason, including but not limited to breach of these Terms.
                  </p>
                  <p className={textStyles.body}>
                    Upon termination, your right to use the Service will cease immediately. The following sections shall survive termination: Intellectual Property Rights, Limitation of Liability, Disclaimers, Indemnification, and Governing Law.
                  </p>
                </section>

                <section className="mb-8">
                  <h2 className={cn(textStyles.h2, "mb-4")}>11. Governing Law and Jurisdiction</h2>
                  <p className={textStyles.body}>
                    These Terms shall be interpreted and governed by the laws of the jurisdiction where Calc9 operates, without regard to conflict of law provisions. Any disputes arising from these Terms or your use of the Service shall be subject to the exclusive jurisdiction of the courts in that jurisdiction.
                  </p>
                </section>

                <section className="mb-8">
                  <h2 className={cn(textStyles.h2, "mb-4")}>12. Changes to Terms</h2>
                  <p className={textStyles.body}>
                    We reserve the right to modify or replace these Terms at any time. If a revision is material, we will try to provide at least 30 days advance notice. Material changes will be indicated by updating the &quot;Last updated&quot; date at the top of this page. Your continued use of the Service after any such changes constitutes your acceptance of the new Terms.
                  </p>
                </section>

                <section className="mb-8">
                  <h2 className={cn(textStyles.h2, "mb-4")}>13. Severability</h2>
                  <p className={textStyles.body}>
                    If any provision of these Terms is held to be invalid or unenforceable by a court, the remaining provisions will remain in effect. The invalid or unenforceable provision will be deemed modified to the extent necessary to make it valid and enforceable.
                  </p>
                </section>

                <section className="mb-8">
                  <h2 className={cn(textStyles.h2, "mb-4")}>14. Contact Information</h2>
                  <p className={cn(textStyles.body, "mb-4")}>
                    If you have any questions about these Terms of Service, please contact us:
                  </p>
                  <div className={cn(statusStyles.info.container, "p-4")}>
                    <p className={statusStyles.info.text}>
                      <strong>Email:</strong> <EmailProtected user="tianlun.song" domain="foxmail.com" /><br />
                      <strong>Website:</strong> calc9.com
                    </p>
                  </div>
                </section>

                <section className="mb-8">
                  <h2 className={cn(textStyles.h2, "mb-4")}>15. Acknowledgment</h2>
                  <p className={textStyles.body}>
                    By using our Service, you acknowledge that you have read these Terms of Service, understood them, and agree to be bound by them. If you do not agree to these Terms, please do not use our Service.
                  </p>
                </section>
              </div>
            </div>
          </div>
        </div>
      </div>
      <Footer />
    </>
  );
}