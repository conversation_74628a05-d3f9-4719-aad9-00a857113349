'use client';

import { useState, useEffect, useCallback } from 'react';
import Footer from "@/components/Footer";
import CalculatorHeader from '@/components/CalculatorHeader';
import { containerStyles, textStyles, inputStyles, statusStyles, cn } from '@/components/ui/styles';

interface ConversionResults {
  binary: string;
  octal: string;
  decimal: string;
  hexadecimal: string;
  custom: string;
}

export default function BaseConverter() {
  const [inputValue, setInputValue] = useState<string>('42');
  const [fromBase, setFromBase] = useState<number>(10);
  const [toBase, setToBase] = useState<number>(2);
  const [customFromBase, setCustomFromBase] = useState<number>(10);
  const [customToBase, setCustomToBase] = useState<number>(16);
  const [results, setResults] = useState<ConversionResults>({
    binary: '101010',
    octal: '52',
    decimal: '42',
    hexadecimal: '2A',
    custom: '2A'
  });
  const [error, setError] = useState<string>('');

  const commonBases = [
    { value: 2, label: 'Binary (Base 2)', abbr: 'BIN', description: 'Computer systems, digital logic' },
    { value: 8, label: 'Octal (Base 8)', abbr: 'OCT', description: 'Unix permissions, legacy systems' },
    { value: 10, label: 'Decimal (Base 10)', abbr: 'DEC', description: 'Human counting, everyday math' },
    { value: 16, label: 'Hexadecimal (Base 16)', abbr: 'HEX', description: 'Memory addresses, colors' },
    { value: 12, label: 'Duodecimal (Base 12)', abbr: 'DUO', description: 'Time, measurements' },
    { value: 20, label: 'Vigesimal (Base 20)', abbr: 'VIG', description: 'Ancient counting systems' },
    { value: 60, label: 'Sexagesimal (Base 60)', abbr: 'SEX', description: 'Time, angles, coordinates' }
  ];

  const isValidDigit = useCallback((digit: string, base: number): boolean => {
    const char = digit.toUpperCase();
    if (base <= 10) {
      return /^\d$/.test(digit) && parseInt(digit) < base;
    } else {
      const charCode = char.charCodeAt(0);
      if (/^\d$/.test(digit)) {
        return parseInt(digit) < Math.min(base, 10);
      } else if (/^[A-Z]$/.test(char)) {
        return charCode - 55 < base; // A=10, B=11, etc.
      }
    }
    return false;
  }, []);

  const validateInput = useCallback((value: string, base: number): boolean => {
    if (!value.trim()) return false;
    return value.split('').every(digit => isValidDigit(digit, base));
  }, [isValidDigit]);

  const convertFromBase = useCallback((value: string, fromBase: number): number => {
    let result = 0;
    const digits = value.toUpperCase().split('').reverse();
    
    for (let i = 0; i < digits.length; i++) {
      const digit = digits[i];
      let digitValue: number;
      
      if (/^\d$/.test(digit)) {
        digitValue = parseInt(digit);
      } else {
        digitValue = digit.charCodeAt(0) - 55; // A=10, B=11, etc.
      }
      
      result += digitValue * Math.pow(fromBase, i);
    }
    
    return result;
  }, []);

  const convertToBase = useCallback((value: number, toBase: number): string => {
    if (value === 0) return '0';
    
    let result = '';
    let remaining = Math.abs(value);
    
    while (remaining > 0) {
      const remainder = remaining % toBase;
      if (remainder < 10) {
        result = remainder.toString() + result;
      } else {
        result = String.fromCharCode(55 + remainder) + result; // 10=A, 11=B, etc.
      }
      remaining = Math.floor(remaining / toBase);
    }
    
    return value < 0 ? '-' + result : result;
  }, []);

  const performConversion = useCallback(() => {
    try {
      setError('');
      
      if (!validateInput(inputValue, fromBase)) {
        setError(`Invalid input for base ${fromBase}`);
        return;
      }

      const decimalValue = convertFromBase(inputValue, fromBase);
      
      const newResults: ConversionResults = {
        binary: convertToBase(decimalValue, 2),
        octal: convertToBase(decimalValue, 8),
        decimal: decimalValue.toString(),
        hexadecimal: convertToBase(decimalValue, 16),
        custom: convertToBase(decimalValue, toBase)
      };
      
      setResults(newResults);
    } catch {
      setError('Conversion error occurred');
    }
  }, [inputValue, fromBase, toBase, validateInput, convertFromBase, convertToBase]);

  useEffect(() => {
    if (inputValue.trim()) {
      performConversion();
    }
  }, [inputValue, fromBase, toBase, performConversion]);

  const getValidChars = (base: number): string => {
    if (base <= 10) {
      return `0-${base - 1}`;
    } else {
      const letters = String.fromCharCode(...Array.from({length: base - 10}, (_, i) => 65 + i));
      return `0-9, A-${letters.slice(-1)}`;
    }
  };

  const handleQuickConvert = (value: string, from: number) => {
    setInputValue(value);
    setFromBase(from);
  };

  return (
    <>
      <div className={containerStyles.page}>
        <div className="container mx-auto px-4 py-8">
          <div className="max-w-6xl mx-auto">
            <CalculatorHeader
              title="Number Base Converter"
              description="Convert numbers between different bases (binary, octal, decimal, hexadecimal, and more). Learn about number systems and their practical applications in computing and mathematics."
              category="Math"
              className="text-center mb-12"
            />

            <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
              {/* Converter Section */}
              <div className={containerStyles.form}>
                <h2 className={cn(textStyles.h2, "mb-6")}>Base Converter</h2>

                <div className="space-y-6">
                  {/* Input Section */}
                  <div>
                    <label className={cn(textStyles.label, "block mb-2")}>
                      Input Number
                    </label>
                    <input
                      type="text"
                      value={inputValue}
                      onChange={(e) => setInputValue(e.target.value.toUpperCase())}
                      className={cn(inputStyles.base, "font-mono text-lg")}
                      placeholder="Enter number"
                    />
                    {error && (
                      <p className={cn("text-sm mt-1", statusStyles.danger.text)}>{error}</p>
                    )}
                  </div>

                  {/* From Base Selection */}
                  <div>
                    <label className={cn(textStyles.label, "block mb-2")}>
                      From Base
                    </label>
                    <div className="grid grid-cols-2 md:grid-cols-4 gap-2 mb-3">
                      {[2, 8, 10, 16].map((base) => (
                        <button
                          key={base}
                          onClick={() => setFromBase(base)}
                          className={cn(
                            "p-2 rounded-md text-sm font-medium transition-colors",
                            fromBase === base
                              ? "bg-blue-500 dark:bg-blue-600 text-white"
                              : cn("border border-gray-300 dark:border-gray-600 hover:bg-gray-50 dark:hover:bg-gray-600", "bg-white dark:bg-gray-700", textStyles.body)
                          )}
                        >
                          {base === 2 ? 'BIN' : base === 8 ? 'OCT' : base === 10 ? 'DEC' : 'HEX'}
                        </button>
                      ))}
                    </div>
                    <div className="flex items-center space-x-2">
                      <label className={cn("text-sm", textStyles.body)}>Custom:</label>
                      <input
                        type="number"
                        min="2"
                        max="36"
                        value={customFromBase}
                        onChange={(e) => {
                          const base = parseInt(e.target.value) || 2;
                          setCustomFromBase(base);
                          setFromBase(base);
                        }}
                        className={cn(inputStyles.base, "w-20 text-sm")}
                      />
                      <span className={cn("text-sm", textStyles.muted)}>
                        Valid chars: {getValidChars(fromBase)}
                      </span>
                    </div>
                  </div>

                  {/* To Base Selection */}
                  <div>
                    <label className={cn(textStyles.label, "block mb-2")}>
                      To Base (for custom conversion)
                    </label>
                    <div className="flex items-center space-x-2">
                      <input
                        type="number"
                        min="2"
                        max="36"
                        value={customToBase}
                        onChange={(e) => {
                          const base = parseInt(e.target.value) || 2;
                          setCustomToBase(base);
                          setToBase(base);
                        }}
                        className={cn(inputStyles.base, "w-20 text-sm")}
                      />
                      <span className={cn("text-sm", textStyles.muted)}>(Base 2-36)</span>
                    </div>
                  </div>

                  {/* Quick Examples */}
                  <div className="border-t border-gray-200 dark:border-gray-700 pt-4">
                    <h3 className={cn("text-sm font-medium mb-3", textStyles.h4)}>Quick Examples</h3>
                    <div className="grid grid-cols-2 gap-2">
                      <button
                        onClick={() => handleQuickConvert('255', 10)}
                        className={cn(containerStyles.cardSmall, "text-left p-2 text-sm transition-colors hover:bg-gray-50 dark:hover:bg-gray-700")}
                      >
                        <div className={cn("font-medium", textStyles.body)}>255 (DEC)</div>
                        <div className={cn("text-xs", textStyles.muted)}>Common in RGB colors</div>
                      </button>
                      <button
                        onClick={() => handleQuickConvert('FF', 16)}
                        className={cn(containerStyles.cardSmall, "text-left p-2 text-sm transition-colors hover:bg-gray-50 dark:hover:bg-gray-700")}
                      >
                        <div className={cn("font-medium", textStyles.body)}>FF (HEX)</div>
                        <div className={cn("text-xs", textStyles.muted)}>Maximum byte value</div>
                      </button>
                      <button
                        onClick={() => handleQuickConvert('1024', 10)}
                        className={cn(containerStyles.cardSmall, "text-left p-2 text-sm transition-colors hover:bg-gray-50 dark:hover:bg-gray-700")}
                      >
                        <div className={cn("font-medium", textStyles.body)}>1024 (DEC)</div>
                        <div className={cn("text-xs", textStyles.muted)}>1 KB in binary</div>
                      </button>
                      <button
                        onClick={() => handleQuickConvert('777', 8)}
                        className={cn(containerStyles.cardSmall, "text-left p-2 text-sm transition-colors hover:bg-gray-50 dark:hover:bg-gray-700")}
                      >
                        <div className={cn("font-medium", textStyles.body)}>777 (OCT)</div>
                        <div className={cn("text-xs", textStyles.muted)}>Unix permissions</div>
                      </button>
                    </div>
                  </div>
                </div>
              </div>

              {/* Results Section */}
              <div className="space-y-6">
                {!error && inputValue.trim() && (
                  <div className={cn(containerStyles.card, "p-6 border-l-4 border-green-500 dark:border-green-400")}>
                    <h3 className={cn(textStyles.h3, "mb-4")}>🔢 Conversion Results</h3>
                    <div className="space-y-3">
                      <div className={cn(statusStyles.info.container, "flex items-center justify-between p-3")}>
                        <div>
                          <div className={cn("font-medium", textStyles.body)}>Binary (Base 2)</div>
                          <div className={cn("text-sm", textStyles.muted)}>Digital systems</div>
                        </div>
                        <div className={cn("font-mono text-lg font-bold", statusStyles.info.text)}>{results.binary}</div>
                      </div>
                      
                      <div className={cn(statusStyles.warning.container, "flex items-center justify-between p-3")}>
                        <div>
                          <div className={cn("font-medium", textStyles.body)}>Octal (Base 8)</div>
                          <div className={cn("text-sm", textStyles.muted)}>Unix permissions</div>
                        </div>
                        <div className={cn("font-mono text-lg font-bold", statusStyles.warning.text)}>{results.octal}</div>
                      </div>
                      
                      <div className={cn(statusStyles.success.container, "flex items-center justify-between p-3")}>
                        <div>
                          <div className={cn("font-medium", textStyles.body)}>Decimal (Base 10)</div>
                          <div className={cn("text-sm", textStyles.muted)}>Human counting</div>
                        </div>
                        <div className={cn("font-mono text-lg font-bold", statusStyles.success.text)}>{results.decimal}</div>
                      </div>
                      
                      <div className={cn(statusStyles.purple.container, "flex items-center justify-between p-3")}>
                        <div>
                          <div className={cn("font-medium", textStyles.body)}>Hexadecimal (Base 16)</div>
                          <div className={cn("text-sm", textStyles.muted)}>Memory, colors</div>
                        </div>
                        <div className={cn("font-mono text-lg font-bold", statusStyles.purple.text)}>{results.hexadecimal}</div>
                      </div>
                      
                      <div className={cn(containerStyles.cardSmall, "flex items-center justify-between p-3")}>
                        <div>
                          <div className={cn("font-medium", textStyles.body)}>Base {toBase}</div>
                          <div className={cn("text-sm", textStyles.muted)}>Custom conversion</div>
                        </div>
                        <div className={cn("font-mono text-lg font-bold", textStyles.h3)}>{results.custom}</div>
                      </div>
                    </div>
                  </div>
                )}

                {/* Common Bases Info */}
                <div className={cn(statusStyles.info.container, "p-6")}>
                  <h3 className={cn(statusStyles.info.title, "mb-4")}>📊 Common Number Bases</h3>
                  <div className="space-y-3">
                    {commonBases.slice(0, 4).map((base) => (
                      <div key={base.value} className={cn(containerStyles.cardSmall, "p-3")}>
                        <div className="flex items-center justify-between">
                          <div>
                            <div className={cn("font-medium", textStyles.body)}>{base.label}</div>
                            <div className={cn("text-sm", textStyles.bodySmall)}>{base.description}</div>
                          </div>
                          <div className={cn("font-mono text-lg font-bold", statusStyles.info.text)}>{base.abbr}</div>
                        </div>
                      </div>
                    ))}
                  </div>
                </div>

                {/* Base Conversion Tips */}
                <div className={cn(statusStyles.success.container, "p-6")}>
                  <h3 className={cn(statusStyles.success.title, "mb-4")}>💡 Conversion Tips</h3>
                  <ul className={cn("space-y-2", statusStyles.success.text)}>
                    <li>• <strong>Binary:</strong> Use powers of 2 (1, 2, 4, 8, 16, 32...)</li>
                    <li>• <strong>Hexadecimal:</strong> Each digit represents 4 binary digits</li>
                    <li>• <strong>Octal:</strong> Each digit represents 3 binary digits</li>
                    <li>• <strong>Large bases:</strong> Use letters A-Z for digits 10-35</li>
                  </ul>
                </div>
              </div>
            </div>

            {/* Educational Content */}
            <div className="mt-12 space-y-8">
              {/* Why Different Bases */}
              <div className={cn(containerStyles.card, "p-8")}>
                <h2 className={cn(textStyles.h2, "mb-6")}>🤔 Why Do We Have Different Number Bases?</h2>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <div>
                    <h3 className={cn("text-lg font-semibold mb-3", textStyles.h3)}>Historical Reasons</h3>
                    <p className={cn("text-sm mb-3", textStyles.bodySmall)}>
                      Different cultures developed various counting systems based on available tools and natural patterns. 
                      Base 10 likely came from counting on fingers, while base 60 was used by Babylonians for astronomy.
                    </p>
                    <ul className={cn("text-sm space-y-1", textStyles.bodySmall)}>
                      <li>• <strong>Base 10:</strong> Ten fingers for counting</li>
                      <li>• <strong>Base 12:</strong> Finger segments (3 per finger × 4 fingers)</li>
                      <li>• <strong>Base 20:</strong> Fingers and toes combined</li>
                      <li>• <strong>Base 60:</strong> Babylonian astronomy and time</li>
                    </ul>
                  </div>
                  <div>
                    <h3 className={cn("text-lg font-semibold mb-3", textStyles.h3)}>Technical Advantages</h3>
                    <p className={cn("text-sm mb-3", textStyles.bodySmall)}>
                      Modern applications use different bases for efficiency and convenience in specific domains. 
                      Each base has unique properties that make certain calculations easier.
                    </p>
                    <ul className={cn("text-sm space-y-1", textStyles.bodySmall)}>
                      <li>• <strong>Binary:</strong> Matches digital on/off states</li>
                      <li>• <strong>Hexadecimal:</strong> Compact representation of binary</li>
                      <li>• <strong>Octal:</strong> Clean grouping of binary triplets</li>
                      <li>• <strong>Base 12:</strong> More divisors than base 10</li>
                    </ul>
                  </div>
                </div>
              </div>

              {/* Detailed Base Explanations */}
              <div className={cn(containerStyles.card, "p-8")}>
                <h2 className={cn(textStyles.h2, "mb-6")}>📚 Understanding Number Systems</h2>
                
                <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
                  {/* Binary System */}
                  <div className="space-y-4">
                    <div className={statusStyles.info.container}>
                      <h3 className={cn(statusStyles.info.title, "text-xl mb-2")}>Binary (Base 2)</h3>
                      <p className={cn(statusStyles.info.text, "text-sm mb-3")}>
                        The foundation of all digital technology. Uses only 0 and 1, representing off and on states in electronic circuits.
                      </p>
                      <div className="space-y-2">
                        <div className={statusStyles.info.text}><strong>Digits:</strong> 0, 1</div>
                        <div className={statusStyles.info.text}><strong>Example:</strong> 1010₂ = 10₁₀</div>
                        <div className={statusStyles.info.text}><strong>Uses:</strong></div>
                        <ul className={cn(statusStyles.info.textSmall, "mt-1 space-y-1")}>
                          <li>• Computer memory and processing</li>
                          <li>• Digital logic circuits</li>
                          <li>• Data storage and transmission</li>
                          <li>• Boolean algebra and programming</li>
                        </ul>
                      </div>
                    </div>

                    <div className={statusStyles.warning.container}>
                      <h3 className={cn(statusStyles.warning.title, "text-xl mb-2")}>Octal (Base 8)</h3>
                      <p className={cn(statusStyles.warning.text, "text-sm mb-3")}>
                        Popular in early computing and still used in Unix/Linux systems for file permissions.
                      </p>
                      <div className="space-y-2">
                        <div className={statusStyles.warning.text}><strong>Digits:</strong> 0-7</div>
                        <div className={statusStyles.warning.text}><strong>Example:</strong> 755₈ = 493₁₀</div>
                        <div className={statusStyles.warning.text}><strong>Uses:</strong></div>
                        <ul className={cn(statusStyles.warning.textSmall, "mt-1 space-y-1")}>
                          <li>• Unix/Linux file permissions</li>
                          <li>• Legacy computer systems</li>
                          <li>• Compact binary representation</li>
                          <li>• Assembly language programming</li>
                        </ul>
                      </div>
                    </div>
                  </div>

                  <div className="space-y-4">
                    <div className={statusStyles.success.container}>
                      <h3 className={cn(statusStyles.success.title, "text-xl mb-2")}>Decimal (Base 10)</h3>
                      <p className={cn(statusStyles.success.text, "text-sm mb-3")}>
                        The standard human counting system. Most natural for everyday mathematics and general calculations.
                      </p>
                      <div className="space-y-2">
                        <div className={statusStyles.success.text}><strong>Digits:</strong> 0-9</div>
                        <div className={statusStyles.success.text}><strong>Example:</strong> 42₁₀ = 42₁₀</div>
                        <div className={statusStyles.success.text}><strong>Uses:</strong></div>
                        <ul className={cn(statusStyles.success.textSmall, "mt-1 space-y-1")}>
                          <li>• Everyday counting and arithmetic</li>
                          <li>• Financial calculations</li>
                          <li>• Scientific measurements</li>
                          <li>• Human-readable displays</li>
                        </ul>
                      </div>
                    </div>

                    <div className={statusStyles.purple.container}>
                      <h3 className={cn(statusStyles.purple.title, "text-xl mb-2")}>Hexadecimal (Base 16)</h3>
                      <p className={cn(statusStyles.purple.text, "text-sm mb-3")}>
                        Extremely popular in computing for its compact representation of binary data.
                      </p>
                      <div className="space-y-2">
                        <div className={statusStyles.purple.text}><strong>Digits:</strong> 0-9, A-F</div>
                        <div className={statusStyles.purple.text}><strong>Example:</strong> FF₁₆ = 255₁₀</div>
                        <div className={statusStyles.purple.text}><strong>Uses:</strong></div>
                        <ul className={cn(statusStyles.purple.textSmall, "mt-1 space-y-1")}>
                          <li>• Memory addresses and pointers</li>
                          <li>• Color codes in web design (#FF0000)</li>
                          <li>• Assembly language and debugging</li>
                          <li>• Cryptography and hashing</li>
                        </ul>
                      </div>
                    </div>
                  </div>
                </div>
              </div>

              {/* Special Bases */}
              <div className={cn(statusStyles.purple.container, "p-6")}>
                <h2 className={cn(statusStyles.purple.title, "mb-4")}>🌟 Special Number Bases</h2>
                <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                  <div className={cn(containerStyles.cardSmall, "p-4")}>
                    <h3 className={cn("font-semibold mb-2", textStyles.h4)}>Base 12 (Duodecimal)</h3>
                    <p className={cn("text-sm mb-2", textStyles.bodySmall)}>
                      Has more divisors than base 10, making fractions easier.
                    </p>
                    <div className={cn("text-xs", textStyles.muted)}>
                      Used in: Clocks (12 hours), measurements (dozen, gross), angles (360° = 12×30°)
                    </div>
                  </div>
                  
                  <div className={cn(containerStyles.cardSmall, "p-4")}>
                    <h3 className={cn("font-semibold mb-2", textStyles.h4)}>Base 60 (Sexagesimal)</h3>
                    <p className={cn("text-sm mb-2", textStyles.bodySmall)}>
                      Ancient Babylonian system still used today.
                    </p>
                    <div className={cn("text-xs", textStyles.muted)}>
                      Used in: Time (60 minutes/seconds), angles (360°), geographic coordinates
                    </div>
                  </div>
                  
                  <div className={cn(containerStyles.cardSmall, "p-4")}>
                    <h3 className={cn("font-semibold mb-2", textStyles.h4)}>Base 36</h3>
                    <p className={cn("text-sm mb-2", textStyles.bodySmall)}>
                      Uses all digits and letters, compact for encoding.
                    </p>
                    <div className={cn("text-xs", textStyles.muted)}>
                      Used in: URL shorteners, license plates, compact identifiers
                    </div>
                  </div>
                </div>
              </div>

              {/* Practical Applications */}
              <div className={cn(statusStyles.danger.container, "p-6")}>
                <h2 className={cn(statusStyles.danger.title, "mb-4")}>🛠️ Real-World Applications</h2>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <div>
                    <h3 className={cn("font-semibold mb-2", textStyles.h4)}>Computer Science</h3>
                    <ul className={cn("text-sm space-y-1", textStyles.bodySmall)}>
                      <li>• <strong>Binary:</strong> All digital data storage and processing</li>
                      <li>• <strong>Hexadecimal:</strong> Memory dumps, machine code, color values</li>
                      <li>• <strong>Octal:</strong> File permissions in Unix/Linux systems</li>
                      <li>• <strong>Base64:</strong> Email encoding and data transmission</li>
                    </ul>
                  </div>
                  <div>
                    <h3 className={cn("font-semibold mb-2", textStyles.h4)}>Other Fields</h3>
                    <ul className={cn("text-sm space-y-1", textStyles.bodySmall)}>
                      <li>• <strong>Time:</strong> Base 60 for minutes and seconds</li>
                      <li>• <strong>Angles:</strong> Base 60 for degrees, minutes, seconds</li>
                      <li>• <strong>Commerce:</strong> Base 12 for dozens and gross</li>
                      <li>• <strong>Historical:</strong> Base 20 in Mayan mathematics</li>
                    </ul>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
        <Footer />
      </div>
    </>
  );
}