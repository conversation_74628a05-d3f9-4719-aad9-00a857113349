import { generatePageTitle, generateCanonicalUrl } from '@/lib/utils';
import type { Metadata } from 'next';

export const metadata: Metadata = {
  title: generatePageTitle('Number Base Converter'),
  description: 'Convert numbers between different bases including binary, octal, decimal, hexadecimal, and custom bases. Learn about number systems, their history, and practical applications in computing.',
  keywords: 'base converter, number base, binary converter, hexadecimal converter, octal converter, decimal converter, number system, radix conversion, computer science, programming',
  robots: 'index, follow',
  alternates: {
    canonical: generateCanonicalUrl('/base-converter'),
  },
  openGraph: {
    title: generatePageTitle('Number Base Converter'),
    description: 'Convert numbers between different bases including binary, octal, decimal, and hexadecimal. Educational tool for learning number systems.',
    type: 'website',
    url: generateCanonicalUrl('/base-converter'),
  },
  twitter: {
    card: 'summary',
    title: generatePageTitle('Number Base Converter'),
    description: 'Convert numbers between different bases including binary, octal, decimal, and hexadecimal. Educational tool for learning number systems.',
  },
};

export default function BaseConverterLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  return children;
}