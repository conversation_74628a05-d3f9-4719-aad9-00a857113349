'use client';

import { useState, useEffect, useCallback } from 'react';
import Footer from "@/components/Footer";
import CalculatorHeader from '@/components/CalculatorHeader';
import { containerStyles } from '@/components/ui/styles';

interface ScaleResult {
  realSize: number;
  scaleSize: number;
  scale: string;
  unit: string;
}

export default function ScaleConversionCalculator() {
  const [realSize, setRealSize] = useState<string>('100');
  const [scaleSize, setScaleSize] = useState<string>('');
  const [scaleRatio, setScaleRatio] = useState<string>('1:50');
  const [unit, setUnit] = useState<string>('mm');
  const [result, setResult] = useState<ScaleResult | null>(null);

  const units = [
    { value: 'mm', label: 'Millimeter (mm)' },
    { value: 'cm', label: 'Centimeter (cm)' },
    { value: 'm', label: 'Meter (m)' },
    { value: 'km', label: 'Kilometer (km)' },
    { value: 'in', label: 'Inch (in)' },
    { value: 'ft', label: 'Foot (ft)' },
    { value: 'yd', label: 'Yard (yd)' },
    { value: 'mi', label: 'Mile (mi)' }
  ];

  const commonScales = [
    '1:1', '1:2', '1:5', '1:10', '1:20', '1:25', '1:50', '1:75', '1:100', '1:200', '1:500'
  ];

  const parseScale = (scale: string): number => {
    const parts = scale.split(':');
    if (parts.length === 2) {
      const numerator = parseFloat(parts[0]);
      const denominator = parseFloat(parts[1]);
      return denominator / numerator;
    }
    return 1;
  };

  const convertToMM = (value: number, fromUnit: string): number => {
    const conversions: { [key: string]: number } = {
      'mm': 1,
      'cm': 10,
      'm': 1000,
      'km': 1000000,
      'in': 25.4,
      'ft': 304.8,
      'yd': 914.4,
      'mi': 1609344
    };
    return value * conversions[fromUnit];
  };

  const convertFromMM = (value: number, toUnit: string): number => {
    const conversions: { [key: string]: number } = {
      'mm': 1,
      'cm': 10,
      'm': 1000,
      'km': 1000000,
      'in': 25.4,
      'ft': 304.8,
      'yd': 914.4,
      'mi': 1609344
    };
    return value / conversions[toUnit];
  };

  const calculateScaleConversion = useCallback(() => {
    if (!realSize && !scaleSize) {
      setResult(null);
      return;
    }

    const scaleRatioNum = parseScale(scaleRatio);
    let calculatedResult: ScaleResult;

    if (realSize && !scaleSize) {
      // Calculate scale size from real size
      const realSizeNum = parseFloat(realSize);
      const realSizeMM = convertToMM(realSizeNum, unit);
      const scaleSizeMM = realSizeMM / scaleRatioNum;
      const scaleSizeConverted = convertFromMM(scaleSizeMM, unit);
      
      calculatedResult = {
        realSize: realSizeNum,
        scaleSize: parseFloat(scaleSizeConverted.toFixed(4)),
        scale: scaleRatio,
        unit: unit
      };
      setScaleSize(scaleSizeConverted.toFixed(4));
    } else if (scaleSize && !realSize) {
      // Calculate real size from scale size
      const scaleSizeNum = parseFloat(scaleSize);
      const scaleSizeMM = convertToMM(scaleSizeNum, unit);
      const realSizeMM = scaleSizeMM * scaleRatioNum;
      const realSizeConverted = convertFromMM(realSizeMM, unit);
      
      calculatedResult = {
        realSize: parseFloat(realSizeConverted.toFixed(4)),
        scaleSize: scaleSizeNum,
        scale: scaleRatio,
        unit: unit
      };
      setRealSize(realSizeConverted.toFixed(4));
    } else {
      // Both values provided, calculate scale
      const realSizeNum = parseFloat(realSize);
      const scaleSizeNum = parseFloat(scaleSize);
      const ratio = realSizeNum / scaleSizeNum;
      const calculatedScale = `1:${ratio.toFixed(0)}`;
      
      calculatedResult = {
        realSize: realSizeNum,
        scaleSize: scaleSizeNum,
        scale: calculatedScale,
        unit: unit
      };
      setScaleRatio(calculatedScale);
    }

    setResult(calculatedResult);
  }, [realSize, scaleSize, scaleRatio, unit]);

  useEffect(() => {
    calculateScaleConversion();
  }, [calculateScaleConversion]);

  const clearInputs = () => {
    setRealSize('');
    setScaleSize('');
    setResult(null);
  };

  return (
    <>
      <script
        type="application/ld+json"
        dangerouslySetInnerHTML={{
          __html: JSON.stringify({
            "@context": "https://schema.org",
            "@type": "WebApplication",
            "name": "Scale Conversion Calculator",
            "description": "Free online scale conversion calculator for converting between real size and scale size with different ratios and units. Perfect for architects, engineers, and model makers.",
            "applicationCategory": "UtilityApplication",
            "operatingSystem": "Web Browser",
            "url": "https://calc9.com/scale-conversion-calculator",
            "offers": {
              "@type": "Offer",
              "price": "0",
              "priceCurrency": "USD"
            },
            "featureList": [
              "Convert real size to scale size",
              "Convert scale size to real size",
              "Calculate scale ratios",
              "Support for 8 different units",
              "Common architectural scales",
              "Real-time calculations"
            ],
            "keywords": "scale converter, scale calculation, ratio calculator, architectural scale, engineering scale, model scale"
          })
        }}
      />
      <div className={containerStyles.page}>
        <div className="container mx-auto px-4">
          <div className="max-w-6xl mx-auto">
            <CalculatorHeader
              title="Scale Conversion Calculator"
              description="Convert between real size and scale size with different ratios and units. Perfect for architects, engineers, and model makers."
              category="Math"
              className="text-center mb-12"
            />

            <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
              {/* Calculator Section */}
              <div className="bg-white dark:bg-gray-800 rounded-lg shadow-lg dark:shadow-gray-700/50 p-6 border border-gray-200 dark:border-gray-700">
                <h2 className="text-2xl font-semibold text-gray-900 dark:text-white mb-6">Scale Conversion</h2>

                <div className="space-y-6">
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                      <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                        Real Size
                      </label>
                      <input
                        type="number"
                        value={realSize}
                        onChange={(e) => setRealSize(e.target.value)}
                        placeholder="Enter real size"
                        className="w-full px-3 py-2 bg-white dark:bg-gray-700 border border-gray-300 dark:border-gray-600 rounded-md text-gray-900 dark:text-white placeholder-gray-500 dark:placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-blue-500 dark:focus:ring-blue-400 focus:border-blue-500 dark:focus:border-blue-400 transition-colors"
                      />
                    </div>
                    <div>
                      <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                        Scale Size
                      </label>
                      <input
                        type="number"
                        value={scaleSize}
                        onChange={(e) => setScaleSize(e.target.value)}
                        placeholder="Enter scale size"
                        className="w-full px-3 py-2 bg-white dark:bg-gray-700 border border-gray-300 dark:border-gray-600 rounded-md text-gray-900 dark:text-white placeholder-gray-500 dark:placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-blue-500 dark:focus:ring-blue-400 focus:border-blue-500 dark:focus:border-blue-400 transition-colors"
                      />
                    </div>
                  </div>

                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                      <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                        Scale Ratio
                      </label>
                      <input
                        type="text"
                        value={scaleRatio}
                        onChange={(e) => setScaleRatio(e.target.value)}
                        placeholder="1:50"
                        className="w-full px-3 py-2 bg-white dark:bg-gray-700 border border-gray-300 dark:border-gray-600 rounded-md text-gray-900 dark:text-white placeholder-gray-500 dark:placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-blue-500 dark:focus:ring-blue-400 focus:border-blue-500 dark:focus:border-blue-400 transition-colors"
                      />
                    </div>
                    <div>
                      <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                        Unit
                      </label>
                      <select
                        value={unit}
                        onChange={(e) => setUnit(e.target.value)}
                        className="w-full px-3 py-2 bg-white dark:bg-gray-700 border border-gray-300 dark:border-gray-600 rounded-md text-gray-900 dark:text-white focus:outline-none focus:ring-2 focus:ring-blue-500 dark:focus:ring-blue-400 focus:border-blue-500 dark:focus:border-blue-400 transition-colors"
                      >
                        {units.map(u => (
                          <option key={u.value} value={u.value}>{u.label}</option>
                        ))}
                      </select>
                    </div>
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                      Common Scales
                    </label>
                    <div className="flex flex-wrap gap-2">
                      {commonScales.map(scale => (
                        <button
                          key={scale}
                          onClick={() => {
                            setScaleRatio(scale);
                            // Clear scale size to prevent auto-recalculation of scale ratio
                            setScaleSize('');
                          }}
                          className="px-3 py-1 bg-gray-200 dark:bg-gray-700 text-gray-700 dark:text-gray-300 rounded-md hover:bg-gray-300 dark:hover:bg-gray-600 transition-colors text-sm border border-gray-300 dark:border-gray-600"
                        >
                          {scale}
                        </button>
                      ))}
                    </div>
                  </div>

                  <button
                    onClick={clearInputs}
                    className="w-full bg-gray-600 dark:bg-gray-700 hover:bg-gray-700 dark:hover:bg-gray-600 text-white font-medium py-2 px-4 rounded-md transition-colors"
                  >
                    Clear All
                  </button>
                </div>
              </div>

              {/* Results and Information Section */}
              <div className="space-y-6">
                {result && (
                  <div className="bg-white dark:bg-gray-800 rounded-lg shadow-lg dark:shadow-gray-700/50 p-6 border-l-4 border-blue-500 dark:border-blue-400 border border-gray-200 dark:border-gray-700">
                    <h3 className="text-xl font-semibold text-gray-900 dark:text-white mb-4">
                      📏 Scale Conversion Result
                    </h3>
                    <div className="space-y-3">
                      <div className="flex justify-between items-center p-3 bg-gray-50 dark:bg-gray-700 rounded-lg border border-gray-200 dark:border-gray-600">
                        <span className="text-gray-600 dark:text-gray-300">Real Size:</span>
                        <span className="font-medium text-lg text-gray-900 dark:text-white">{result.realSize} {result.unit}</span>
                      </div>
                      <div className="flex justify-between items-center p-3 bg-gray-50 dark:bg-gray-700 rounded-lg border border-gray-200 dark:border-gray-600">
                        <span className="text-gray-600 dark:text-gray-300">Scale Size:</span>
                        <span className="font-medium text-lg text-gray-900 dark:text-white">{result.scaleSize} {result.unit}</span>
                      </div>
                      <div className="flex justify-between items-center p-3 bg-blue-50 dark:bg-blue-900/50 rounded-lg border border-blue-200 dark:border-blue-700">
                        <span className="text-gray-600 dark:text-gray-300">Scale Ratio:</span>
                        <span className="font-bold text-lg text-blue-600 dark:text-blue-300">{result.scale}</span>
                      </div>
                    </div>
                  </div>
                )}

                {/* Scale Information */}
                <div className="bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-700 rounded-lg p-6">
                  <h3 className="text-xl font-semibold text-blue-800 dark:text-blue-200 mb-4">📐 About Scale Conversion</h3>
                  <div className="space-y-3 text-blue-700 dark:text-blue-300">
                    <p className="text-sm">
                      <strong>Scale Ratio:</strong> A scale ratio like 1:50 means that 1 unit on the drawing represents 50 units in real life.
                    </p>
                    <p className="text-sm">
                      <strong>Architecture:</strong> Common scales include 1:100 (floor plans), 1:50 (detailed plans), 1:20 (construction details).
                    </p>
                    <p className="text-sm">
                      <strong>Engineering:</strong> Typical scales are 1:200, 1:500 for site plans, and 1:1 to 1:10 for detail drawings.
                    </p>
                    <p className="text-sm">
                      <strong>Model Making:</strong> Popular scales include 1:24, 1:32, 1:48, 1:72 for various model types.
                    </p>
                  </div>
                </div>

                {/* How to Use */}
                <div className="bg-green-50 dark:bg-green-900/20 border border-green-200 dark:border-green-700 rounded-lg p-6">
                  <h3 className="text-xl font-semibold text-green-800 dark:text-green-200 mb-4">❓ How to Use</h3>
                  <div className="space-y-2 text-green-700 dark:text-green-300">
                    <p className="text-sm">• Enter any two values to automatically calculate the third</p>
                    <p className="text-sm">• Choose from 8 different units (metric and imperial)</p>
                    <p className="text-sm">• Click preset scales for common architectural ratios</p>
                    <p className="text-sm">• Results update instantly as you type</p>
                    <p className="text-sm">• Use &quot;Clear All&quot; to start fresh</p>
                  </div>
                </div>

                {/* Applications */}
                <div className="bg-purple-50 dark:bg-purple-900/20 border border-purple-200 dark:border-purple-700 rounded-lg p-6">
                  <h3 className="text-xl font-semibold text-purple-800 dark:text-purple-200 mb-4">🎯 Applications</h3>
                  <div className="text-purple-700 dark:text-purple-300 space-y-2">
                    <p className="text-sm"><strong>Architecture:</strong> Floor plans, elevations, site plans, construction details</p>
                    <p className="text-sm"><strong>Engineering:</strong> Technical drawings, mechanical parts, schematics</p>
                    <p className="text-sm"><strong>Model Making:</strong> Scale models, miniatures, dollhouses</p>
                    <p className="text-sm"><strong>Design:</strong> Product design, furniture design, landscape architecture</p>
                    <p className="text-sm"><strong>Education:</strong> Teaching proportions, ratios, and spatial relationships</p>
                  </div>
                </div>

                {/* Examples */}
                <div className="bg-yellow-50 dark:bg-yellow-900/20 border border-yellow-200 dark:border-yellow-700 rounded-lg p-6">
                  <h3 className="text-xl font-semibold text-yellow-800 dark:text-yellow-200 mb-4">💡 Examples</h3>
                  <div className="text-yellow-700 dark:text-yellow-300 space-y-2">
                    <p className="text-sm"><strong>Example 1:</strong> A 5m wall at 1:100 scale = 5cm on paper</p>
                    <p className="text-sm"><strong>Example 2:</strong> A 2cm drawing at 1:50 scale = 1m in reality</p>
                    <p className="text-sm"><strong>Example 3:</strong> 10mm model car represents 240mm real car (1:24 scale)</p>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
        <Footer />
      </div>
    </>
  );
}