import { generatePageTitle, generateCanonicalUrl } from '@/lib/utils';

export const metadata = {
  title: generatePageTitle("Scale Conversion Calculator"),
  description: "Free online scale conversion calculator for converting between real size and scale size with different ratios and units. Perfect for architects, engineers, and model makers working with architectural drawings, technical plans, and scale models.",
  keywords: "scale converter, scale calculation, ratio calculator, proportion calculator, architectural scale, engineering scale, model scale, drawing scale, scale ratio, real size to scale size",
  robots: "index, follow",
  alternates: {
    canonical: generateCanonicalUrl("/scale-conversion-calculator"),
  },
  openGraph: {
    title: "Scale Conversion Calculator - Convert Between Different Scales",
    description: "Free online scale conversion calculator for converting between real size and scale size with different ratios and units.",
    type: 'website',
    url: generateCanonicalUrl("/scale-conversion-calculator"),
  },
  twitter: {
    card: 'summary_large_image',
    title: "Scale Conversion Calculator - Convert Between Different Scales",
    description: "Free online scale conversion calculator for converting between real size and scale size with different ratios and units.",
  },
};

export default function ScaleConversionCalculatorLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  return children;
}