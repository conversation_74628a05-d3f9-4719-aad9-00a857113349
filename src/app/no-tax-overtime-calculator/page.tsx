'use client';

import { useState, useEffect, useCallback, Suspense } from 'react';
import { useRouter, useSearchParams } from 'next/navigation';
import Footer from "@/components/Footer";
import CalculatorHeader from '@/components/CalculatorHeader';
import { containerStyles, textStyles, inputStyles, statusStyles, cn } from '@/components/ui/styles';

interface OvertimeCalculationResult {
  regularPay: number;
  overtimePay: number;
  overtimePremium: number;
  totalAnnualPay: number;
  taxableIncome: number;
  federalTaxWithoutDeduction: number;
  federalTaxWithDeduction: number;
  overtimeDeduction: number;
  actualDeduction: number;
  annualTaxSavings: number;
  effectiveTaxRate: number;
  marginalTaxRate: number;
  qualifiesForDeduction: boolean;
  phaseOutReduction: number;
}

function NoTaxOvertimeCalculatorContent() {
  const router = useRouter();
  const searchParams = useSearchParams();
  
  // Form inputs
  const [filingStatus, setFilingStatus] = useState<string>('single');
  const [regularHourlyRate, setRegularHourlyRate] = useState<string>('25');
  const [weeklyOvertimeHours, setWeeklyOvertimeHours] = useState<string>('10');
  const [weeksWorked, setWeeksWorked] = useState<string>('52');
  const [otherIncome, setOtherIncome] = useState<string>('0');
  const [standardDeduction, setStandardDeduction] = useState<string>('14600');
  
  // Results
  const [results, setResults] = useState<OvertimeCalculationResult>({
    regularPay: 0,
    overtimePay: 0,
    overtimePremium: 0,
    totalAnnualPay: 0,
    taxableIncome: 0,
    federalTaxWithoutDeduction: 0,
    federalTaxWithDeduction: 0,
    overtimeDeduction: 0,
    actualDeduction: 0,
    annualTaxSavings: 0,
    effectiveTaxRate: 0,
    marginalTaxRate: 0,
    qualifiesForDeduction: false,
    phaseOutReduction: 0
  });

  // URL state management
  const updateURL = useCallback((params: Record<string, string>) => {
    const newParams = new URLSearchParams(searchParams);
    Object.entries(params).forEach(([key, value]) => {
      if (value && value !== '') {
        newParams.set(key, value);
      } else {
        newParams.delete(key);
      }
    });
    router.push(`?${newParams.toString()}`, { scroll: false });
  }, [router, searchParams]);

  // Initialize from URL
  useEffect(() => {
    const filing = searchParams.get('filingStatus') || 'single';
    const rate = searchParams.get('regularHourlyRate') || '25';
    const overtime = searchParams.get('weeklyOvertimeHours') || '10';
    const weeks = searchParams.get('weeksWorked') || '52';
    const income = searchParams.get('otherIncome') || '0';
    const deduction = searchParams.get('standardDeduction') || '14600';

    setFilingStatus(filing);
    setRegularHourlyRate(rate);
    setWeeklyOvertimeHours(overtime);
    setWeeksWorked(weeks);
    setOtherIncome(income);
    setStandardDeduction(deduction);
  }, [searchParams]);

  // Tax brackets for 2025 (estimated)
  const getTaxBrackets = (filing: string) => {
    if (filing === 'marriedJoint') {
      return [
        { min: 0, max: 25550, rate: 0.10 },
        { min: 25550, max: 103800, rate: 0.12 },
        { min: 103800, max: 194150, rate: 0.22 },
        { min: 194150, max: 365600, rate: 0.24 },
        { min: 365600, max: 462500, rate: 0.32 },
        { min: 462500, max: 693750, rate: 0.35 },
        { min: 693750, max: Infinity, rate: 0.37 }
      ];
    } else { // single
      return [
        { min: 0, max: 12775, rate: 0.10 },
        { min: 12775, max: 51900, rate: 0.12 },
        { min: 51900, max: 97075, rate: 0.22 },
        { min: 97075, max: 182800, rate: 0.24 },
        { min: 182800, max: 231250, rate: 0.32 },
        { min: 231250, max: 346875, rate: 0.35 },
        { min: 346875, max: Infinity, rate: 0.37 }
      ];
    }
  };

  const calculateFederalTax = useCallback((taxableIncome: number, filing: string): number => {
    const brackets = getTaxBrackets(filing);
    let tax = 0;
    
    for (const bracket of brackets) {
      if (taxableIncome > bracket.min) {
        const taxableInThisBracket = Math.min(taxableIncome - bracket.min, bracket.max - bracket.min);
        tax += taxableInThisBracket * bracket.rate;
      }
    }
    
    return tax;
  }, []);

  const getMarginalTaxRate = useCallback((taxableIncome: number, filing: string): number => {
    const brackets = getTaxBrackets(filing);
    
    for (const bracket of brackets) {
      if (taxableIncome > bracket.min && taxableIncome <= bracket.max) {
        return bracket.rate;
      }
    }
    
    return brackets[brackets.length - 1].rate;
  }, []);

  const calculateOvertimeTax = useCallback(() => {
    const rate = parseFloat(regularHourlyRate) || 0;
    const overtimeHours = parseFloat(weeklyOvertimeHours) || 0;
    const weeks = parseFloat(weeksWorked) || 52;
    const otherIncomeValue = parseFloat(otherIncome) || 0;
    const standardDeductionValue = parseFloat(standardDeduction) || 14600;

    // Calculate basic pay components
    const regularWeeklyHours = 40;
    const annualRegularPay = rate * regularWeeklyHours * weeks;
    const annualOvertimePay = rate * 1.5 * overtimeHours * weeks;
    const annualOvertimePremium = rate * 0.5 * overtimeHours * weeks; // Only the premium portion
    const totalAnnualPay = annualRegularPay + annualOvertimePay + otherIncomeValue;

    // Calculate taxable income
    const taxableIncome = Math.max(0, totalAnnualPay - standardDeductionValue);
    
    // Calculate MAGI (simplified - using taxable income)
    const magi = totalAnnualPay;
    
    // Check if qualifies for deduction
    const magiThreshold = filingStatus === 'marriedJoint' ? 300000 : 150000;
    const qualifies = magi <= magiThreshold + 50000; // Phase-out range is $50k
    
    // Calculate deduction limits
    const maxDeduction = filingStatus === 'marriedJoint' ? 25000 : 12500;
    let overtimeDeduction = Math.min(annualOvertimePremium, maxDeduction);
    
    // Apply phase-out
    let phaseOutReduction = 0;
    if (magi > magiThreshold) {
      const excessIncome = magi - magiThreshold;
      const phaseOutRate = Math.min(1, excessIncome / 50000); // Phase out over $50k
      phaseOutReduction = overtimeDeduction * phaseOutRate;
      overtimeDeduction = Math.max(0, overtimeDeduction - phaseOutReduction);
    }

    // Calculate taxes
    const federalTaxWithoutDeduction = calculateFederalTax(taxableIncome, filingStatus);
    const adjustedTaxableIncome = Math.max(0, taxableIncome - overtimeDeduction);
    const federalTaxWithDeduction = calculateFederalTax(adjustedTaxableIncome, filingStatus);
    
    const annualTaxSavings = federalTaxWithoutDeduction - federalTaxWithDeduction;
    const effectiveTaxRate = totalAnnualPay > 0 ? (federalTaxWithDeduction / totalAnnualPay) * 100 : 0;
    const marginalTaxRate = getMarginalTaxRate(taxableIncome, filingStatus) * 100;

    setResults({
      regularPay: annualRegularPay,
      overtimePay: annualOvertimePay,
      overtimePremium: annualOvertimePremium,
      totalAnnualPay,
      taxableIncome,
      federalTaxWithoutDeduction,
      federalTaxWithDeduction,
      overtimeDeduction: annualOvertimePremium,
      actualDeduction: overtimeDeduction,
      annualTaxSavings,
      effectiveTaxRate,
      marginalTaxRate,
      qualifiesForDeduction: qualifies,
      phaseOutReduction
    });
  }, [regularHourlyRate, weeklyOvertimeHours, weeksWorked, otherIncome, standardDeduction, filingStatus, calculateFederalTax, getMarginalTaxRate]);

  useEffect(() => {
    calculateOvertimeTax();
  }, [calculateOvertimeTax]);

  const formatCurrency = (amount: number): string => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
      minimumFractionDigits: 0,
      maximumFractionDigits: 0,
    }).format(amount);
  };

  const formatPercent = (rate: number): string => {
    return `${rate.toFixed(1)}%`;
  };

  const handleInputChange = (field: string, value: string) => {
    switch (field) {
      case 'filingStatus':
        setFilingStatus(value);
        // Update standard deduction based on filing status
        const newStandardDeduction = value === 'marriedJoint' ? '29200' : '14600';
        setStandardDeduction(newStandardDeduction);
        updateURL({ filingStatus: value, standardDeduction: newStandardDeduction });
        break;
      case 'regularHourlyRate':
        setRegularHourlyRate(value);
        updateURL({ regularHourlyRate: value });
        break;
      case 'weeklyOvertimeHours':
        setWeeklyOvertimeHours(value);
        updateURL({ weeklyOvertimeHours: value });
        break;
      case 'weeksWorked':
        setWeeksWorked(value);
        updateURL({ weeksWorked: value });
        break;
      case 'otherIncome':
        setOtherIncome(value);
        updateURL({ otherIncome: value });
        break;
      case 'standardDeduction':
        setStandardDeduction(value);
        updateURL({ standardDeduction: value });
        break;
    }
  };

  return (
    <>
      <div className={containerStyles.page}>
        <div className="container mx-auto px-4 py-8">
          <div className="max-w-6xl mx-auto">
            <CalculatorHeader
              title="No Tax on Overtime Calculator"
              description="Calculate your potential tax savings under the No Tax on Overtime provision for 2025-2028. Estimate federal income tax deductions on overtime premium pay."
              category="Financial"
              className="text-center mb-12"
            />

            {/* Important Notice */}
            <div className={cn(statusStyles.warning.container, "mb-8")}>
              <div className="flex items-start space-x-3">
                <span className="text-yellow-600 dark:text-yellow-400 text-2xl">⚠️</span>
                <div>
                  <h4 className={cn("font-medium mb-2", statusStyles.warning.title)}>Important Disclaimer</h4>
                  <div className={cn("text-sm leading-relaxed", statusStyles.warning.text)}>
                    <p className="mb-2">
                      This calculator provides estimates only and should not be considered legal or financial advice. 
                      The No Tax on Overtime provision applies to tax years 2025-2028 and only affects federal income tax, 
                      not Social Security or Medicare taxes.
                    </p>
                    <p>
                      Consult a qualified tax professional for personalized advice. This tool is not affiliated with the IRS.
                    </p>
                  </div>
                </div>
              </div>
            </div>

            {/* Calculator Input Section */}
            <div className={cn(containerStyles.card, "p-8 mb-8")}>
              <h2 className={cn(textStyles.h2, "mb-6")}>Income and Tax Information</h2>
              <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
                
                {/* Left Column - Basic Information */}
                <div className="space-y-6">
                  <div>
                    <label className={cn(textStyles.label, "block mb-2")}>Filing Status</label>
                    <select
                      value={filingStatus}
                      onChange={(e) => handleInputChange('filingStatus', e.target.value)}
                      className={cn(inputStyles.base)}
                    >
                      <option value="single">Single</option>
                      <option value="marriedJoint">Married Filing Jointly</option>
                    </select>
                  </div>

                  <div>
                    <label className={cn(textStyles.label, "block mb-2")}>Regular Hourly Rate</label>
                    <div className="relative">
                      <span className="absolute left-3 top-1/2 -translate-y-1/2 text-gray-500">$</span>
                      <input
                        type="number"
                        value={regularHourlyRate}
                        onChange={(e) => handleInputChange('regularHourlyRate', e.target.value)}
                        className={cn(inputStyles.base, "pl-8")}
                        placeholder="25.00"
                        step="0.01"
                        min="0"
                      />
                    </div>
                  </div>

                  <div>
                    <label className={cn(textStyles.label, "block mb-2")}>Weekly Overtime Hours</label>
                    <input
                      type="number"
                      value={weeklyOvertimeHours}
                      onChange={(e) => handleInputChange('weeklyOvertimeHours', e.target.value)}
                      className={cn(inputStyles.base)}
                      placeholder="10"
                      step="0.5"
                      min="0"
                    />
                    <p className={cn("text-xs mt-1", textStyles.muted)}>
                      Hours worked over 40 per week
                    </p>
                  </div>
                </div>

                {/* Right Column - Additional Information */}
                <div className="space-y-6">
                  <div>
                    <label className={cn(textStyles.label, "block mb-2")}>Weeks Worked Per Year</label>
                    <input
                      type="number"
                      value={weeksWorked}
                      onChange={(e) => handleInputChange('weeksWorked', e.target.value)}
                      className={cn(inputStyles.base)}
                      placeholder="52"
                      step="1"
                      min="1"
                      max="52"
                    />
                  </div>

                  <div>
                    <label className={cn(textStyles.label, "block mb-2")}>Other Annual Income</label>
                    <div className="relative">
                      <span className="absolute left-3 top-1/2 -translate-y-1/2 text-gray-500">$</span>
                      <input
                        type="number"
                        value={otherIncome}
                        onChange={(e) => handleInputChange('otherIncome', e.target.value)}
                        className={cn(inputStyles.base, "pl-8")}
                        placeholder="0"
                        step="100"
                        min="0"
                      />
                    </div>
                    <p className={cn("text-xs mt-1", textStyles.muted)}>
                      Other income sources (salary, bonuses, etc.)
                    </p>
                  </div>

                  <div>
                    <label className={cn(textStyles.label, "block mb-2")}>Standard Deduction (2025)</label>
                    <div className="relative">
                      <span className="absolute left-3 top-1/2 -translate-y-1/2 text-gray-500">$</span>
                      <input
                        type="number"
                        value={standardDeduction}
                        onChange={(e) => handleInputChange('standardDeduction', e.target.value)}
                        className={cn(inputStyles.base, "pl-8")}
                        step="100"
                        min="0"
                      />
                    </div>
                    <p className={cn("text-xs mt-1", textStyles.muted)}>
                      Estimated 2025 standard deduction
                    </p>
                  </div>
                </div>
              </div>
            </div>

            {/* Results Section */}
            <div className={cn(containerStyles.card, "p-8 mb-8")}>
              <h2 className={cn(textStyles.h2, "mb-6")}>Calculation Results</h2>
              
              {/* Summary Cards */}
              <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8">
                <div className={cn("p-6 rounded-lg text-center", "bg-green-50 dark:bg-green-900/20 border border-green-200 dark:border-green-800")}>
                  <div className={cn("text-sm font-medium", textStyles.label)}>Annual Tax Savings</div>
                  <div className={cn("text-3xl font-bold mt-2", "text-green-600 dark:text-green-400")}>
                    {formatCurrency(results.annualTaxSavings)}
                  </div>
                </div>
                
                <div className={cn("p-6 rounded-lg text-center", "bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800")}>
                  <div className={cn("text-sm font-medium", textStyles.label)}>Overtime Deduction</div>
                  <div className={cn("text-3xl font-bold mt-2", "text-blue-600 dark:text-blue-400")}>
                    {formatCurrency(results.actualDeduction)}
                  </div>
                </div>
                
                <div className={cn("p-6 rounded-lg text-center", "bg-purple-50 dark:bg-purple-900/20 border border-purple-200 dark:border-purple-800")}>
                  <div className={cn("text-sm font-medium", textStyles.label)}>Effective Tax Rate</div>
                  <div className={cn("text-3xl font-bold mt-2", "text-purple-600 dark:text-purple-400")}>
                    {formatPercent(results.effectiveTaxRate)}
                  </div>
                </div>
              </div>

              {/* Detailed Breakdown */}
              <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
                <div>
                  <h3 className={cn(textStyles.h3, "mb-4")}>Income Breakdown</h3>
                  <div className="space-y-3">
                    {[
                      { label: 'Regular Pay (40 hrs/week)', value: results.regularPay },
                      { label: 'Total Overtime Pay', value: results.overtimePay },
                      { label: 'Overtime Premium (deductible)', value: results.overtimePremium },
                      { label: 'Other Income', value: parseFloat(otherIncome) || 0 },
                      { label: 'Total Annual Income', value: results.totalAnnualPay, highlight: true }
                    ].map((item, index) => (
                      <div key={index} className={cn(
                        "flex justify-between items-center p-3 rounded-lg",
                        item.highlight 
                          ? "bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-700 font-semibold"
                          : "bg-gray-50 dark:bg-gray-800"
                      )}>
                        <span className={textStyles.body}>{item.label}</span>
                        <span className={cn("font-medium", textStyles.body)}>{formatCurrency(item.value)}</span>
                      </div>
                    ))}
                  </div>
                </div>

                <div>
                  <h3 className={cn(textStyles.h3, "mb-4")}>Tax Calculation</h3>
                  <div className="space-y-3">
                    {[
                      { label: 'Taxable Income', value: results.taxableIncome },
                      { label: 'Federal Tax (without deduction)', value: results.federalTaxWithoutDeduction },
                      { label: 'Overtime Deduction Applied', value: results.actualDeduction },
                      { label: 'Federal Tax (with deduction)', value: results.federalTaxWithDeduction },
                      { label: 'Annual Tax Savings', value: results.annualTaxSavings, highlight: true }
                    ].map((item, index) => (
                      <div key={index} className={cn(
                        "flex justify-between items-center p-3 rounded-lg",
                        item.highlight 
                          ? "bg-green-50 dark:bg-green-900/20 border border-green-200 dark:border-green-700 font-semibold"
                          : "bg-gray-50 dark:bg-gray-800"
                      )}>
                        <span className={textStyles.body}>{item.label}</span>
                        <span className={cn("font-medium", textStyles.body)}>{formatCurrency(item.value)}</span>
                      </div>
                    ))}
                  </div>
                </div>
              </div>

              {/* Qualification Status */}
              <div className="mt-8">
                {results.qualifiesForDeduction ? (
                  <div className={cn(statusStyles.success.container)}>
                    <div className="flex items-center space-x-3">
                      <span className="text-2xl">✅</span>
                      <div>
                        <h4 className={cn("font-medium", statusStyles.success.title)}>Qualifies for Deduction</h4>
                        <p className={cn("text-sm", statusStyles.success.text)}>
                          Based on your income, you qualify for the overtime tax deduction.
                          {results.phaseOutReduction > 0 && (
                            <span className="block mt-1">
                              Note: Your deduction is reduced by {formatCurrency(results.phaseOutReduction)} due to income phase-out.
                            </span>
                          )}
                        </p>
                      </div>
                    </div>
                  </div>
                ) : (
                  <div className={cn(statusStyles.warning.container)}>
                    <div className="flex items-center space-x-3">
                      <span className="text-2xl">❌</span>
                      <div>
                        <h4 className={cn("font-medium", statusStyles.warning.title)}>Does Not Qualify</h4>
                        <p className={cn("text-sm", statusStyles.warning.text)}>
                          Your income exceeds the threshold for the overtime tax deduction.
                        </p>
                      </div>
                    </div>
                  </div>
                )}
              </div>
            </div>

            {/* Information Section */}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-8 mb-8">
              <div className={cn(containerStyles.card, "p-6")}>
                <h3 className={cn(textStyles.h3, "mb-4")}>How It Works</h3>
                <ul className={cn("space-y-3 text-sm", textStyles.body)}>
                  <li className="flex items-start space-x-2">
                    <span className="text-blue-500 mt-1">•</span>
                    <span>Only the <strong>premium portion</strong> of overtime pay is deductible (0.5x your regular rate)</span>
                  </li>
                  <li className="flex items-start space-x-2">
                    <span className="text-blue-500 mt-1">•</span>
                    <span>Maximum deduction: $12,500 (single) or $25,000 (married filing jointly)</span>
                  </li>
                  <li className="flex items-start space-x-2">
                    <span className="text-blue-500 mt-1">•</span>
                    <span>Deduction phases out for high earners ($150K+ single, $300K+ married)</span>
                  </li>
                  <li className="flex items-start space-x-2">
                    <span className="text-blue-500 mt-1">•</span>
                    <span>Available even if you take the standard deduction</span>
                  </li>
                </ul>
              </div>

              <div className={cn(containerStyles.card, "p-6")}>
                <h3 className={cn(textStyles.h3, "mb-4")}>Important Limitations</h3>
                <ul className={cn("space-y-3 text-sm", textStyles.body)}>
                  <li className="flex items-start space-x-2">
                    <span className="text-red-500 mt-1">•</span>
                    <span>Only applies to tax years 2025-2028</span>
                  </li>
                  <li className="flex items-start space-x-2">
                    <span className="text-red-500 mt-1">•</span>
                    <span>Does not affect Social Security or Medicare taxes</span>
                  </li>
                  <li className="flex items-start space-x-2">
                    <span className="text-red-500 mt-1">•</span>
                    <span>Federal income tax only - state taxes may still apply</span>
                  </li>
                  <li className="flex items-start space-x-2">
                    <span className="text-red-500 mt-1">•</span>
                    <span>This calculator provides estimates only</span>
                  </li>
                </ul>
              </div>
            </div>

            {/* External Resources */}
            <div className={cn(containerStyles.card, "p-6 mb-8")}>
              <h3 className={cn(textStyles.h3, "mb-4")}>Additional Resources</h3>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <a 
                  href="https://www.whitehouse.gov/obbb/" 
                  target="_blank" 
                  rel="noopener noreferrer"
                  className={cn("block p-4 rounded-lg border hover:border-blue-500 transition-colors", "bg-gray-50 dark:bg-gray-800 border-gray-200 dark:border-gray-700")}
                >
                  <h4 className={cn("font-medium mb-2", textStyles.body)}>White House Information</h4>
                  <p className={cn("text-sm", textStyles.muted)}>Official information about the One Big Beautiful Bill Act</p>
                  <span className="text-blue-500 text-sm">Learn more →</span>
                </a>
                
                <a 
                  href="https://www.bankrate.com/taxes/no-tax-on-tips-and-overtime-what-workers-should-know/" 
                  target="_blank" 
                  rel="noopener noreferrer"
                  className={cn("block p-4 rounded-lg border hover:border-blue-500 transition-colors", "bg-gray-50 dark:bg-gray-800 border-gray-200 dark:border-gray-700")}
                >
                  <h4 className={cn("font-medium mb-2", textStyles.body)}>Bankrate Analysis</h4>
                  <p className={cn("text-sm", textStyles.muted)}>What workers should know about overtime tax changes</p>
                  <span className="text-blue-500 text-sm">Read article →</span>
                </a>
              </div>
            </div>

          </div>
        </div>
      </div>
      <Footer />
    </>
  );
}

export default function NoTaxOvertimeCalculator() {
  return (
    <Suspense fallback={<div>Loading...</div>}>
      <NoTaxOvertimeCalculatorContent />
    </Suspense>
  );
}