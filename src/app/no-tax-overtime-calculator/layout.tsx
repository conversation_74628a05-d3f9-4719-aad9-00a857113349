import type { Metadata } from 'next';

export const metadata: Metadata = {
  title: 'No Tax on Overtime Calculator - Federal Tax Savings Estimator | CalcHub',
  description: 'Calculate your potential tax savings under the No Tax on Overtime provision (2025-2028). Estimate federal income tax deductions on overtime premium pay with income phase-out calculations.',
  keywords: 'no tax on overtime calculator, overtime tax deduction, federal tax savings, overtime premium, tax calculator 2025, one big beautiful bill act, overtime tax exemption',
  openGraph: {
    title: 'No Tax on Overtime Calculator - Federal Tax Savings Estimator',
    description: 'Calculate your potential tax savings under the No Tax on Overtime provision (2025-2028). Estimate federal income tax deductions on overtime premium pay.',
    type: 'website',
  },
};

export default function NoTaxOvertimeCalculatorLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  return children;
}