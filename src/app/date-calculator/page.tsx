'use client';

import { useState, useEffect, useCallback } from 'react';
import Footer from "@/components/Footer";
import CalculatorHeader from '@/components/CalculatorHeader';
import { containerStyles } from '@/components/ui/styles';

interface DateResult {
  years: number;
  months: number;
  days: number;
  totalDays: number;
  totalWeeks: number;
  totalHours: number;
  businessDays: number;
  weekends: number;
  resultDate?: string;
  dayOfWeek?: string;
}

export default function DateCalculator() {
  const [calculatorType, setCalculatorType] = useState<'difference' | 'add' | 'subtract'>('difference');
  
  // Date difference calculator
  const [startDate, setStartDate] = useState<string>('');
  const [endDate, setEndDate] = useState<string>('');
  
  // Add/Subtract calculator
  const [baseDate, setBaseDate] = useState<string>('');
  const [addYears, setAddYears] = useState<number>(0);
  const [addMonths, setAddMonths] = useState<number>(0);
  const [addDays, setAddDays] = useState<number>(0);
  const [excludeWeekends, setExcludeWeekends] = useState<boolean>(false);
  
  const [result, setResult] = useState<DateResult | null>(null);

  const calculateBusinessDays = useCallback((start: Date, end: Date): number => {
    let count = 0;
    const current = new Date(start);
    
    while (current <= end) {
      const dayOfWeek = current.getDay();
      if (dayOfWeek !== 0 && dayOfWeek !== 6) { // Not Sunday (0) or Saturday (6)
        count++;
      }
      current.setDate(current.getDate() + 1);
    }
    
    return count;
  }, []);

  const calculateWeekends = useCallback((start: Date, end: Date): number => {
    let count = 0;
    const current = new Date(start);
    
    while (current <= end) {
      const dayOfWeek = current.getDay();
      if (dayOfWeek === 0 || dayOfWeek === 6) { // Sunday (0) or Saturday (6)
        count++;
      }
      current.setDate(current.getDate() + 1);
    }
    
    return count;
  }, []);

  const calculateDateDifference = useCallback(() => {
    if (!startDate || !endDate) return null;

    const start = new Date(startDate);
    const end = new Date(endDate);
    
    if (start > end) return null;

    // Calculate years, months, days
    let years = end.getFullYear() - start.getFullYear();
    let months = end.getMonth() - start.getMonth();
    let days = end.getDate() - start.getDate();

    if (days < 0) {
      months--;
      const lastMonthDate = new Date(end.getFullYear(), end.getMonth(), 0);
      days += lastMonthDate.getDate();
    }

    if (months < 0) {
      years--;
      months += 12;
    }

    // Calculate totals
    const totalMs = end.getTime() - start.getTime();
    const totalDays = Math.floor(totalMs / (1000 * 60 * 60 * 24));
    const totalWeeks = Number((totalDays / 7).toFixed(1));
    const totalHours = Math.floor(totalMs / (1000 * 60 * 60));

    const businessDays = calculateBusinessDays(start, end);
    const weekends = calculateWeekends(start, end);

    return {
      years,
      months,
      days,
      totalDays,
      totalWeeks,
      totalHours,
      businessDays,
      weekends
    };
  }, [startDate, endDate, calculateBusinessDays, calculateWeekends]);

  const addBusinessDays = useCallback((date: Date, days: number): Date => {
    const result = new Date(date);
    let addedDays = 0;
    
    while (addedDays < days) {
      result.setDate(result.getDate() + 1);
      const dayOfWeek = result.getDay();
      if (dayOfWeek !== 0 && dayOfWeek !== 6) { // Not weekend
        addedDays++;
      }
    }
    
    return result;
  }, []);

  const subtractBusinessDays = useCallback((date: Date, days: number): Date => {
    const result = new Date(date);
    let subtractedDays = 0;
    
    while (subtractedDays < days) {
      result.setDate(result.getDate() - 1);
      const dayOfWeek = result.getDay();
      if (dayOfWeek !== 0 && dayOfWeek !== 6) { // Not weekend
        subtractedDays++;
      }
    }
    
    return result;
  }, []);

  const calculateAddSubtract = useCallback(() => {
    if (!baseDate) return null;

    const base = new Date(baseDate);
    let resultDate: Date;

    if (excludeWeekends && addDays > 0 && addYears === 0 && addMonths === 0) {
      // Special handling for business days
      if (calculatorType === 'add') {
        resultDate = addBusinessDays(base, addDays);
      } else {
        resultDate = subtractBusinessDays(base, addDays);
      }
    } else {
      // Regular date calculation
      resultDate = new Date(base);
      
      if (calculatorType === 'add') {
        resultDate.setFullYear(resultDate.getFullYear() + addYears);
        resultDate.setMonth(resultDate.getMonth() + addMonths);
        resultDate.setDate(resultDate.getDate() + addDays);
      } else {
        resultDate.setFullYear(resultDate.getFullYear() - addYears);
        resultDate.setMonth(resultDate.getMonth() - addMonths);
        resultDate.setDate(resultDate.getDate() - addDays);
      }
    }

    const dayOfWeek = resultDate.toLocaleDateString('en-US', { weekday: 'long' });
    const formattedDate = resultDate.toISOString().split('T')[0];

    // Calculate difference from base date for additional info
    const diffMs = Math.abs(resultDate.getTime() - base.getTime());
    const totalDays = Math.floor(diffMs / (1000 * 60 * 60 * 24));
    const totalWeeks = Number((totalDays / 7).toFixed(1));
    const totalHours = Math.floor(diffMs / (1000 * 60 * 60));

    return {
      years: 0,
      months: 0,
      days: 0,
      totalDays,
      totalWeeks,
      totalHours,
      businessDays: 0,
      weekends: 0,
      resultDate: formattedDate,
      dayOfWeek
    };
  }, [baseDate, addYears, addMonths, addDays, excludeWeekends, calculatorType, addBusinessDays, subtractBusinessDays]);

  const handleCalculate = useCallback(() => {
    let dateResult;
    
    if (calculatorType === 'difference') {
      dateResult = calculateDateDifference();
    } else {
      dateResult = calculateAddSubtract();
    }
    
    setResult(dateResult);
  }, [calculatorType, calculateDateDifference, calculateAddSubtract]);

  useEffect(() => {
    handleCalculate();
  }, [handleCalculate]);

  useEffect(() => {
    // Set default dates
    const today = new Date();
    const nextWeek = new Date(today);
    nextWeek.setDate(today.getDate() + 7);
    
    setStartDate(today.toISOString().split('T')[0]);
    setEndDate(nextWeek.toISOString().split('T')[0]);
    setBaseDate(today.toISOString().split('T')[0]);
  }, []);

  return (
    <>
      <div className={containerStyles.page}>
        <div className="container mx-auto px-4">
          <div className="max-w-6xl mx-auto">
            <CalculatorHeader
              title="Date Calculator"
              description="Calculate date differences, add or subtract time periods from dates, and work with business days. Perfect for project planning, scheduling, and date calculations."
              category="Other"
              className="text-center mb-12"
            />

            {/* Calculator Type Toggle */}
            <div className="flex justify-center mb-8">
              <div className="bg-white dark:bg-gray-800 rounded-lg p-1 flex border border-gray-200 dark:border-gray-700 shadow-sm">
                <button
                  onClick={() => setCalculatorType('difference')}
                  className={`px-4 py-2 rounded-md font-medium transition-colors ${
                    calculatorType === 'difference'
                      ? 'bg-blue-500 dark:bg-blue-600 text-white shadow-md'
                      : 'text-gray-600 dark:text-gray-300 hover:text-gray-800 dark:hover:text-white hover:bg-gray-50 dark:hover:bg-gray-700'
                  }`}
                >
                  Date Difference
                </button>
                <button
                  onClick={() => setCalculatorType('add')}
                  className={`px-4 py-2 rounded-md font-medium transition-colors ${
                    calculatorType === 'add'
                      ? 'bg-green-500 dark:bg-green-600 text-white shadow-md'
                      : 'text-gray-600 dark:text-gray-300 hover:text-gray-800 dark:hover:text-white hover:bg-gray-50 dark:hover:bg-gray-700'
                  }`}
                >
                  Add Time
                </button>
                <button
                  onClick={() => setCalculatorType('subtract')}
                  className={`px-4 py-2 rounded-md font-medium transition-colors ${
                    calculatorType === 'subtract'
                      ? 'bg-red-500 dark:bg-red-600 text-white shadow-md'
                      : 'text-gray-600 dark:text-gray-300 hover:text-gray-800 dark:hover:text-white hover:bg-gray-50 dark:hover:bg-gray-700'
                  }`}
                >
                  Subtract Time
                </button>
              </div>
            </div>

            <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
              {/* Calculator Section */}
              <div className="bg-white dark:bg-gray-800 rounded-lg shadow-lg dark:shadow-gray-700/50 p-6 border border-gray-200 dark:border-gray-700">
                <h2 className="text-2xl font-semibold text-gray-900 dark:text-white mb-6">
                  {calculatorType === 'difference' && 'Calculate Date Difference'}
                  {calculatorType === 'add' && 'Add Time to Date'}
                  {calculatorType === 'subtract' && 'Subtract Time from Date'}
                </h2>

                <div className="space-y-6">
                  {calculatorType === 'difference' ? (
                    <>
                      {/* Date Difference Calculator */}
                      <div>
                        <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                          Start Date
                        </label>
                        <input
                          type="date"
                          value={startDate}
                          onChange={(e) => setStartDate(e.target.value)}
                          className="w-full px-3 py-2 bg-white dark:bg-gray-700 border border-gray-300 dark:border-gray-600 rounded-md text-gray-900 dark:text-white focus:outline-none focus:ring-2 focus:ring-blue-500 dark:focus:ring-blue-400 focus:border-blue-500 dark:focus:border-blue-400 transition-colors"
                        />
                      </div>

                      <div>
                        <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                          End Date
                        </label>
                        <input
                          type="date"
                          value={endDate}
                          onChange={(e) => setEndDate(e.target.value)}
                          min={startDate}
                          className="w-full px-3 py-2 bg-white dark:bg-gray-700 border border-gray-300 dark:border-gray-600 rounded-md text-gray-900 dark:text-white focus:outline-none focus:ring-2 focus:ring-blue-500 dark:focus:ring-blue-400 focus:border-blue-500 dark:focus:border-blue-400 transition-colors"
                        />
                      </div>
                    </>
                  ) : (
                    <>
                      {/* Add/Subtract Date Calculator */}
                      <div>
                        <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                          Base Date
                        </label>
                        <input
                          type="date"
                          value={baseDate}
                          onChange={(e) => setBaseDate(e.target.value)}
                          className="w-full px-3 py-2 bg-white dark:bg-gray-700 border border-gray-300 dark:border-gray-600 rounded-md text-gray-900 dark:text-white focus:outline-none focus:ring-2 focus:ring-blue-500 dark:focus:ring-blue-400 focus:border-blue-500 dark:focus:border-blue-400 transition-colors"
                        />
                      </div>

                      <div className="grid grid-cols-3 gap-4">
                        <div>
                          <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                            Years
                          </label>
                          <input
                            type="number"
                            min="0"
                            value={addYears}
                            onChange={(e) => setAddYears(Number(e.target.value))}
                            className="w-full px-3 py-2 bg-white dark:bg-gray-700 border border-gray-300 dark:border-gray-600 rounded-md text-gray-900 dark:text-white focus:outline-none focus:ring-2 focus:ring-blue-500 dark:focus:ring-blue-400 focus:border-blue-500 dark:focus:border-blue-400 transition-colors"
                          />
                        </div>
                        <div>
                          <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                            Months
                          </label>
                          <input
                            type="number"
                            min="0"
                            value={addMonths}
                            onChange={(e) => setAddMonths(Number(e.target.value))}
                            className="w-full px-3 py-2 bg-white dark:bg-gray-700 border border-gray-300 dark:border-gray-600 rounded-md text-gray-900 dark:text-white focus:outline-none focus:ring-2 focus:ring-blue-500 dark:focus:ring-blue-400 focus:border-blue-500 dark:focus:border-blue-400 transition-colors"
                          />
                        </div>
                        <div>
                          <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                            Days
                          </label>
                          <input
                            type="number"
                            min="0"
                            value={addDays}
                            onChange={(e) => setAddDays(Number(e.target.value))}
                            className="w-full px-3 py-2 bg-white dark:bg-gray-700 border border-gray-300 dark:border-gray-600 rounded-md text-gray-900 dark:text-white focus:outline-none focus:ring-2 focus:ring-blue-500 dark:focus:ring-blue-400 focus:border-blue-500 dark:focus:border-blue-400 transition-colors"
                          />
                        </div>
                      </div>

                      <div>
                        <label className="flex items-center text-gray-700 dark:text-gray-300">
                          <input
                            type="checkbox"
                            checked={excludeWeekends}
                            onChange={(e) => setExcludeWeekends(e.target.checked)}
                            className="mr-2 text-blue-600 dark:text-blue-400 focus:ring-blue-500 dark:focus:ring-blue-400 rounded"
                          />
                          <span className="text-sm font-medium">
                            Business days only (exclude weekends)
                          </span>
                        </label>
                        <p className="text-xs text-gray-500 dark:text-gray-400 mt-1">
                          Only applies when adding/subtracting days without years or months
                        </p>
                      </div>
                    </>
                  )}

                  {/* Quick Preset Buttons */}
                  <div className="border-t border-gray-200 dark:border-gray-600 pt-4">
                    <h3 className="text-sm font-medium text-gray-700 dark:text-gray-300 mb-3">Quick Presets</h3>
                    <div className="grid grid-cols-2 gap-2">
                      {calculatorType === 'difference' ? (
                        <>
                          <button
                            onClick={() => {
                              const today = new Date();
                              const nextMonth = new Date(today);
                              nextMonth.setMonth(today.getMonth() + 1);
                              setStartDate(today.toISOString().split('T')[0]);
                              setEndDate(nextMonth.toISOString().split('T')[0]);
                            }}
                            className="text-left p-2 bg-gray-100 dark:bg-gray-700 border border-gray-300 dark:border-gray-600 rounded hover:bg-gray-200 dark:hover:bg-gray-600 text-sm transition-colors"
                          >
                            <div className="font-medium text-gray-800 dark:text-gray-200">Next Month</div>
                            <div className="text-gray-600 dark:text-gray-400">Today to next month</div>
                          </button>
                          <button
                            onClick={() => {
                              const today = new Date();
                              const nextYear = new Date(today);
                              nextYear.setFullYear(today.getFullYear() + 1);
                              setStartDate(today.toISOString().split('T')[0]);
                              setEndDate(nextYear.toISOString().split('T')[0]);
                            }}
                            className="text-left p-2 bg-gray-100 dark:bg-gray-700 border border-gray-300 dark:border-gray-600 rounded hover:bg-gray-200 dark:hover:bg-gray-600 text-sm transition-colors"
                          >
                            <div className="font-medium text-gray-800 dark:text-gray-200">Next Year</div>
                            <div className="text-gray-600 dark:text-gray-400">Today to next year</div>
                          </button>
                        </>
                      ) : (
                        <>
                          <button
                            onClick={() => {
                              setAddYears(0);
                              setAddMonths(0);
                              setAddDays(7);
                            }}
                            className="text-left p-2 bg-gray-100 dark:bg-gray-700 border border-gray-300 dark:border-gray-600 rounded hover:bg-gray-200 dark:hover:bg-gray-600 text-sm transition-colors"
                          >
                            <div className="font-medium text-gray-800 dark:text-gray-200">1 Week</div>
                          </button>
                          <button
                            onClick={() => {
                              setAddYears(0);
                              setAddMonths(1);
                              setAddDays(0);
                            }}
                            className="text-left p-2 bg-gray-100 dark:bg-gray-700 border border-gray-300 dark:border-gray-600 rounded hover:bg-gray-200 dark:hover:bg-gray-600 text-sm transition-colors"
                          >
                            <div className="font-medium text-gray-800 dark:text-gray-200">1 Month</div>
                          </button>
                          <button
                            onClick={() => {
                              setAddYears(0);
                              setAddMonths(0);
                              setAddDays(30);
                            }}
                            className="text-left p-2 bg-gray-100 dark:bg-gray-700 border border-gray-300 dark:border-gray-600 rounded hover:bg-gray-200 dark:hover:bg-gray-600 text-sm transition-colors"
                          >
                            <div className="font-medium text-gray-800 dark:text-gray-200">30 Days</div>
                          </button>
                          <button
                            onClick={() => {
                              setAddYears(1);
                              setAddMonths(0);
                              setAddDays(0);
                            }}
                            className="text-left p-2 bg-gray-100 dark:bg-gray-700 border border-gray-300 dark:border-gray-600 rounded hover:bg-gray-200 dark:hover:bg-gray-600 text-sm transition-colors"
                          >
                            <div className="font-medium text-gray-800 dark:text-gray-200">1 Year</div>
                          </button>
                        </>
                      )}
                    </div>
                  </div>
                </div>
              </div>

              {/* Results Section */}
              <div className="space-y-6">
                {result && (
                  <>
                    {calculatorType === 'difference' ? (
                      <div className="bg-white dark:bg-gray-800 rounded-lg shadow-lg dark:shadow-gray-700/50 p-6 border-l-4 border-blue-500 dark:border-blue-400 border border-gray-200 dark:border-gray-700">
                        <h3 className="text-xl font-semibold text-gray-900 dark:text-white mb-4">📅 Date Difference</h3>
                        
                        <div className="text-center mb-4">
                          <div className="text-3xl font-bold text-blue-600 dark:text-blue-400 mb-2">
                            {result.years > 0 && `${result.years} years`}
                            {result.months > 0 && ` ${result.months} months`}
                            {result.days > 0 && ` ${result.days} days`}
                            {result.years === 0 && result.months === 0 && result.days === 0 && 'Same day'}
                          </div>
                        </div>

                        <div className="space-y-3">
                          <div className="flex justify-between items-center p-3 bg-blue-50 dark:bg-blue-900/50 rounded border border-blue-200 dark:border-blue-700">
                            <span className="font-medium text-blue-800 dark:text-blue-200">Total Days</span>
                            <span className="text-lg font-bold text-blue-600 dark:text-blue-300">{result.totalDays.toLocaleString()}</span>
                          </div>
                          <div className="flex justify-between items-center p-3 bg-green-50 dark:bg-green-900/50 rounded border border-green-200 dark:border-green-700">
                            <span className="font-medium text-green-800 dark:text-green-200">Total Weeks</span>
                            <span className="text-lg font-bold text-green-600 dark:text-green-300">{result.totalWeeks}</span>
                          </div>
                          <div className="flex justify-between items-center p-3 bg-purple-50 dark:bg-purple-900/50 rounded border border-purple-200 dark:border-purple-700">
                            <span className="font-medium text-purple-800 dark:text-purple-200">Business Days</span>
                            <span className="text-lg font-bold text-purple-600 dark:text-purple-300">{result.businessDays.toLocaleString()}</span>
                          </div>
                          <div className="flex justify-between items-center p-3 bg-orange-50 dark:bg-orange-900/50 rounded border border-orange-200 dark:border-orange-700">
                            <span className="font-medium text-orange-800 dark:text-orange-200">Weekends</span>
                            <span className="text-lg font-bold text-orange-600 dark:text-orange-300">{result.weekends.toLocaleString()}</span>
                          </div>
                        </div>
                      </div>
                    ) : (
                      <div className="bg-white dark:bg-gray-800 rounded-lg shadow-lg dark:shadow-gray-700/50 p-6 border-l-4 border-green-500 dark:border-green-400 border border-gray-200 dark:border-gray-700">
                        <h3 className="text-xl font-semibold text-gray-900 dark:text-white mb-4">
                          {calculatorType === 'add' ? '➕ Result Date' : '➖ Result Date'}
                        </h3>
                        
                        <div className="text-center mb-4">
                          <div className="text-3xl font-bold text-green-600 dark:text-green-400 mb-2">
                            {result.resultDate && new Date(result.resultDate).toLocaleDateString('en-US', {
                              year: 'numeric',
                              month: 'long',
                              day: 'numeric'
                            })}
                          </div>
                          <div className="text-lg text-gray-600 dark:text-gray-300">
                            {result.dayOfWeek}
                          </div>
                        </div>

                        <div className="grid grid-cols-2 gap-4 text-sm">
                          <div className="text-center p-3 bg-green-50 dark:bg-green-900/50 rounded border border-green-200 dark:border-green-700">
                            <div className="font-semibold text-green-800 dark:text-green-200">Days {calculatorType === 'add' ? 'Added' : 'Subtracted'}</div>
                            <div className="text-green-600 dark:text-green-300">{result.totalDays}</div>
                          </div>
                          <div className="text-center p-3 bg-blue-50 dark:bg-blue-900/50 rounded border border-blue-200 dark:border-blue-700">
                            <div className="font-semibold text-blue-800 dark:text-blue-200">Weeks</div>
                            <div className="text-blue-600 dark:text-blue-300">{result.totalWeeks}</div>
                          </div>
                        </div>
                      </div>
                    )}

                    {/* Calendar Information */}
                    <div className="bg-indigo-50 dark:bg-indigo-900/20 border border-indigo-200 dark:border-indigo-700 rounded-lg p-6">
                      <h3 className="text-xl font-semibold text-indigo-800 dark:text-indigo-200 mb-4">📊 Calendar Facts</h3>
                      <div className="space-y-2 text-sm text-indigo-700 dark:text-indigo-300">
                        <div className="flex justify-between">
                          <span>Days in a year</span>
                          <span>365 (366 in leap years)</span>
                        </div>
                        <div className="flex justify-between">
                          <span>Business days per year</span>
                          <span>~261 days</span>
                        </div>
                        <div className="flex justify-between">
                          <span>Weekends per year</span>
                          <span>~104 days</span>
                        </div>
                        <div className="flex justify-between">
                          <span>Quarters in a year</span>
                          <span>4 quarters (~91 days each)</span>
                        </div>
                      </div>
                    </div>

                    {/* Usage Tips */}
                    <div className="bg-yellow-50 dark:bg-yellow-900/20 border border-yellow-200 dark:border-yellow-700 rounded-lg p-6">
                      <h3 className="text-xl font-semibold text-yellow-800 dark:text-yellow-200 mb-4">💡 Usage Tips</h3>
                      <ul className="space-y-2 text-yellow-700 dark:text-yellow-300 text-sm">
                        <li>• Use date difference to calculate project durations</li>
                        <li>• Add time to find deadlines and due dates</li>
                        <li>• Subtract time to find start dates for projects</li>
                        <li>• Business days exclude weekends (Saturday & Sunday)</li>
                        <li>• Perfect for vacation planning and scheduling</li>
                      </ul>
                    </div>
                  </>
                )}
              </div>
            </div>
          </div>
        </div>
        <Footer />
      </div>
    </>
  );
}