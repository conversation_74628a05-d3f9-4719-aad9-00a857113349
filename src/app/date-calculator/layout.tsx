import { generatePageTitle, generateCanonicalUrl } from '@/lib/utils';
import type { Metadata } from 'next';

export const metadata: Metadata = {
  title: generatePageTitle('Date Calculator'),
  description: 'Calculate date differences, add or subtract days/weeks/months/years from dates. Professional date calculation and planning tool.',
  keywords: 'date calculator, date difference, add days, subtract days, date math, calendar calculator, date planning, business days calculator',
  robots: 'index, follow',
  alternates: {
    canonical: generateCanonicalUrl('/date-calculator'),
  },
  openGraph: {
    title: generatePageTitle('Date Calculator'),
    description: 'Calculate date differences, add or subtract days/weeks/months/years from dates. Professional date calculation tool.',
    type: 'website',
    url: generateCanonicalUrl('/date-calculator'),
  },
  twitter: {
    card: 'summary',
    title: generatePageTitle('Date Calculator'),
    description: 'Calculate date differences, add or subtract days/weeks/months/years from dates. Professional date calculation tool.',
  },
};

export default function DateCalculatorLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  return children;
}