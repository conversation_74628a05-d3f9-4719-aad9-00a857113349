import Footer from '@/components/Footer';
import Logo from '@/components/Logo';
import { containerStyles } from '@/components/ui/styles';
import Link from 'next/link';

export default function About() {
  return (
    <div className={containerStyles.page}>
      <div className="container mx-auto px-4 py-8">
        <div className="max-w-4xl mx-auto">
          <header className="text-center mb-12">
            <Link href="/" className="text-blue-600 dark:text-blue-400 hover:text-blue-700 dark:hover:text-blue-300 mb-4 inline-flex items-center transition-colors">
              <svg className="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M10 19l-7-7m0 0l7-7m-7 7h18" />
              </svg>
              Back to Home
            </Link>
            <div className="flex justify-center items-center mb-6">
              <Logo
                width={48}
                height={48}
                className="h-12 w-auto mr-3"
              />
              <h1 className="text-4xl md:text-5xl font-bold text-gray-900 dark:text-white">
                About Calc9
              </h1>
            </div>
            <p className="text-xl text-gray-600 dark:text-blue-100 max-w-2xl mx-auto">
              Professional calculator tools designed for accuracy, speed, and convenience.
            </p>
          </header>

          <main className="space-y-12">
            {/* Mission Section */}
            <section className="bg-white dark:bg-gray-800 rounded-lg shadow-md dark:shadow-xl p-8 border border-gray-200 dark:border-gray-700">
              <h2 className="text-2xl font-bold text-gray-900 dark:text-white mb-6 flex items-center">
                <svg className="w-6 h-6 text-blue-600 dark:text-blue-400 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 10V3L4 14h7v7l9-11h-7z" />
                </svg>
                Our Mission
              </h2>
              <div className="space-y-4">
                <p className="text-gray-600 dark:text-gray-300 text-lg leading-relaxed">
                  At Calc9, we believe that everyone deserves access to accurate, fast, and reliable calculation tools. 
                  Our mission is to provide professional-grade calculators that are easy to use, scientifically accurate, 
                  and accessible from any device, anywhere in the world.
                </p>
                <p className="text-gray-600 dark:text-gray-300 text-lg leading-relaxed">
                  We understand that calculations are fundamental to daily life, whether you&apos;re planning your health journey, 
                  managing finances, working on construction projects, or solving complex mathematical problems. That&apos;s why 
                  we&apos;ve created a comprehensive suite of calculators that covers everything from basic arithmetic to 
                  specialized industry tools.
                </p>
                <p className="text-gray-600 dark:text-gray-300 text-lg leading-relaxed">
                  Our commitment extends beyond just providing tools – we&apos;re dedicated to education, helping users 
                  understand the science and methodology behind each calculation, empowering them to make informed decisions.
                </p>
              </div>
            </section>

            {/* Our Story Section */}
            <section className="bg-white dark:bg-gray-800 rounded-lg shadow-md dark:shadow-xl p-8 border border-gray-200 dark:border-gray-700">
              <h2 className="text-2xl font-bold text-gray-900 dark:text-white mb-6 flex items-center">
                <svg className="w-6 h-6 text-indigo-600 dark:text-indigo-400 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 6.253v13m0-13C10.832 5.477 9.246 5 7.5 5S4.168 5.477 3 6.253v13C4.168 18.477 5.754 18 7.5 18s3.332.477 4.5 1.253m0-13C13.168 5.477 14.754 5 16.5 5c1.746 0 3.332.477 4.5 1.253v13C19.832 18.477 18.246 18 16.5 18c-1.746 0-3.332.477-4.5 1.253" />
                </svg>
                Our Story
              </h2>
              <div className="space-y-4">
                <p className="text-gray-600 dark:text-gray-300 text-lg leading-relaxed">
                  Calc9 was born from a simple observation: most online calculators are either too basic, cluttered with ads, 
                  or lack the accuracy needed for professional use. We set out to change that by creating a platform that 
                  combines simplicity with sophistication.
                </p>
                <p className="text-gray-600 dark:text-gray-300 text-lg leading-relaxed">
                  Our journey began with a focus on health and fitness calculators, recognizing the growing need for reliable 
                  tools to help people make informed decisions about their wellbeing. As our user base grew, so did our 
                  understanding of what people really need from calculation tools.
                </p>
                <p className="text-gray-600 dark:text-gray-300 text-lg leading-relaxed">
                  Today, Calc9 serves thousands of users worldwide, from students and professionals to entrepreneurs and 
                  health enthusiasts. Each calculator is carefully crafted with input from domain experts to ensure accuracy 
                  and reliability.
                </p>
              </div>
            </section>

            {/* Why Choose Us Section */}
            <section className="bg-white dark:bg-gray-800 rounded-lg shadow-md dark:shadow-xl p-8 border border-gray-200 dark:border-gray-700">
              <h2 className="text-2xl font-bold text-gray-900 dark:text-white mb-6 flex items-center">
                <svg className="w-6 h-6 text-green-600 dark:text-green-400 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                </svg>
                Why Choose Calc9?
              </h2>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div className="space-y-4">
                  <div className="flex items-start space-x-3">
                    <div className="flex-shrink-0 w-2 h-2 bg-blue-500 rounded-full mt-2"></div>
                    <div>
                      <h3 className="font-semibold text-gray-900 dark:text-white">Scientifically Accurate</h3>
                      <p className="text-gray-600 dark:text-gray-300">All our formulas are based on established scientific principles and regularly updated.</p>
                    </div>
                  </div>
                  <div className="flex items-start space-x-3">
                    <div className="flex-shrink-0 w-2 h-2 bg-blue-500 rounded-full mt-2"></div>
                    <div>
                      <h3 className="font-semibold text-gray-900 dark:text-white">Lightning Fast</h3>
                      <p className="text-gray-600 dark:text-gray-300">Get instant results with our optimized calculation engines.</p>
                    </div>
                  </div>
                  <div className="flex items-start space-x-3">
                    <div className="flex-shrink-0 w-2 h-2 bg-blue-500 rounded-full mt-2"></div>
                    <div>
                      <h3 className="font-semibold text-gray-900 dark:text-white">Mobile Optimized</h3>
                      <p className="text-gray-600 dark:text-gray-300">Perfect experience on desktop, tablet, and mobile devices.</p>
                    </div>
                  </div>
                </div>
                <div className="space-y-4">
                  <div className="flex items-start space-x-3">
                    <div className="flex-shrink-0 w-2 h-2 bg-blue-500 rounded-full mt-2"></div>
                    <div>
                      <h3 className="font-semibold text-gray-900 dark:text-white">Always Free</h3>
                      <p className="text-gray-600 dark:text-gray-300">No hidden fees, no subscriptions. All tools are completely free to use.</p>
                    </div>
                  </div>
                  <div className="flex items-start space-x-3">
                    <div className="flex-shrink-0 w-2 h-2 bg-blue-500 rounded-full mt-2"></div>
                    <div>
                      <h3 className="font-semibold text-gray-900 dark:text-white">Privacy Focused</h3>
                      <p className="text-gray-600 dark:text-gray-300">Your data stays on your device. We don&apos;t store your calculations.</p>
                    </div>
                  </div>
                  <div className="flex items-start space-x-3">
                    <div className="flex-shrink-0 w-2 h-2 bg-blue-500 rounded-full mt-2"></div>
                    <div>
                      <h3 className="font-semibold text-gray-900 dark:text-white">Regular Updates</h3>
                      <p className="text-gray-600 dark:text-gray-300">We continuously improve our tools based on user feedback.</p>
                    </div>
                  </div>
                </div>
              </div>
            </section>

            {/* Our Tools Section */}
            <section className="bg-white dark:bg-gray-800 rounded-lg shadow-md dark:shadow-xl p-8 border border-gray-200 dark:border-gray-700">
              <h2 className="text-2xl font-bold text-gray-900 dark:text-white mb-6 flex items-center">
                <svg className="w-6 h-6 text-purple-600 dark:text-purple-400 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 7h6m0 10v-3m-3 3h.01M9 17h.01M9 14h.01M12 14h.01M15 11h.01M12 11h.01M9 11h.01M7 21h10a2 2 0 002-2V5a2 2 0 00-2-2H7a2 2 0 00-2 2v14a2 2 0 002 2z" />
                </svg>
                Our Calculator Tools
              </h2>
              <div className="mb-6">
                <p className="text-gray-600 dark:text-gray-300 text-lg leading-relaxed">
                  We offer a comprehensive suite of calculators designed to meet the diverse needs of our users. 
                  Each tool is carefully crafted with precision, validated by experts, and continuously updated to 
                  ensure accuracy and reliability.
                </p>
              </div>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div className="space-y-4">
                  <div className="bg-blue-50 dark:bg-blue-900/20 p-4 rounded-lg">
                    <h3 className="font-semibold text-gray-900 dark:text-white mb-2 flex items-center">
                      <svg className="w-5 h-5 text-red-600 dark:text-red-400 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4.318 6.318a4.5 4.5 0 000 6.364L12 20.364l7.682-7.682a4.5 4.5 0 00-6.364-6.364L12 7.636l-1.318-1.318a4.5 4.5 0 00-6.364 0z" />
                      </svg>
                      Health & Fitness
                    </h3>
                    <ul className="text-gray-600 dark:text-gray-300 space-y-1">
                      <li>• BMI Calculator - Body Mass Index assessment</li>
                      <li>• Body Type Calculator - Somatotype analysis</li>
                      <li>• Calorie Calculator - Daily energy requirements</li>
                      <li>• Sleep Calculator - Optimal sleep timing</li>
                    </ul>
                  </div>
                  <div className="bg-green-50 dark:bg-green-900/20 p-4 rounded-lg">
                    <h3 className="font-semibold text-gray-900 dark:text-white mb-2 flex items-center">
                      <svg className="w-5 h-5 text-blue-600 dark:text-blue-400 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 7h6m0 10v-3m-3 3h.01M9 17h.01M9 14h.01M12 14h.01M15 11h.01M12 11h.01M9 11h.01M7 21h10a2 2 0 002-2V5a2 2 0 00-2-2H7a2 2 0 00-2 2v14a2 2 0 002 2z" />
                      </svg>
                      Mathematical Tools
                    </h3>
                    <ul className="text-gray-600 dark:text-gray-300 space-y-1">
                      <li>• Scientific Calculator - Advanced computations</li>
                      <li>• Base Converter - Number system conversions</li>
                      <li>• Scale Conversion Calculator - Unit conversions</li>
                      <li>• Map Scale Calculator - Geographic scaling</li>
                    </ul>
                  </div>
                </div>
                <div className="space-y-4">
                  <div className="bg-purple-50 dark:bg-purple-900/20 p-4 rounded-lg">
                    <h3 className="font-semibold text-gray-900 dark:text-white mb-2 flex items-center">
                      <svg className="w-5 h-5 text-purple-600 dark:text-purple-400 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
                      </svg>
                      Date & Time
                    </h3>
                    <ul className="text-gray-600 dark:text-gray-300 space-y-1">
                      <li>• Age Calculator - Precise age computation</li>
                      <li>• Date Calculator - Date arithmetic operations</li>
                      <li>• Date Converter - Format conversions</li>
                      <li>• Time Calculator - Duration calculations</li>
                    </ul>
                  </div>
                  <div className="bg-orange-50 dark:bg-orange-900/20 p-4 rounded-lg">
                    <h3 className="font-semibold text-gray-900 dark:text-white mb-2 flex items-center">
                      <svg className="w-5 h-5 text-orange-600 dark:text-orange-400 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19.428 15.428a2 2 0 00-1.022-.547l-2.387-.477a6 6 0 00-3.86.517l-.318.158a6 6 0 01-3.86.517L6.05 15.21a2 2 0 00-1.806.547M8 4h8l-1 1v5.172a2 2 0 00.586 1.414l5 5c1.26 1.26.367 3.414-1.415 3.414H4.828c-1.782 0-2.674-2.154-1.414-3.414l5-5A2 2 0 009 10.172V5L8 4z" />
                      </svg>
                      Specialized Tools
                    </h3>
                    <ul className="text-gray-600 dark:text-gray-300 space-y-1">
                      <li>• Concrete Calculator - Construction planning</li>
                      <li>• CD Calculator - Certificate of deposit returns</li>
                      <li>• PPI Calculator - Pixel density analysis</li>
                      <li>• Storage Converter - Data size conversions</li>
                    </ul>
                  </div>
                </div>
              </div>
            </section>

            {/* Technology & Innovation Section */}
            <section className="bg-white dark:bg-gray-800 rounded-lg shadow-md dark:shadow-xl p-8 border border-gray-200 dark:border-gray-700">
              <h2 className="text-2xl font-bold text-gray-900 dark:text-white mb-6 flex items-center">
                <svg className="w-6 h-6 text-cyan-600 dark:text-cyan-400 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9.663 17h4.673M12 3v1m6.364 1.636l-.707.707M21 12h-1M4 12H3m3.343-5.657l-.707-.707m2.828 9.9a5 5 0 117.072 0l-.548.547A3.374 3.374 0 0014 18.469V19a2 2 0 11-4 0v-.531c0-.895-.356-1.754-.988-2.386l-.548-.547z" />
                </svg>
                Technology & Innovation
              </h2>
              <div className="space-y-4">
                <p className="text-gray-600 dark:text-gray-300 text-lg leading-relaxed">
                  Built with cutting-edge web technologies, Calc9 leverages the power of React and Next.js to deliver 
                  lightning-fast performance and seamless user experiences. Our platform is designed with modern 
                  web standards and best practices in mind.
                </p>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <div className="space-y-3">
                    <h3 className="font-semibold text-gray-900 dark:text-white">Performance Features</h3>
                    <ul className="text-gray-600 dark:text-gray-300 space-y-1">
                      <li>• Server-side rendering for instant loading</li>
                      <li>• Progressive Web App capabilities</li>
                      <li>• Optimized for all device sizes</li>
                      <li>• Offline calculation support</li>
                    </ul>
                  </div>
                  <div className="space-y-3">
                    <h3 className="font-semibold text-gray-900 dark:text-white">User Experience</h3>
                    <ul className="text-gray-600 dark:text-gray-300 space-y-1">
                      <li>• Intuitive, clean interface design</li>
                      <li>• Real-time calculation updates</li>
                      <li>• Comprehensive input validation</li>
                      <li>• Accessibility-first approach</li>
                    </ul>
                  </div>
                </div>
              </div>
            </section>

            {/* Our Commitment Section */}
            <section className="bg-white dark:bg-gray-800 rounded-lg shadow-md dark:shadow-xl p-8 border border-gray-200 dark:border-gray-700">
              <h2 className="text-2xl font-bold text-gray-900 dark:text-white mb-6 flex items-center">
                <svg className="w-6 h-6 text-emerald-600 dark:text-emerald-400 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4m5.618-4.016A11.955 11.955 0 0112 2.944a11.955 11.955 0 01-8.618 3.04A12.02 12.02 0 003 9c0 5.591 3.824 10.29 9 11.622 5.176-1.332 9-6.03 9-11.622 0-1.042-.133-2.052-.382-3.016z" />
                </svg>
                Our Commitment to You
              </h2>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div className="space-y-4">
                  <div className="flex items-start space-x-3">
                    <div className="flex-shrink-0 w-8 h-8 bg-green-100 dark:bg-green-900/30 rounded-full flex items-center justify-center">
                      <svg className="w-4 h-4 text-green-600 dark:text-green-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                      </svg>
                    </div>
                    <div>
                      <h3 className="font-semibold text-gray-900 dark:text-white">Quality Assurance</h3>
                      <p className="text-gray-600 dark:text-gray-300">Every calculator undergoes rigorous testing to ensure mathematical accuracy and reliability.</p>
                    </div>
                  </div>
                  <div className="flex items-start space-x-3">
                    <div className="flex-shrink-0 w-8 h-8 bg-blue-100 dark:bg-blue-900/30 rounded-full flex items-center justify-center">
                      <svg className="w-4 h-4 text-blue-600 dark:text-blue-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z" />
                      </svg>
                    </div>
                    <div>
                      <h3 className="font-semibold text-gray-900 dark:text-white">Privacy Protection</h3>
                      <p className="text-gray-600 dark:text-gray-300">We prioritize your privacy - no personal data collection, no tracking, no ads.</p>
                    </div>
                  </div>
                  <div className="flex items-start space-x-3">
                    <div className="flex-shrink-0 w-8 h-8 bg-purple-100 dark:bg-purple-900/30 rounded-full flex items-center justify-center">
                      <svg className="w-4 h-4 text-purple-600 dark:text-purple-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 10V3L4 14h7v7l9-11h-7z" />
                      </svg>
                    </div>
                    <div>
                      <h3 className="font-semibold text-gray-900 dark:text-white">Continuous Improvement</h3>
                      <p className="text-gray-600 dark:text-gray-300">We regularly update our tools based on user feedback and the latest scientific research.</p>
                    </div>
                  </div>
                </div>
                <div className="space-y-4">
                  <div className="flex items-start space-x-3">
                    <div className="flex-shrink-0 w-8 h-8 bg-orange-100 dark:bg-orange-900/30 rounded-full flex items-center justify-center">
                      <svg className="w-4 h-4 text-orange-600 dark:text-orange-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 18h.01M8 21h8a2 2 0 002-2V5a2 2 0 00-2-2H8a2 2 0 00-2 2v14a2 2 0 002 2z" />
                      </svg>
                    </div>
                    <div>
                      <h3 className="font-semibold text-gray-900 dark:text-white">Universal Access</h3>
                      <p className="text-gray-600 dark:text-gray-300">Designed to work perfectly on any device, from smartphones to desktop computers.</p>
                    </div>
                  </div>
                  <div className="flex items-start space-x-3">
                    <div className="flex-shrink-0 w-8 h-8 bg-red-100 dark:bg-red-900/30 rounded-full flex items-center justify-center">
                      <svg className="w-4 h-4 text-red-600 dark:text-red-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4.318 6.318a4.5 4.5 0 000 6.364L12 20.364l7.682-7.682a4.5 4.5 0 00-6.364-6.364L12 7.636l-1.318-1.318a4.5 4.5 0 00-6.364 0z" />
                      </svg>
                    </div>
                    <div>
                      <h3 className="font-semibold text-gray-900 dark:text-white">Community Focus</h3>
                      <p className="text-gray-600 dark:text-gray-300">Built by users, for users. Our community drives the development of new features and tools.</p>
                    </div>
                  </div>
                  <div className="flex items-start space-x-3">
                    <div className="flex-shrink-0 w-8 h-8 bg-indigo-100 dark:bg-indigo-900/30 rounded-full flex items-center justify-center">
                      <svg className="w-4 h-4 text-indigo-600 dark:text-indigo-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9.663 17h4.673M12 3v1m6.364 1.636l-.707.707M21 12h-1M4 12H3m3.343-5.657l-.707-.707m2.828 9.9a5 5 0 117.072 0l-.548.547A3.374 3.374 0 0014 18.469V19a2 2 0 11-4 0v-.531c0-.895-.356-1.754-.988-2.386l-.548-.547z" />
                      </svg>
                    </div>
                    <div>
                      <h3 className="font-semibold text-gray-900 dark:text-white">Innovation</h3>
                      <p className="text-gray-600 dark:text-gray-300">Always exploring new ways to make calculations easier, faster, and more intuitive.</p>
                    </div>
                  </div>
                </div>
              </div>
            </section>

            {/* Contact Section */}
            <section className="bg-white dark:bg-gray-800 rounded-lg shadow-md dark:shadow-xl p-8 border border-gray-200 dark:border-gray-700">
              <h2 className="text-2xl font-bold text-gray-900 dark:text-white mb-6 flex items-center">
                <svg className="w-6 h-6 text-orange-600 dark:text-orange-400 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-3.582 8-8 8a8.959 8.959 0 01-4.906-1.488L3 21l2.488-5.094A8.959 8.959 0 013 12c0-4.418 3.582-8 8-8s8 3.582 8 8z" />
                </svg>
                Get in Touch
              </h2>
              <div className="space-y-6">
                <p className="text-gray-600 dark:text-gray-300 text-lg">
                  We love hearing from our users! Whether you have feedback, suggestions, feature requests, or need help with our tools, 
                  we&apos;re here to assist you. Your input helps us make Calc9 better for everyone.
                </p>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <div className="bg-blue-50 dark:bg-blue-900/20 p-4 rounded-lg">
                    <div className="flex items-center space-x-3 mb-2">
                      <svg className="w-5 h-5 text-blue-600 dark:text-blue-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-3.582 8-8 8a8.959 8.959 0 01-4.906-1.488L3 21l2.488-5.094A8.959 8.959 0 013 12c0-4.418 3.582-8 8-8s8 3.582 8 8z" />
                      </svg>
                      <h3 className="font-semibold text-gray-900 dark:text-white">Send Feedback</h3>
                    </div>
                    <p className="text-gray-600 dark:text-gray-300">Use the feedback button at the bottom of any page to share your thoughts, report issues, or suggest improvements.</p>
                  </div>
                  <div className="bg-purple-50 dark:bg-purple-900/20 p-4 rounded-lg">
                    <div className="flex items-center space-x-3 mb-2">
                      <svg className="w-5 h-5 text-purple-600 dark:text-purple-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13.828 10.172a4 4 0 00-5.656 0l-4 4a4 4 0 105.656 5.656l1.102-1.101m-.758-4.899a4 4 0 005.656 0l4-4a4 4 0 00-5.656-5.656l-1.1 1.1" />
                      </svg>
                      <h3 className="font-semibold text-gray-900 dark:text-white">Learn More</h3>
                    </div>
                    <p className="text-gray-600 dark:text-gray-300">Check out our blog for calculation tips, guides, and insights into the science behind our tools.</p>
                  </div>
                </div>
                <div className="text-center bg-gray-50 dark:bg-gray-700/50 p-6 rounded-lg">
                  <h3 className="font-semibold text-gray-900 dark:text-white mb-2">Join Our Community</h3>
                  <p className="text-gray-600 dark:text-gray-300">
                    Be part of the Calc9 community and help shape the future of online calculators. Your feedback and suggestions directly influence our development roadmap.
                  </p>
                </div>
              </div>
            </section>
          </main>
        </div>
      </div>
      <Footer />
    </div>
  );
}
