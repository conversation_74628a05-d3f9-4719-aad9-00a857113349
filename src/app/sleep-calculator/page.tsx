'use client';

import { useState, useEffect, useCallback, useMemo } from 'react';
import Footer from "@/components/Footer";
import CalculatorHeader from '@/components/CalculatorHeader';
import { containerStyles, textStyles, inputStyles, buttonStyles, statusStyles, cn } from '@/components/ui/styles';

interface SleepResult {
  type: 'bedtime' | 'waketime';
  times: string[];
  cycleDuration: number;
  fallAsleepTime: number;
  totalSleepHours: number;
  recommendedIndex?: number;
}

export default function SleepCalculator() {
  const [wakeTime, setWakeTime] = useState<string>('07:00');
  const [bedTime, setBedTime] = useState<string>('');
  const [cycleDuration, setCycleDuration] = useState<number>(90);
  const [fallAsleepTime, setFallAsleepTime] = useState<number>(15);
  const [ageGroup, setAgeGroup] = useState<string>(() => {
    if (typeof window !== 'undefined') {
      return localStorage.getItem('sleep-calculator-age-group') || 'adult';
    }
    return 'adult';
  });
  const [result, setResult] = useState<SleepResult | null>(null);

  const ageRecommendations = useMemo(() => ({
    'teen': { min: 8, max: 10, label: 'Teen (14-17 years)' },
    'adult': { min: 7, max: 9, label: 'Adult (18-64 years)' },
    'senior': { min: 7, max: 8, label: 'Senior (65+ years)' }
  }), []);

  const calculateSleepCycles = useCallback((targetTime: string, isWakeTime: boolean): SleepResult => {
    const now = new Date();
    const [hours, minutes] = targetTime.split(':').map(Number);
    
    const targetDate = new Date(now);
    targetDate.setHours(hours, minutes, 0, 0);
    
    if (targetDate <= now) {
      targetDate.setDate(targetDate.getDate() + 1);
    }

    const times: string[] = [];
    const cycles = [1, 2, 3, 4, 5, 6];
    const sleepHours: number[] = [];
    
    cycles.forEach(cycleCount => {
      let calculatedTime: Date;
      
      if (isWakeTime) {
        const totalMinutes = (cycleCount * cycleDuration) + fallAsleepTime;
        calculatedTime = new Date(targetDate.getTime() - (totalMinutes * 60000));
      } else {
        const totalMinutes = (cycleCount * cycleDuration) + fallAsleepTime;
        calculatedTime = new Date(targetDate.getTime() + (totalMinutes * 60000));
      }
      
      const timeString = calculatedTime.toLocaleTimeString('en-US', {
        hour: '2-digit',
        minute: '2-digit',
        hour12: false
      });
      
      const sleepHour = (cycleCount * cycleDuration) / 60;
      times.push(timeString);
      sleepHours.push(sleepHour);
    });

    // Find the best recommendation based on age group
    const ageRec = ageRecommendations[ageGroup as keyof typeof ageRecommendations];
    let recommendedIndex = 4; // Default to 5 cycles
    
    // Find the cycle that best fits the age group recommendation
    for (let i = 0; i < sleepHours.length; i++) {
      if (sleepHours[i] >= ageRec.min && sleepHours[i] <= ageRec.max) {
        recommendedIndex = i;
        break;
      }
    }
    
    // If no cycle fits exactly, find the closest one
    if (sleepHours[recommendedIndex] < ageRec.min || sleepHours[recommendedIndex] > ageRec.max) {
      let closestDistance = Infinity;
      for (let i = 0; i < sleepHours.length; i++) {
        const distance = Math.min(
          Math.abs(sleepHours[i] - ageRec.min),
          Math.abs(sleepHours[i] - ageRec.max)
        );
        if (distance < closestDistance) {
          closestDistance = distance;
          recommendedIndex = i;
        }
      }
    }

    return {
      type: isWakeTime ? 'bedtime' : 'waketime',
      times,
      cycleDuration,
      fallAsleepTime,
      totalSleepHours: (cycles.length * cycleDuration) / 60,
      recommendedIndex
    };
  }, [cycleDuration, fallAsleepTime, ageGroup, ageRecommendations]);

  const getCurrentTime = (): string => {
    const now = new Date();
    return now.toLocaleTimeString('en-US', {
      hour: '2-digit',
      minute: '2-digit',
      hour12: false
    });
  };

  const calculateBedtime = useCallback(() => {
    const sleepResult = calculateSleepCycles(wakeTime, true);
    setResult(sleepResult);
    // Scroll to results after a short delay to allow DOM update
    setTimeout(() => {
      const resultsElement = document.getElementById('sleep-results');
      if (resultsElement) {
        resultsElement.scrollIntoView({ 
          behavior: 'smooth', 
          block: 'center' 
        });
      }
    }, 100);
  }, [calculateSleepCycles, wakeTime]);

  const calculateWakeTime = useCallback(() => {
    const sleepResult = calculateSleepCycles(bedTime, false);
    setResult(sleepResult);
    // Scroll to results after a short delay to allow DOM update
    setTimeout(() => {
      const resultsElement = document.getElementById('sleep-results');
      if (resultsElement) {
        resultsElement.scrollIntoView({ 
          behavior: 'smooth', 
          block: 'center' 
        });
      }
    }, 100);
  }, [calculateSleepCycles, bedTime]);

  useEffect(() => {
    setWakeTime('07:00');
    const currentTime = getCurrentTime();
    setBedTime(currentTime);
    calculateBedtime();
  }, [calculateBedtime]);

  return (
    <>
      <div className={containerStyles.page}>
        <div className="container mx-auto px-4 py-8">
          <div className="max-w-4xl mx-auto">
            <CalculatorHeader
              title="Sleep Calculator"
              description="Find the best bedtime and wake-up times based on your sleep cycles"
              category="Health"
              className="text-center mb-8"
            />

            {/* Main Calculator Cards */}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-8">
              {/* Calculate Bedtime Card */}
              <div className={cn(containerStyles.card, "p-8 text-center")}>
                <h2 className={cn(textStyles.h2, "mb-4")}>Calculate Bedtime</h2>
                <p className={cn(textStyles.body, "mb-6")}>I want to wake up at...</p>
                
                <div className="mb-6">
                  <input
                    type="time"
                    value={wakeTime}
                    onChange={(e) => setWakeTime(e.target.value)}
                    className={cn(inputStyles.base, "text-2xl text-center p-4 w-full max-w-xs mx-auto")}
                  />
                </div>
                
                <div className="flex justify-center space-x-2 mb-6">
                  <button
                    onClick={() => setWakeTime('07:00')}
                    className={cn(buttonStyles.secondary, "text-sm")}
                  >
                    7:00 AM
                  </button>
                  <button
                    onClick={() => {
                      const [hours, minutes] = wakeTime.split(':').map(Number);
                      const time = new Date();
                      time.setHours(hours, minutes, 0, 0);
                      time.setMinutes(time.getMinutes() - 30);
                      setWakeTime(time.toLocaleTimeString('en-US', { hour: '2-digit', minute: '2-digit', hour12: false }));
                    }}
                    className={cn(buttonStyles.secondary, "text-sm")}
                  >
                    -30m
                  </button>
                  <button
                    onClick={() => {
                      const [hours, minutes] = wakeTime.split(':').map(Number);
                      const time = new Date();
                      time.setHours(hours, minutes, 0, 0);
                      time.setMinutes(time.getMinutes() + 30);
                      setWakeTime(time.toLocaleTimeString('en-US', { hour: '2-digit', minute: '2-digit', hour12: false }));
                    }}
                    className={cn(buttonStyles.secondary, "text-sm")}
                  >
                    +30m
                  </button>
                </div>
                
                <button
                  onClick={calculateBedtime}
                  className={cn(buttonStyles.primary, "w-full")}
                >
                  Calculate Bedtime
                </button>
              </div>

              {/* Calculate Wake Time Card */}
              <div className={cn(containerStyles.card, "p-8 text-center")}>
                <h2 className={cn(textStyles.h2, "mb-4")}>Calculate Wake Time</h2>
                <p className={cn(textStyles.body, "mb-6")}>I want to go to bed...</p>
                
                <div className="mb-6">
                  <input
                    type="time"
                    value={bedTime}
                    onChange={(e) => setBedTime(e.target.value)}
                    className={cn(inputStyles.base, "text-2xl text-center p-4 w-full max-w-xs mx-auto")}
                  />
                </div>
                
                <div className="flex justify-center space-x-2 mb-6">
                  <button
                    onClick={() => setBedTime(getCurrentTime())}
                    className={cn(buttonStyles.secondary, "text-sm")}
                  >
                    Now
                  </button>
                  <button
                    onClick={() => {
                      const current = new Date();
                      current.setMinutes(current.getMinutes() + 30);
                      setBedTime(current.toLocaleTimeString('en-US', { hour: '2-digit', minute: '2-digit', hour12: false }));
                    }}
                    className={cn(buttonStyles.secondary, "text-sm")}
                  >
                    +30m
                  </button>
                  <button
                    onClick={() => {
                      const current = new Date();
                      current.setHours(current.getHours() + 1);
                      setBedTime(current.toLocaleTimeString('en-US', { hour: '2-digit', minute: '2-digit', hour12: false }));
                    }}
                    className={cn(buttonStyles.secondary, "text-sm")}
                  >
                    +1h
                  </button>
                </div>
                
                <button
                  onClick={calculateWakeTime}
                  className={cn(buttonStyles.primary, "w-full")}
                >
                  Calculate Wake Time
                </button>
              </div>
            </div>

            {/* Results Section */}
            {result && (
              <div id="sleep-results" className={cn(containerStyles.card, "p-6 border-l-4 border-green-500 dark:border-green-400 mb-8")}>
                <h3 className={cn(textStyles.h2, "mb-4 text-center")}>
                  {result.type === 'bedtime' ? '🛏️ Recommended Bedtimes' : '⏰ Recommended Wake Times'}
                </h3>
                
                {result.type === 'bedtime' ? (
                  <div className="space-y-6">
                    <p className={cn(textStyles.body, "mb-4 text-center text-sm")}>
                      To wake up refreshed at {wakeTime}, try going to bed at one of these times:
                    </p>
                    
                    {/* Recommended Time - Prominently displayed */}
                    <div className="flex justify-center mb-6">
                      <div className="bg-gradient-to-r from-blue-500 to-blue-600 text-white rounded-xl p-6 shadow-lg">
                        <div className="text-center">
                          <div className="text-xs font-medium mb-1 opacity-90">RECOMMENDED FOR {ageRecommendations[ageGroup as keyof typeof ageRecommendations].label.split(' ')[0]}</div>
                          <div className="text-3xl font-bold mb-1">{result.times[result.recommendedIndex || 4]}</div>
                          <div className="text-sm opacity-90">
                            {(result.recommendedIndex || 4) + 1} cycles • {(((result.recommendedIndex || 4) + 1) * cycleDuration / 60).toFixed(1)}h sleep
                          </div>
                        </div>
                      </div>
                    </div>
                    
                    {/* Other options - Reduced prominence */}
                    <div className="space-y-2">
                      <div className="text-center text-sm text-gray-600 dark:text-gray-400 mb-3">
                        Other options:
                      </div>
                      <div className="grid grid-cols-2 md:grid-cols-5 gap-2">
                        {result.times.slice(0, 6).map((time, index) => {
                          if (index === (result.recommendedIndex || 4)) return null; // Skip the recommended time
                          return (
                            <div key={index} className={cn(
                              "text-center p-3 rounded-lg border transition-colors cursor-pointer",
                              "hover:bg-gray-50 dark:hover:bg-gray-700 border-gray-200 dark:border-gray-600"
                            )}>
                              <div className="font-medium text-sm text-gray-700 dark:text-gray-300">{time}</div>
                              <div className="text-xs text-gray-500 dark:text-gray-400">
                                {index + 1} cycle{index > 0 ? 's' : ''} • {((index + 1) * cycleDuration / 60).toFixed(1)}h
                              </div>
                            </div>
                          );
                        })}
                      </div>
                    </div>
                  </div>
                ) : (
                  <div className="space-y-6">
                    <p className={cn(textStyles.body, "mb-4 text-center text-sm")}>
                      If you go to bed at {bedTime}, try waking up at one of these times:
                    </p>
                    
                    {/* Recommended Time - Prominently displayed */}
                    <div className="flex justify-center mb-6">
                      <div className="bg-gradient-to-r from-green-500 to-green-600 text-white rounded-xl p-6 shadow-lg">
                        <div className="text-center">
                          <div className="text-xs font-medium mb-1 opacity-90">RECOMMENDED FOR {ageRecommendations[ageGroup as keyof typeof ageRecommendations].label.split(' ')[0]}</div>
                          <div className="text-3xl font-bold mb-1">{result.times[result.recommendedIndex || 4]}</div>
                          <div className="text-sm opacity-90">
                            {(result.recommendedIndex || 4) + 1} cycles • {(((result.recommendedIndex || 4) + 1) * cycleDuration / 60).toFixed(1)}h sleep
                          </div>
                        </div>
                      </div>
                    </div>
                    
                    {/* Other options - Reduced prominence */}
                    <div className="space-y-2">
                      <div className="text-center text-sm text-gray-600 dark:text-gray-400 mb-3">
                        Other options:
                      </div>
                      <div className="grid grid-cols-2 md:grid-cols-5 gap-2">
                        {result.times.slice(0, 6).map((time, index) => {
                          if (index === (result.recommendedIndex || 4)) return null; // Skip the recommended time
                          return (
                            <div key={index} className={cn(
                              "text-center p-3 rounded-lg border transition-colors cursor-pointer",
                              "hover:bg-gray-50 dark:hover:bg-gray-700 border-gray-200 dark:border-gray-600"
                            )}>
                              <div className="font-medium text-sm text-gray-700 dark:text-gray-300">{time}</div>
                              <div className="text-xs text-gray-500 dark:text-gray-400">
                                {index + 1} cycle{index > 0 ? 's' : ''} • {((index + 1) * cycleDuration / 60).toFixed(1)}h
                              </div>
                            </div>
                          );
                        })}
                      </div>
                    </div>
                  </div>
                )}
              </div>
            )}

            {/* Quick Settings */}
            <div className={cn(containerStyles.card, "p-6 mb-8")}>
              <h3 className={cn(textStyles.h3, "mb-4 text-center")}>⚙️ Quick Settings</h3>
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                <div>
                  <label className={cn(textStyles.label, "block mb-2 text-sm")}>Fall asleep time</label>
                  <select
                    value={fallAsleepTime}
                    onChange={(e) => setFallAsleepTime(Number(e.target.value))}
                    className={cn(inputStyles.select, "text-sm")}
                  >
                    <option value={0}>0 min</option>
                    <option value={15}>15 min (average)</option>
                    <option value={30}>30 min</option>
                  </select>
                </div>
                <div>
                  <label className={cn(textStyles.label, "block mb-2 text-sm")}>Sleep cycle</label>
                  <select
                    value={cycleDuration}
                    onChange={(e) => setCycleDuration(Number(e.target.value))}
                    className={cn(inputStyles.select, "text-sm")}
                  >
                    <option value={90}>90 min (average)</option>
                    <option value={100}>100 min</option>
                    <option value={110}>110 min</option>
                  </select>
                </div>
                <div>
                  <label className={cn(textStyles.label, "block mb-2 text-sm")}>Age group</label>
                  <select
                    value={ageGroup}
                    onChange={(e) => {
                      const newAgeGroup = e.target.value;
                      setAgeGroup(newAgeGroup);
                      localStorage.setItem('sleep-calculator-age-group', newAgeGroup);
                    }}
                    className={cn(inputStyles.select, "text-sm")}
                  >
                    <option value="teen">Teen (8-10h)</option>
                    <option value="adult">Adult (7-9h)</option>
                    <option value="senior">Senior (7-8h)</option>
                  </select>
                </div>
              </div>
            </div>

            {/* Settings Explanation Cards */}
            <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8">
              {/* Fall Asleep Time Card */}
              <div className={cn(containerStyles.card, "p-6")}>
                <div className="text-center mb-4">
                  <div className="text-2xl mb-2">⏱️</div>
                  <h3 className={cn(textStyles.h4, "font-semibold")}>Fall Asleep Time</h3>
                </div>
                <div className="text-sm space-y-2">
                  <p><strong>Average:</strong> 15 minutes</p>
                  <p><strong>Fast sleepers:</strong> 0-5 minutes</p>
                  <p><strong>Difficulty sleeping:</strong> 20-30 minutes</p>
                  <p className="text-xs text-gray-600 dark:text-gray-400 pt-2">
                    This is the time from when you get into bed until you actually fall asleep. 
                    Adjust based on your personal experience.
                  </p>
                </div>
              </div>

              {/* Sleep Cycle Card */}
              <div className={cn(containerStyles.card, "p-6")}>
                <div className="text-center mb-4">
                  <div className="text-2xl mb-2">🔄</div>
                  <h3 className={cn(textStyles.h4, "font-semibold")}>Sleep Cycles</h3>
                </div>
                <div className="text-sm space-y-2">
                  <p><strong>Average:</strong> 90 minutes</p>
                  <p><strong>Range:</strong> 70-120 minutes</p>
                  <p><strong>Stages:</strong> Light → Deep → REM sleep</p>
                  <p className="text-xs text-gray-600 dark:text-gray-400 pt-2">
                    Each cycle includes different sleep stages. Waking at the end of a cycle 
                    helps you feel more refreshed.
                  </p>
                </div>
              </div>

              {/* Age Group Card */}
              <div className={cn(containerStyles.card, "p-6")}>
                <div className="text-center mb-4">
                  <div className="text-2xl mb-2">👥</div>
                  <h3 className={cn(textStyles.h4, "font-semibold")}>Age Groups</h3>
                </div>
                <div className="text-sm space-y-2">
                  <p><strong>Teen:</strong> 8-10 hours</p>
                  <p><strong>Adult:</strong> 7-9 hours</p>
                  <p><strong>Senior:</strong> 7-8 hours</p>
                  <p className="text-xs text-gray-600 dark:text-gray-400 pt-2">
                    Sleep needs change with age. Our recommendations adjust based on 
                    scientific sleep research for each age group.
                  </p>
                </div>
              </div>
            </div>

            {/* Sleep Tips - Enhanced */}
            <div className={cn(statusStyles.info.container, "p-6 mb-8")}>
              <h3 className={cn(statusStyles.info.title, "mb-4 text-center")}>💡 Better Sleep Tips</h3>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6 text-sm">
                <div className="space-y-3">
                  <div>
                    <strong>🕐 Timing:</strong> Keep consistent sleep schedule, even on weekends
                  </div>
                  <div>
                    <strong>🌙 Environment:</strong> Cool (60-67°F), dark, and quiet room
                  </div>
                  <div>
                    <strong>📱 Before Bed:</strong> Avoid screens 1 hour before sleep
                  </div>
                </div>
                <div className="space-y-3">
                  <div>
                    <strong>☕ Avoid:</strong> Caffeine after 2 PM and large meals before bed
                  </div>
                  <div>
                    <strong>☀️ Light Exposure:</strong> Get natural sunlight during the day
                  </div>
                  <div>
                    <strong>🧘 Routine:</strong> Create a relaxing 30-minute bedtime routine
                  </div>
                </div>
              </div>
            </div>

            {/* Sleep Science */}
            <div className={cn(statusStyles.purple.container, "p-6")}>
              <h3 className={cn(statusStyles.purple.title, "mb-4 text-center")}>🧠 The Science</h3>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6 text-sm">
                <div className="space-y-3">
                  <div>
                    <strong>Sleep Cycles:</strong> Your brain cycles through light sleep, deep sleep, 
                    and REM sleep approximately every 90 minutes throughout the night.
                  </div>
                  <div>
                    <strong>Optimal Timing:</strong> Waking up at the end of a complete cycle, 
                    during lighter sleep stages, helps you feel more alert and refreshed.
                  </div>
                </div>
                <div className="space-y-3">
                  <div>
                    <strong>Circadian Rhythm:</strong> Your body&apos;s internal clock regulates 
                    sleep-wake cycles based on light exposure and daily patterns.
                  </div>
                  <div>
                    <strong>Individual Variation:</strong> While 90 minutes is average, 
                    your personal cycle length may vary between 70-120 minutes.
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
        <Footer />
      </div>
    </>
  );
}