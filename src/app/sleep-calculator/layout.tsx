import { generatePageTitle, generateCanonicalUrl } from '@/lib/utils';
import type { Metadata } from 'next';

export const metadata: Metadata = {
  title: generatePageTitle('Sleep Calculator'),
  description: 'Optimize your sleep schedule with our science-based sleep calculator. Find the best bedtime and wake-up times based on sleep cycles, circadian rhythm, and sleep duration recommendations.',
  keywords: 'sleep calculator, bedtime calculator, wake up time, sleep cycle, circadian rhythm, sleep duration, optimal sleep, sleep quality, sleep schedule',
  robots: 'index, follow',
  alternates: {
    canonical: generateCanonicalUrl('/sleep-calculator'),
  },
  openGraph: {
    title: generatePageTitle('Sleep Calculator'),
    description: 'Optimize your sleep schedule with our science-based sleep calculator. Calculate optimal bedtime and wake-up times based on sleep cycles.',
    type: 'website',
    url: generateCanonicalUrl('/sleep-calculator'),
  },
  twitter: {
    card: 'summary',
    title: generatePageTitle('Sleep Calculator'),
    description: 'Optimize your sleep schedule with our science-based sleep calculator. Calculate optimal bedtime and wake-up times based on sleep cycles.',
  },
};

export default function SleepCalculatorLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  return children;
}