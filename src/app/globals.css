@tailwind base;
@tailwind components;
@tailwind utilities;

html {
  margin: 0;
  padding: 0;
  min-height: 100%;
  /* Light mode: light blue to light gray gradient */
  background: linear-gradient(to bottom right, #d1ecff, #ffffff);
  background-attachment: fixed;
  background-repeat: no-repeat;
  background-size: 100% 100%;
  transition: background 0.5s ease;
}

/* Dark mode: deeper black to blue gradient for html element */
html.dark {
  background: linear-gradient(to bottom right, #000000, #101031, #0e1a3c);
  background-attachment: fixed;
  background-repeat: no-repeat;
  background-size: 100% 100%;
}

/* Ensure consistent background coverage */
html::before {
  content: '';
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: inherit;
  z-index: -1;
}

/* Fallback for older browsers */
@supports not (background-attachment: fixed) {
  html {
    background-attachment: scroll;
  }

  html.dark {
    background-attachment: scroll;
  }
}

body {
  font-family: Arial, Helvetica, sans-serif;
  min-height: 100vh;
  margin: 0;
  padding: 0;
  position: relative;
  overflow-x: hidden;
  background: transparent;
  transition: background 0.3s ease;
}

/* Logo filter for different themes */
:root {
  --logo-filter: none;
}

html.dark {
  --logo-filter: brightness(0) invert(1);
}

/* Fix scroll and positioning issues during theme transition */
html,
html.dark {
  overflow-x: hidden;
  scroll-behavior: smooth;
  min-height: 100%;
}

/* Prevent layout shift during theme transitions */
* {
  box-sizing: border-box;
}

*::before,
*::after {
  box-sizing: border-box;
}

/* Global background fix - make all white backgrounds semi-transparent */
.bg-white {
  background-color: rgba(255, 255, 255, 0.85) !important;
  backdrop-filter: blur(4px);
}

html.dark .bg-white {
  background-color: rgba(31, 41, 55, 0.85) !important;
  backdrop-filter: blur(4px);
}

/* Fix for specific dark mode bg-gray classes */
html.dark .bg-gray-800 {
  background-color: rgba(31, 41, 55, 0.85) !important;
  backdrop-filter: blur(4px);
}

html.dark .bg-gray-700 {
  background-color: rgba(55, 65, 81, 0.85) !important;
  backdrop-filter: blur(4px);
}

html.dark .bg-gray-900 {
  background-color: rgba(17, 24, 39, 0.85) !important;
  backdrop-filter: blur(4px);
}

/* Fix for light mode gray backgrounds */
.bg-gray-50 {
  background-color: rgba(249, 250, 251, 0.80) !important;
  backdrop-filter: blur(2px);
}

.bg-gray-100 {
  background-color: rgba(243, 244, 246, 0.80) !important;
  backdrop-filter: blur(2px);
}

html.dark .bg-gray-50 {
  background-color: rgba(55, 65, 81, 0.80) !important;
  backdrop-filter: blur(2px);
}

html.dark .bg-gray-100 {
  background-color: rgba(75, 85, 99, 0.80) !important;
  backdrop-filter: blur(2px);
}

/* Optimized font color hierarchy for dark mode */
.dark {
  /* Primary text: Pure white for main headings and important text */
  --text-primary: #ffffff;

  /* Secondary text: Light gray for general content */
  --text-secondary: #f1f5f9;

  /* Tertiary text: Medium gray for less important text */
  --text-tertiary: #cbd5e1;

  /* Muted text: Lighter gray for captions and secondary info */
  --text-muted: #94a3b8;

  /* Accent text: Bright blue for links and highlights */
  --text-accent: #60a5fa;

  /* Success text: Bright green for positive values */
  --text-success: #4ade80;

  /* Warning text: Bright yellow/orange for warnings */
  --text-warning: #fbbf24;

  /* Error text: Bright red for errors */
  --text-error: #f87171;

  /* Info text: Bright cyan for informational content */
  --text-info: #22d3ee;
}

/* Apply optimized font colors - only for critical text overrides */
.dark h1,
.dark h2,
.dark h3,
.dark h4,
.dark h5,
.dark h6 {
  color: var(--text-primary);
}

.dark .text-primary {
  color: var(--text-primary);
}

.dark .text-secondary {
  color: var(--text-secondary);
}

.dark .text-tertiary {
  color: var(--text-tertiary);
}

.dark .text-muted {
  color: var(--text-muted);
}

.dark .text-accent {
  color: var(--text-accent);
}

.dark .text-success {
  color: var(--text-success);
}

.dark .text-warning {
  color: var(--text-warning);
}

.dark .text-error {
  color: var(--text-error);
}

.dark .text-info {
  color: var(--text-info);
}

/* Override specific Tailwind classes for better contrast - only when needed */
.dark .text-gray-800,
.dark .text-gray-900 {
  color: var(--text-primary);
}

.dark .text-gray-700 {
  color: var(--text-secondary);
}

.dark .text-gray-600 {
  color: var(--text-tertiary);
}

.dark .text-gray-500 {
  color: var(--text-muted);
}

/* Enhanced color variants for better dark mode visibility */
.dark .text-blue-400 {
  color: var(--text-accent);
}

.dark .text-green-400 {
  color: var(--text-success);
}

.dark .text-amber-400 {
  color: var(--text-warning);
}

/* Basic dark mode scrollbar styling */
.dark ::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

.dark ::-webkit-scrollbar-thumb {
  background-color: #4b5563;
  border-radius: 4px;
}

.dark ::-webkit-scrollbar-thumb:hover {
  background-color: #6b7280;
}

/* Dark mode placeholder text */
.dark input::placeholder {
  color: #9ca3af;
}

.dark textarea::placeholder {
  color: #9ca3af;
}

/* Dark mode text selection */
.dark ::selection {
  background-color: #3b82f6;
  color: #ffffff;
}

/* Ensure smooth transitions for theme changes */
.dark * {
  transition: background-color 0.3s ease, color 0.3s ease, border-color 0.3s ease;
}

/* Basic table styling for dark mode */
.dark table {
  background-color: #374151;
  color: #f9fafb;
}

.dark table th {
  background-color: #4b5563;
  color: #f9fafb;
  border-color: #6b7280;
}

.dark table td {
  border-color: #6b7280;
  color: #f9fafb;
}

.dark table tr:nth-child(even) {
  background-color: #4b5563;
}

.dark table tr:hover {
  background-color: #6b7280;
}

/* Blog content optimized dark mode classes */
.blog-card {
  @apply bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-lg shadow-lg dark:shadow-gray-700/50 transition-colors duration-300;
}

.blog-content {
  @apply text-gray-700 dark:text-gray-300 transition-colors duration-300;
}

.blog-content-light {
  @apply text-gray-600 dark:text-gray-400 transition-colors duration-300;
}

.blog-heading {
  @apply text-gray-800 dark:text-white transition-colors duration-300;
}

.blog-link {
  @apply text-blue-600 dark:text-blue-400 hover:text-blue-700 dark:hover:text-blue-300 transition-colors duration-300;
}

.blog-info-box {
  @apply bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-700 rounded-lg transition-colors duration-300;
}

.blog-info-text {
  @apply text-blue-800 dark:text-blue-200 transition-colors duration-300;
}

.blog-info-content {
  @apply text-gray-700 dark:text-gray-300 transition-colors duration-300;
}

.blog-success-box {
  @apply bg-green-50 dark:bg-green-900/20 border border-green-200 dark:border-green-700 rounded-lg transition-colors duration-300;
}

.blog-success-text {
  @apply text-green-800 dark:text-green-200 transition-colors duration-300;
}

.blog-warning-box {
  @apply bg-yellow-50 dark:bg-yellow-900/20 border border-yellow-200 dark:border-yellow-700 rounded-lg transition-colors duration-300;
}

.blog-warning-text {
  @apply text-yellow-800 dark:text-yellow-200 transition-colors duration-300;
}

.blog-error-box {
  @apply bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-700 rounded-lg transition-colors duration-300;
}

.blog-error-text {
  @apply text-red-800 dark:text-red-200 transition-colors duration-300;
}

.blog-secondary-box {
  @apply bg-gray-50 dark:bg-gray-700 border border-gray-200 dark:border-gray-600 rounded-lg transition-colors duration-300;
}

.blog-code-block {
  @apply bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-600 rounded font-mono text-sm text-gray-700 dark:text-gray-300 transition-colors duration-300;
}

.blog-button {
  @apply bg-blue-500 hover:bg-blue-600 dark:bg-blue-600 dark:hover:bg-blue-700 text-white transition-colors duration-300;
}

.blog-button-outline {
  @apply border border-blue-200 dark:border-blue-700 text-blue-600 dark:text-blue-400 hover:bg-blue-50 dark:hover:bg-blue-900/20 transition-colors duration-300;
}

/* Enhanced category badges */
.blog-category-tech {
  @apply bg-purple-100 dark:bg-purple-900/50 text-purple-800 dark:text-purple-200 transition-colors duration-300;
}

.blog-category-finance {
  @apply bg-green-100 dark:bg-green-900/50 text-green-800 dark:text-green-200 transition-colors duration-300;
}

.blog-category-general {
  @apply bg-gray-100 dark:bg-gray-700 text-gray-800 dark:text-gray-200 transition-colors duration-300;
}

/* Interactive elements for better UX */
.blog-interactive-card {
  @apply bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-lg hover:shadow-md dark:hover:shadow-gray-600/50 transition-all duration-300;
}

.blog-interactive-card:hover {
  @apply transform -translate-y-1;
}

/* Better focus states for accessibility */
.blog-focus {
  @apply focus:outline-none focus:ring-2 focus:ring-blue-500 dark:focus:ring-blue-400 focus:ring-offset-2 dark:focus:ring-offset-gray-800;
}

/* Typography optimization for dark mode */
.blog-prose {
  @apply max-w-none;
}

.blog-prose h1,
.blog-prose h2,
.blog-prose h3,
.blog-prose h4,
.blog-prose h5,
.blog-prose h6 {
  @apply text-gray-800 dark:text-white font-bold leading-tight;
}

.blog-prose h1 {
  @apply text-4xl mb-6;
}

.blog-prose h2 {
  @apply text-3xl mb-5;
}

.blog-prose h3 {
  @apply text-2xl mb-4;
}

.blog-prose h4 {
  @apply text-xl mb-3;
}

.blog-prose p {
  @apply text-gray-700 dark:text-gray-300 leading-relaxed mb-4;
}

.blog-prose ul {
  @apply mb-4;
}

.blog-prose li {
  @apply text-gray-700 dark:text-gray-300 leading-relaxed;
}

.blog-prose strong {
  @apply text-gray-800 dark:text-white font-semibold;
}

.blog-prose code {
  @apply bg-gray-100 dark:bg-gray-700 text-gray-800 dark:text-gray-200 px-1 py-0.5 rounded text-sm font-mono;
}

.blog-prose blockquote {
  @apply border-l-4 border-blue-500 dark:border-blue-400 bg-blue-50 dark:bg-blue-900/20 text-blue-800 dark:text-blue-200 pl-4 py-2 my-4 rounded-r;
}

/* Responsive design helpers */
@media (max-width: 768px) {
  .blog-mobile-padding {
    @apply px-4;
  }

  .blog-mobile-text {
    @apply text-sm;
  }
}