'use client';

import { useState } from 'react';
import Footer from '@/components/Footer';
import CalculatorHeader from '@/components/CalculatorHeader';
import { containerStyles, textStyles, inputStyles, buttonStyles, statusStyles, cn } from '@/components/ui/styles';

interface BMIForm {
  height: string;
  weight: string;
  unit: 'metric' | 'imperial';
  age: string;
  gender: 'male' | 'female' | '';
}

interface BMIResult {
  bmi: number;
  category: string;
  status: 'underweight' | 'normal' | 'overweight' | 'obese';
  healthyWeightRange: { min: number; max: number };
  weightToLose?: number;
  weightToGain?: number;
}

export default function BMICalculator() {
  const [form, setForm] = useState<BMIForm>({
    height: '',
    weight: '',
    unit: 'metric',
    age: '',
    gender: ''
  });

  const [result, setResult] = useState<BMIResult | null>(null);
  const [isCalculating, setIsCalculating] = useState(false);

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement>) => {
    const { name, value } = e.target;
    setForm(prev => ({ ...prev, [name]: value }));
  };

  const calculateBMI = () => {
    setIsCalculating(true);
    
    let heightInM: number;
    let weightInKg: number;

    if (form.unit === 'metric') {
      heightInM = parseFloat(form.height) / 100; // cm to m
      weightInKg = parseFloat(form.weight);
    } else {
      // Imperial: convert feet/inches to meters, pounds to kg
      const feet = Math.floor(parseFloat(form.height) / 12);
      const inches = parseFloat(form.height) % 12;
      heightInM = (feet * 12 + inches) * 0.0254;
      weightInKg = parseFloat(form.weight) * 0.453592;
    }

    const bmi = weightInKg / (heightInM * heightInM);
    
    let category: string;
    let status: BMIResult['status'];
    
    if (bmi < 18.5) {
      category = 'Underweight';
      status = 'underweight';
    } else if (bmi < 25) {
      category = 'Normal weight';
      status = 'normal';
    } else if (bmi < 30) {
      category = 'Overweight';
      status = 'overweight';
    } else {
      category = 'Obese';
      status = 'obese';
    }

    // Calculate healthy weight range (BMI 18.5-25)
    const minHealthyWeight = 18.5 * heightInM * heightInM;
    const maxHealthyWeight = 25 * heightInM * heightInM;

    const bmiResult: BMIResult = {
      bmi: Math.round(bmi * 10) / 10,
      category,
      status,
      healthyWeightRange: {
        min: form.unit === 'metric' 
          ? Math.round(minHealthyWeight * 10) / 10
          : Math.round(minHealthyWeight * 2.20462 * 10) / 10,
        max: form.unit === 'metric' 
          ? Math.round(maxHealthyWeight * 10) / 10
          : Math.round(maxHealthyWeight * 2.20462 * 10) / 10
      }
    };

    // Calculate weight to lose/gain to reach healthy range
    if (status === 'overweight' || status === 'obese') {
      const targetWeight = maxHealthyWeight;
      bmiResult.weightToLose = form.unit === 'metric' 
        ? Math.round((weightInKg - targetWeight) * 10) / 10
        : Math.round((parseFloat(form.weight) - targetWeight * 2.20462) * 10) / 10;
    } else if (status === 'underweight') {
      const targetWeight = minHealthyWeight;
      bmiResult.weightToGain = form.unit === 'metric'
        ? Math.round((targetWeight - weightInKg) * 10) / 10
        : Math.round((targetWeight * 2.20462 - parseFloat(form.weight)) * 10) / 10;
    }

    setTimeout(() => {
      setResult(bmiResult);
      setIsCalculating(false);
    }, 500);
  };

  const resetForm = () => {
    setForm({
      height: '',
      weight: '',
      unit: 'metric',
      age: '',
      gender: ''
    });
    setResult(null);
  };

  const isFormValid = form.height && form.weight;

  const getStatusColor = (status: BMIResult['status']) => {
    switch (status) {
      case 'underweight': return statusStyles.info.container;
      case 'normal': return statusStyles.success.container;
      case 'overweight': return statusStyles.warning.container;
      case 'obese': return statusStyles.danger.container;
      default: return containerStyles.card;
    }
  };

  return (
    <>
      <div className={containerStyles.page}>
        <div className="container mx-auto px-4">
          <div className="max-w-4xl mx-auto">
            <CalculatorHeader
              title="BMI Calculator"
              description="Calculate your Body Mass Index (BMI) to understand your weight status and get recommendations for a healthy weight range."
              category="Health"
            />

            <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
              {/* Input Form */}
              <div className={containerStyles.form}>
                <h2 className={cn(textStyles.h2, "mb-6")}>Calculate Your BMI</h2>
                
                <div className="space-y-4">
                  {/* Unit System */}
                  <div>
                    <label className={cn(textStyles.label, "mb-2")}>
                      Unit System
                    </label>
                    <div className="flex space-x-4">
                      <label className="flex items-center">
                        <input
                          type="radio"
                          name="unit"
                          value="metric"
                          checked={form.unit === 'metric'}
                          onChange={handleInputChange}
                          className="mr-2"
                        />
                        <span className={textStyles.body}>Metric (kg, cm)</span>
                      </label>
                      <label className="flex items-center">
                        <input
                          type="radio"
                          name="unit"
                          value="imperial"
                          checked={form.unit === 'imperial'}
                          onChange={handleInputChange}
                          className="mr-2"
                        />
                        <span className={textStyles.body}>Imperial (lbs, inches)</span>
                      </label>
                    </div>
                  </div>

                  {/* Height */}
                  <div>
                    <label className={cn(textStyles.label, "mb-2")}>
                      Height ({form.unit === 'metric' ? 'cm' : 'inches'}) *
                    </label>
                    <input
                      type="number"
                      name="height"
                      value={form.height}
                      onChange={handleInputChange}
                      min={form.unit === 'metric' ? '50' : '20'}
                      max={form.unit === 'metric' ? '250' : '100'}
                      step="0.1"
                      className={inputStyles.base}
                      placeholder={form.unit === 'metric' ? 'Enter height in cm' : 'Enter height in inches'}
                    />
                  </div>

                  {/* Weight */}
                  <div>
                    <label className={cn(textStyles.label, "mb-2")}>
                      Weight ({form.unit === 'metric' ? 'kg' : 'lbs'}) *
                    </label>
                    <input
                      type="number"
                      name="weight"
                      value={form.weight}
                      onChange={handleInputChange}
                      min={form.unit === 'metric' ? '20' : '44'}
                      max={form.unit === 'metric' ? '300' : '660'}
                      step="0.1"
                      className={inputStyles.base}
                      placeholder={form.unit === 'metric' ? 'Enter weight in kg' : 'Enter weight in lbs'}
                    />
                  </div>

                  {/* Age (Optional) */}
                  <div>
                    <label className={cn(textStyles.label, "mb-2")}>
                      Age (years) - Optional
                    </label>
                    <input
                      type="number"
                      name="age"
                      value={form.age}
                      onChange={handleInputChange}
                      min="1"
                      max="120"
                      className={inputStyles.base}
                      placeholder="Enter your age"
                    />
                  </div>

                  {/* Gender (Optional) */}
                  <div>
                    <label className={cn(textStyles.label, "mb-2")}>
                      Gender - Optional
                    </label>
                    <select
                      name="gender"
                      value={form.gender}
                      onChange={handleInputChange}
                      className={inputStyles.base}
                    >
                      <option value="">Select gender</option>
                      <option value="male">Male</option>
                      <option value="female">Female</option>
                    </select>
                  </div>

                  {/* Buttons */}
                  <div className="flex space-x-4 pt-4">
                    <button
                      onClick={calculateBMI}
                      disabled={!isFormValid || isCalculating}
                      className={cn(buttonStyles.primary, "flex-1")}
                    >
                      {isCalculating ? 'Calculating...' : 'Calculate BMI'}
                    </button>
                    <button
                      onClick={resetForm}
                      className={buttonStyles.secondary}
                    >
                      Reset
                    </button>
                  </div>
                </div>
              </div>

              {/* Results */}
              <div className={containerStyles.form}>
                <h2 className={cn(textStyles.h2, "mb-6")}>Your Results</h2>
                
                {!result ? (
                  <div className="text-center py-8">
                    <div className="w-16 h-16 bg-gray-200 dark:bg-slate-700 rounded-full flex items-center justify-center mx-auto mb-4">
                      <svg className="w-8 h-8 text-gray-400 dark:text-slate-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z" />
                      </svg>
                    </div>
                    <p className={textStyles.muted}>Enter your height and weight to calculate your BMI.</p>
                  </div>
                ) : (
                  <div className="space-y-6">
                    {/* BMI Score */}
                    <div className="text-center">
                      <div className="text-4xl font-bold text-blue-600 mb-2">{result.bmi}</div>
                      <div className={cn(textStyles.body, "text-lg mb-4")}>Your BMI</div>
                      <div className={cn("inline-block px-4 py-2 rounded-lg border", getStatusColor(result.status))}>
                        <div className="font-semibold">{result.category}</div>
                      </div>
                    </div>

                    {/* Healthy Weight Range */}
                    <div className={containerStyles.card}>
                      <h3 className={cn(textStyles.h3, "mb-2")}>Healthy Weight Range</h3>
                      <div className={cn(textStyles.body, "text-lg")}>
                        {result.healthyWeightRange.min} - {result.healthyWeightRange.max} {form.unit === 'metric' ? 'kg' : 'lbs'}
                      </div>
                    </div>

                    {/* Weight Recommendations */}
                    {(result.weightToLose || result.weightToGain) && (
                      <div className={containerStyles.card}>
                        <h3 className={cn(textStyles.h3, "mb-2")}>Recommendation</h3>
                        {result.weightToLose && (
                          <div className={statusStyles.warning.text}>
                            Consider losing {result.weightToLose} {form.unit === 'metric' ? 'kg' : 'lbs'} to reach a healthy weight range.
                          </div>
                        )}
                        {result.weightToGain && (
                          <div className={statusStyles.info.text}>
                            Consider gaining {result.weightToGain} {form.unit === 'metric' ? 'kg' : 'lbs'} to reach a healthy weight range.
                          </div>
                        )}
                      </div>
                    )}

                    {/* BMI Categories */}
                    <div className={containerStyles.card}>
                      <h3 className={cn(textStyles.h3, "mb-3")}>BMI Categories</h3>
                      <div className={cn(textStyles.muted, "space-y-2")}>
                        <div className="flex justify-between">
                          <span>Underweight:</span>
                          <span>Below 18.5</span>
                        </div>
                        <div className="flex justify-between">
                          <span>Normal weight:</span>
                          <span>18.5 - 24.9</span>
                        </div>
                        <div className="flex justify-between">
                          <span>Overweight:</span>
                          <span>25 - 29.9</span>
                        </div>
                        <div className="flex justify-between">
                          <span>Obese:</span>
                          <span>30 and above</span>
                        </div>
                      </div>
                    </div>
                  </div>
                )}
              </div>
            </div>

            {/* Information */}
            <div className={cn(statusStyles.info.container, "mt-8")}>
              <h3 className={cn(textStyles.h3, "mb-2")}>Important Information</h3>
              <ul className={cn(textStyles.muted, "space-y-1")}>
                <li>• BMI is a screening tool and not a diagnostic measure</li>
                <li>• It may not be accurate for athletes, elderly, or children</li>
                <li>• BMI doesn&apos;t distinguish between muscle and fat</li>
                <li>• Consult healthcare professionals for comprehensive health assessment</li>
                <li>• Other factors like waist circumference and body composition matter too</li>
              </ul>
            </div>
          </div>
        </div>
        <Footer />
      </div>
    </>
  );
}