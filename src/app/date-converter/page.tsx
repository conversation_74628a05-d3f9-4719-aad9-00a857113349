'use client';

import { useState, useEffect } from 'react';
import Footer from "@/components/Footer";
import CalculatorHeader from '@/components/CalculatorHeader';
import { containerStyles, textStyles, inputStyles, statusStyles, cn } from '@/components/ui/styles';

interface ConversionResults {
  days: number;
  weeks: number;
  months: number;
  years: number;
}

export default function DateConverter() {
  const [inputValue, setInputValue] = useState<string>('1');
  const [fromUnit, setFromUnit] = useState<string>('days');
  const [results, setResults] = useState<ConversionResults>({
    days: 1,
    weeks: 0.142857,
    months: 0.032877,
    years: 0.00274
  });

  const convertToAllUnits = (value: number, unit: string): ConversionResults => {
    let totalDays: number;

    // Convert input to days first
    switch (unit) {
      case 'days':
        totalDays = value;
        break;
      case 'weeks':
        totalDays = value * 7;
        break;
      case 'months':
        totalDays = value * 30.44; // Average month length
        break;
      case 'years':
        totalDays = value * 365.25; // Including leap years
        break;
      default:
        totalDays = value;
    }

    // Convert from days to all units
    return {
      days: totalDays,
      weeks: totalDays / 7,
      months: totalDays / 30.44,
      years: totalDays / 365.25
    };
  };

  const handleInputChange = (value: string) => {
    setInputValue(value);
    const numValue = parseFloat(value) || 0;
    setResults(convertToAllUnits(numValue, fromUnit));
  };

  const handleUnitChange = (unit: string) => {
    setFromUnit(unit);
    const numValue = parseFloat(inputValue) || 0;
    setResults(convertToAllUnits(numValue, unit));
  };

  useEffect(() => {
    const numValue = parseFloat(inputValue) || 0;
    setResults(convertToAllUnits(numValue, fromUnit));
  }, [inputValue, fromUnit]);

  const formatNumber = (num: number): string => {
    if (num === 0) return '0';
    if (num < 0.001) return num.toExponential(3);
    if (num < 1) return num.toFixed(6).replace(/\.?0+$/, '');
    if (num < 1000) return num.toFixed(3).replace(/\.?0+$/, '');
    return num.toLocaleString('en-US', { maximumFractionDigits: 2 });
  };

  const units = [
    { value: 'days', label: 'Days', icon: '📅' },
    { value: 'weeks', label: 'Weeks', icon: '🗓️' },
    { value: 'months', label: 'Months', icon: '📆' },
    { value: 'years', label: 'Years', icon: '🗓️' }
  ];

  return (
    <>
      <div className={containerStyles.page}>
        <div className="container mx-auto px-4 py-8">
          <div className="max-w-4xl mx-auto">
            <CalculatorHeader
              title="Date Unit Converter"
              description="Convert between days, weeks, months, and years instantly. Perfect for project planning, age calculations, and time period conversions."
              category="Other"
              className="text-center mb-12"
            />

            <div className={cn(containerStyles.card, "p-8 mb-8")}>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
                {/* Input Section */}
                <div>
                  <h2 className={cn(textStyles.h2, "mb-6")}>Convert From</h2>
                  
                  <div className="space-y-4">
                    <div>
                      <label className={cn(textStyles.label, "block mb-2")}>
                        Enter Value
                      </label>
                      <input
                        type="number"
                        value={inputValue}
                        onChange={(e) => handleInputChange(e.target.value)}
                        className={cn(inputStyles.base, "text-lg")}
                        placeholder="Enter number"
                        min="0"
                        step="any"
                      />
                    </div>

                    <div>
                      <label className={cn(textStyles.label, "block mb-2")}>
                        Unit
                      </label>
                      <div className="grid grid-cols-2 gap-2">
                        {units.map((unit) => (
                          <button
                            key={unit.value}
                            onClick={() => handleUnitChange(unit.value)}
                            className={cn(
                              "p-3 rounded-lg border transition-colors text-center",
                              fromUnit === unit.value
                                ? "bg-blue-500 dark:bg-blue-600 text-white border-blue-500 dark:border-blue-600"
                                : cn(
                                    "border-gray-300 dark:border-gray-600 hover:bg-gray-50 dark:hover:bg-gray-600",
                                    "bg-white dark:bg-gray-700",
                                    textStyles.body
                                  )
                            )}
                          >
                            <div className="text-lg mb-1">{unit.icon}</div>
                            <div className="text-sm font-medium">{unit.label}</div>
                          </button>
                        ))}
                      </div>
                    </div>
                  </div>
                </div>

                {/* Results Section */}
                <div>
                  <h2 className={cn(textStyles.h2, "mb-6")}>Conversion Results</h2>
                  
                  <div className="space-y-4">
                    {units.map((unit) => (
                      <div
                        key={unit.value}
                        className={cn(
                          "p-4 rounded-lg border transition-colors",
                          fromUnit === unit.value
                            ? "bg-blue-100 dark:bg-blue-900/40 border-blue-300 dark:border-blue-600"
                            : cn(containerStyles.cardSmall, "border-gray-200 dark:border-gray-600")
                        )}
                      >
                        <div className="flex items-center justify-between">
                          <div className="flex items-center space-x-3">
                            <span className="text-2xl">{unit.icon}</span>
                            <span className={cn("font-medium", textStyles.body)}>{unit.label}</span>
                          </div>
                          <div className="text-right">
                            <div className={cn("text-xl font-bold", textStyles.h3)}>
                              {formatNumber(results[unit.value as keyof ConversionResults])}
                            </div>
                            <div className={cn("text-sm", textStyles.muted)}>
                              {unit.label.toLowerCase()}
                            </div>
                          </div>
                        </div>
                      </div>
                    ))}
                  </div>
                </div>
              </div>
            </div>

            {/* Quick Examples */}
            <div className={cn(statusStyles.info.container, "mb-8")}>
              <h3 className={cn(statusStyles.info.title, "mb-4")}>Quick Examples</h3>
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
                <button
                  onClick={() => {
                    setInputValue('30');
                    handleUnitChange('days');
                  }}
                  className={cn(
                    "text-left p-3 rounded border transition-colors",
                    "bg-white dark:bg-gray-700 border-gray-200 dark:border-gray-600",
                    "hover:bg-gray-50 dark:hover:bg-gray-600"
                  )}
                >
                  <div className={cn("font-medium", textStyles.body)}>30 Days</div>
                  <div className={cn("text-sm", textStyles.bodySmall)}>≈ 1 month</div>
                </button>
                <button
                  onClick={() => {
                    setInputValue('2');
                    handleUnitChange('weeks');
                  }}
                  className={cn(
                    "text-left p-3 rounded border transition-colors",
                    "bg-white dark:bg-gray-700 border-gray-200 dark:border-gray-600",
                    "hover:bg-gray-50 dark:hover:bg-gray-600"
                  )}
                >
                  <div className={cn("font-medium", textStyles.body)}>2 Weeks</div>
                  <div className={cn("text-sm", textStyles.bodySmall)}>= 14 days</div>
                </button>
                <button
                  onClick={() => {
                    setInputValue('6');
                    handleUnitChange('months');
                  }}
                  className={cn(
                    "text-left p-3 rounded border transition-colors",
                    "bg-white dark:bg-gray-700 border-gray-200 dark:border-gray-600",
                    "hover:bg-gray-50 dark:hover:bg-gray-600"
                  )}
                >
                  <div className={cn("font-medium", textStyles.body)}>6 Months</div>
                  <div className={cn("text-sm", textStyles.bodySmall)}>≈ 0.5 years</div>
                </button>
                <button
                  onClick={() => {
                    setInputValue('1');
                    handleUnitChange('years');
                  }}
                  className={cn(
                    "text-left p-3 rounded border transition-colors",
                    "bg-white dark:bg-gray-700 border-gray-200 dark:border-gray-600",
                    "hover:bg-gray-50 dark:hover:bg-gray-600"
                  )}
                >
                  <div className={cn("font-medium", textStyles.body)}>1 Year</div>
                  <div className={cn("text-sm", textStyles.bodySmall)}>≈ 365 days</div>
                </button>
              </div>
            </div>

            {/* Conversion Information */}
            <div className={cn(containerStyles.card, "p-6 mb-8")}>
              <h3 className={cn(textStyles.h3, "mb-4")}>Conversion Reference</h3>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                  <h4 className={cn("font-medium mb-2", textStyles.h4)}>Standard Conversions</h4>
                  <ul className={cn("space-y-1 text-sm", textStyles.body)}>
                    <li>• 1 week = 7 days</li>
                    <li>• 1 month ≈ 30.44 days (average)</li>
                    <li>• 1 year = 365.25 days (including leap years)</li>
                    <li>• 1 year ≈ 12 months</li>
                  </ul>
                </div>
                <div>
                  <h4 className={cn("font-medium mb-2", textStyles.h4)}>Common Use Cases</h4>
                  <ul className={cn("space-y-1 text-sm", textStyles.body)}>
                    <li>• Project timeline planning</li>
                    <li>• Age calculations</li>
                    <li>• Subscription period conversions</li>
                    <li>• Event planning and scheduling</li>
                  </ul>
                </div>
              </div>
            </div>

            {/* Note about accuracy */}
            <div className={cn(statusStyles.warning.container, "mb-8")}>
              <div className="flex items-start space-x-3">
                <span className="text-yellow-600 dark:text-yellow-400 text-xl">⚠️</span>
                <div>
                  <h4 className={cn("font-medium", statusStyles.warning.title, "mb-0")}>Note on Accuracy</h4>
                  <p className={cn("text-sm mt-1", statusStyles.warning.text)}>
                    Month and year conversions use average values (30.44 days/month, 365.25 days/year). 
                    Actual calendar months vary from 28-31 days, and years may be 365 or 366 days.
                  </p>
                </div>
              </div>
            </div>

          </div>
        </div>
        <Footer />
      </div>
    </>
  );
}