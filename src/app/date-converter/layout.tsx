import { generatePageTitle, generateCanonicalUrl } from '@/lib/utils';
import type { Metadata } from 'next';

export const metadata: Metadata = {
  title: generatePageTitle('Date Unit Converter'),
  description: 'Convert between days, weeks, months, and years instantly. Free online date unit converter for project planning, age calculations, and time period conversions.',
  keywords: 'date converter, time converter, days to weeks, months to years, date unit conversion, time unit calculator, days calculator, weeks calculator, project timeline',
  robots: 'index, follow',
  alternates: {
    canonical: generateCanonicalUrl('/date-converter'),
  },
  openGraph: {
    title: generatePageTitle('Date Unit Converter'),
    description: 'Convert between days, weeks, months, and years instantly. Perfect for project planning and time calculations.',
    type: 'website',
    url: generateCanonicalUrl('/date-converter'),
  },
  twitter: {
    card: 'summary',
    title: generatePageTitle('Date Unit Converter'),
    description: 'Convert between days, weeks, months, and years instantly. Perfect for project planning and time calculations.',
  },
};

export default function DateConverterLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  return children;
}