import type { Metadata } from 'next';

export const metadata: Metadata = {
  title: 'Bandwidth Calculator - Network Speed Converter & Download Time Calculator | CalcHub',
  description: 'Professional bandwidth calculator with unit conversion, download time estimation, website bandwidth planning, and network speed reference. Convert between bps, Kbps, Mbps, Gbps, and Tbps.',
  keywords: 'bandwidth calculator, network speed converter, download time calculator, internet speed, bps, kbps, mbps, gbps, tbps, website bandwidth estimator, data transfer rate',
  openGraph: {
    title: 'Bandwidth Calculator - Network Speed Converter & Download Time Calculator',
    description: 'Professional bandwidth calculator with unit conversion, download time estimation, website bandwidth planning, and network speed reference.',
    type: 'website',
  },
};

export default function BandwidthConverterLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  return children;
}