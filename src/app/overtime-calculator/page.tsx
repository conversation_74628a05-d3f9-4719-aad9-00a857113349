'use client';

import { useState, useEffect, useCallback, Suspense } from 'react';
import { useRouter, useSearchParams } from 'next/navigation';
import Footer from "@/components/Footer";
import CalculatorHeader from '@/components/CalculatorHeader';
import { containerStyles, textStyles, inputStyles, cn } from '@/components/ui/styles';

interface OvertimeResult {
  standardHourlyRate: number;
  regularHours: number;
  tier1Hours: number;
  tier2Hours: number;
  regularPay: number;
  tier1Pay: number;
  tier2Pay: number;
  grossPay: number;
  payPerPeriod: number;
  annualSalary: number;
  effectiveHourlyRate: number;
  tier1Rate: number;
  tier2Rate: number;
}

type PayRateType = 'hour' | 'day' | 'week' | 'month' | 'year';
type UsualWorkType = 'week' | 'day' | 'month';
type PayFrequency = 'weekly' | 'bi-weekly' | 'semi-monthly' | 'monthly';
type OvertimeRateType = 'std' | 'double' | 'triple' | 'custom-x' | 'custom-money';

function OvertimeCalculatorContent() {
  const router = useRouter();
  const searchParams = useSearchParams();
  
  // Form inputs
  const [standardPayRate, setStandardPayRate] = useState<string>('25');
  const [payRateType, setPayRateType] = useState<PayRateType>('hour');
  const [usualWorkHours, setUsualWorkHours] = useState<string>('40');
  const [usualWorkType, setUsualWorkType] = useState<UsualWorkType>('week');
  const [payFrequency, setPayFrequency] = useState<PayFrequency>('weekly');
  const [tier1Hours, setTier1Hours] = useState<string>('10');
  const [tier1RateType, setTier1RateType] = useState<OvertimeRateType>('std');
  const [tier1CustomMultiplier, setTier1CustomMultiplier] = useState<string>('1.5');
  const [tier1CustomMoney, setTier1CustomMoney] = useState<string>('37.50');
  const [tier2Hours, setTier2Hours] = useState<string>('5');
  const [tier2RateType, setTier2RateType] = useState<OvertimeRateType>('double');
  const [tier2CustomMultiplier, setTier2CustomMultiplier] = useState<string>('2');
  const [tier2CustomMoney, setTier2CustomMoney] = useState<string>('50');
  const [currency, setCurrency] = useState<string>('USD');
  
  // Results
  const [result, setResult] = useState<OvertimeResult>({
    standardHourlyRate: 25,
    regularHours: 40,
    tier1Hours: 10,
    tier2Hours: 5,
    regularPay: 1000,
    tier1Pay: 375,
    tier2Pay: 250,
    grossPay: 1625,
    payPerPeriod: 1625,
    annualSalary: 84500,
    effectiveHourlyRate: 29.55,
    tier1Rate: 37.5,
    tier2Rate: 50
  });

  // URL state management
  const updateURL = useCallback((params: Record<string, string>) => {
    const newParams = new URLSearchParams(searchParams);
    Object.entries(params).forEach(([key, value]) => {
      if (value && value !== '') {
        newParams.set(key, value);
      } else {
        newParams.delete(key);
      }
    });
    router.push(`?${newParams.toString()}`, { scroll: false });
  }, [router, searchParams]);

  // Initialize from URL
  useEffect(() => {
    const rate = searchParams.get('standardPayRate') || '25';
    const rateType = searchParams.get('payRateType') || 'hour';
    const workHours = searchParams.get('usualWorkHours') || '40';
    const workType = searchParams.get('usualWorkType') || 'week';
    const frequency = searchParams.get('payFrequency') || 'weekly';
    const t1Hours = searchParams.get('tier1Hours') || '10';
    const t1RateType = searchParams.get('tier1RateType') || 'std';
    const t1CustomX = searchParams.get('tier1CustomMultiplier') || '1.5';
    const t1CustomMoney = searchParams.get('tier1CustomMoney') || '37.50';
    const t2Hours = searchParams.get('tier2Hours') || '5';
    const t2RateType = searchParams.get('tier2RateType') || 'double';
    const t2CustomX = searchParams.get('tier2CustomMultiplier') || '2';
    const t2CustomMoney = searchParams.get('tier2CustomMoney') || '50';
    const curr = searchParams.get('currency') || 'USD';

    setStandardPayRate(rate);
    setPayRateType(rateType as PayRateType);
    setUsualWorkHours(workHours);
    setUsualWorkType(workType as UsualWorkType);
    setPayFrequency(frequency as PayFrequency);
    setTier1Hours(t1Hours);
    setTier1RateType(t1RateType as OvertimeRateType);
    setTier1CustomMultiplier(t1CustomX);
    setTier1CustomMoney(t1CustomMoney);
    setTier2Hours(t2Hours);
    setTier2RateType(t2RateType as OvertimeRateType);
    setTier2CustomMultiplier(t2CustomX);
    setTier2CustomMoney(t2CustomMoney);
    setCurrency(curr);
  }, [searchParams]);

  // Convert pay rate to hourly rate
  const getHourlyRate = useCallback(() => {
    const inputRate = parseFloat(standardPayRate) || 0;
    
    switch (payRateType) {
      case 'hour':
        return inputRate;
      case 'day':
        return inputRate / 8; // Assuming 8 hours per day
      case 'week':
        return inputRate / 40; // Assuming 40 hours per week
      case 'month':
        return inputRate / (40 * 4.33); // ~173 hours per month
      case 'year':
        return inputRate / (40 * 52); // 2080 hours per year
      default:
        return inputRate;
    }
  }, [standardPayRate, payRateType]);

  // Get weekly regular hours
  const getWeeklyRegularHours = useCallback(() => {
    const hours = parseFloat(usualWorkHours) || 0;
    
    switch (usualWorkType) {
      case 'day':
        return hours * 5; // 5 days per week
      case 'week':
        return hours;
      case 'month':
        return hours / 4.33; // Convert monthly to weekly
      default:
        return hours;
    }
  }, [usualWorkHours, usualWorkType]);

  // Calculate overtime rates
  const calculateOvertimeRate = useCallback((rateType: OvertimeRateType, customMultiplier: string, customMoney: string, baseRate: number) => {
    switch (rateType) {
      case 'std':
        return baseRate * 1.5;
      case 'double':
        return baseRate * 2;
      case 'triple':
        return baseRate * 3;
      case 'custom-x':
        return baseRate * (parseFloat(customMultiplier) || 1);
      case 'custom-money':
        return parseFloat(customMoney) || baseRate;
      default:
        return baseRate * 1.5;
    }
  }, []);

  // Calculate pay per period based on frequency
  const calculatePayPerPeriod = useCallback((weeklyPay: number) => {
    switch (payFrequency) {
      case 'weekly':
        return weeklyPay;
      case 'bi-weekly':
        return weeklyPay * 2;
      case 'semi-monthly':
        return weeklyPay * 2.17; // 52 weeks / 24 pay periods
      case 'monthly':
        return weeklyPay * 4.33; // 52 weeks / 12 months
      default:
        return weeklyPay;
    }
  }, [payFrequency]);

  // Calculate results
  const calculateOvertime = useCallback(() => {
    const hourlyRate = getHourlyRate();
    const weeklyRegularHours = getWeeklyRegularHours();
    const t1Hours = parseFloat(tier1Hours) || 0;
    const t2Hours = parseFloat(tier2Hours) || 0;
    
    // Calculate overtime rates
    const tier1Rate = calculateOvertimeRate(tier1RateType, tier1CustomMultiplier, tier1CustomMoney, hourlyRate);
    const tier2Rate = calculateOvertimeRate(tier2RateType, tier2CustomMultiplier, tier2CustomMoney, hourlyRate);
    
    // Calculate weekly pay
    const regularPay = hourlyRate * weeklyRegularHours;
    const tier1Pay = tier1Rate * t1Hours;
    const tier2Pay = tier2Rate * t2Hours;
    const weeklyGrossPay = regularPay + tier1Pay + tier2Pay;
    
    // Calculate other metrics
    const payPerPeriod = calculatePayPerPeriod(weeklyGrossPay);
    const annualSalary = weeklyGrossPay * 52;
    const totalWeeklyHours = weeklyRegularHours + t1Hours + t2Hours;
    const effectiveHourlyRate = totalWeeklyHours > 0 ? weeklyGrossPay / totalWeeklyHours : hourlyRate;
    
    setResult({
      standardHourlyRate: hourlyRate,
      regularHours: weeklyRegularHours,
      tier1Hours: t1Hours,
      tier2Hours: t2Hours,
      regularPay,
      tier1Pay,
      tier2Pay,
      grossPay: weeklyGrossPay,
      payPerPeriod,
      annualSalary,
      effectiveHourlyRate,
      tier1Rate,
      tier2Rate
    });
  }, [getHourlyRate, getWeeklyRegularHours, tier1Hours, tier2Hours, tier1RateType, tier1CustomMultiplier, tier1CustomMoney, tier2RateType, tier2CustomMultiplier, tier2CustomMoney, calculateOvertimeRate, calculatePayPerPeriod]);

  useEffect(() => {
    calculateOvertime();
  }, [calculateOvertime]);

  // Format currency
  const formatCurrency = (amount: number): string => {
    const currencyMap: Record<string, string> = {
      USD: 'en-US',
      EUR: 'de-DE',
      GBP: 'en-GB',
      JPY: 'ja-JP',
      CNY: 'zh-CN',
      CAD: 'en-CA',
      AUD: 'en-AU',
    };
    
    return new Intl.NumberFormat(currencyMap[currency] || 'en-US', {
      style: 'currency',
      currency: currency,
      minimumFractionDigits: currency === 'JPY' ? 0 : 2,
      maximumFractionDigits: currency === 'JPY' ? 0 : 2,
    }).format(amount);
  };

  // Format number
  const formatNumber = (num: number): string => {
    return new Intl.NumberFormat('en-US', {
      minimumFractionDigits: 1,
      maximumFractionDigits: 2,
    }).format(num);
  };

  // Get rate type label
  const getRateTypeLabel = (rateType: OvertimeRateType, customMultiplier: string, customMoney: string) => {
    switch (rateType) {
      case 'std':
        return '1.5x Standard';
      case 'double':
        return '2x Double';
      case 'triple':
        return '3x Triple';
      case 'custom-x':
        return `${customMultiplier}x Custom`;
      case 'custom-money':
        return `${formatCurrency(parseFloat(customMoney) || 0)}/hr`;
      default:
        return '1.5x Standard';
    }
  };

  return (
    <>
      <div className={containerStyles.page}>
        <div className="container mx-auto px-4 py-8">
          <div className="max-w-6xl mx-auto">
            <CalculatorHeader
              title="Overtime Calculator"
              description="Calculate your overtime pay with flexible tier-based rates. Set your standard pay rate, usual work hours, and customize overtime rates for different scenarios."
              category="Financial"
              className="text-center mb-12"
            />

            {/* Calculator Input Section */}
            <div className={cn(containerStyles.card, "p-8 mb-8")}>
              <div className="grid grid-cols-1 xl:grid-cols-3 gap-8">
                {/* Basic Information */}
                <div className="space-y-6">
                  <h2 className={cn(textStyles.h2, "mb-6 text-blue-600 dark:text-blue-400")}>Basic Information</h2>
                  
                  <div>
                    <label className={cn(textStyles.label, "block mb-2")}>Currency</label>
                    <select
                      value={currency}
                      onChange={(e) => {
                        setCurrency(e.target.value);
                        updateURL({ currency: e.target.value });
                      }}
                      className={cn(inputStyles.base)}
                    >
                      <option value="USD">USD ($)</option>
                      <option value="EUR">EUR (€)</option>
                      <option value="GBP">GBP (£)</option>
                      <option value="JPY">JPY (¥)</option>
                      <option value="CNY">CNY (¥)</option>
                      <option value="CAD">CAD ($)</option>
                      <option value="AUD">AUD ($)</option>
                    </select>
                  </div>
                  
                  <div>
                    <label className={cn(textStyles.label, "block mb-2")}>Standard pay rate</label>
                    <div className="grid grid-cols-2 gap-2">
                      <div className="relative">
                        <span className="absolute left-3 top-1/2 -translate-y-1/2 text-gray-500">
                          {currency === 'EUR' ? '€' : currency === 'GBP' ? '£' : currency === 'JPY' || currency === 'CNY' ? '¥' : '$'}
                        </span>
                        <input
                          type="number"
                          value={standardPayRate}
                          onChange={(e) => {
                            setStandardPayRate(e.target.value);
                            updateURL({ standardPayRate: e.target.value });
                          }}
                          className={cn(inputStyles.base, "pl-8")}
                          placeholder="25.00"
                          step="0.01"
                          min="0"
                        />
                      </div>
                      <select
                        value={payRateType}
                        onChange={(e) => {
                          setPayRateType(e.target.value as PayRateType);
                          updateURL({ payRateType: e.target.value });
                        }}
                        className={cn(inputStyles.base)}
                      >
                        <option value="hour">per hour</option>
                        <option value="day">per day</option>
                        <option value="week">per week</option>
                        <option value="month">per month</option>
                        <option value="year">per year</option>
                      </select>
                    </div>
                    <p className={cn("text-xs mt-1", textStyles.muted)}>
                      Your base pay rate before overtime
                    </p>
                  </div>
                  
                  <div>
                    <label className={cn(textStyles.label, "block mb-2")}>How many hours do you usually work?</label>
                    <div className="grid grid-cols-2 gap-2">
                      <input
                        type="number"
                        value={usualWorkHours}
                        onChange={(e) => {
                          setUsualWorkHours(e.target.value);
                          updateURL({ usualWorkHours: e.target.value });
                        }}
                        className={cn(inputStyles.base)}
                        placeholder="40"
                        step="0.5"
                        min="0"
                      />
                      <select
                        value={usualWorkType}
                        onChange={(e) => {
                          setUsualWorkType(e.target.value as UsualWorkType);
                          updateURL({ usualWorkType: e.target.value });
                        }}
                        className={cn(inputStyles.base)}
                      >
                        <option value="week">per week</option>
                        <option value="day">per day</option>
                        <option value="month">per month</option>
                      </select>
                    </div>
                    <p className={cn("text-xs mt-1", textStyles.muted)}>
                      Your typical work schedule
                    </p>
                  </div>
                  
                  <div>
                    <label className={cn(textStyles.label, "block mb-2")}>How often are you paid?</label>
                    <select
                      value={payFrequency}
                      onChange={(e) => {
                        setPayFrequency(e.target.value as PayFrequency);
                        updateURL({ payFrequency: e.target.value });
                      }}
                      className={cn(inputStyles.base)}
                    >
                      <option value="weekly">Weekly</option>
                      <option value="bi-weekly">Bi-weekly</option>
                      <option value="semi-monthly">Semi-monthly</option>
                      <option value="monthly">Monthly</option>
                    </select>
                    <p className={cn("text-xs mt-1", textStyles.muted)}>
                      Your payroll frequency
                    </p>
                  </div>
                </div>
                
                {/* Overtime Tiers */}
                <div className="space-y-6">
                  <h2 className={cn(textStyles.h2, "mb-6 text-green-600 dark:text-green-400")}>Overtime Tiers</h2>
                  
                  <div>
                    <label className={cn(textStyles.label, "block mb-2")}>Tier 1 Overtime Hours (per week)</label>
                    <div className="grid grid-cols-2 gap-2">
                      <input
                        type="number"
                        value={tier1Hours}
                        onChange={(e) => {
                          setTier1Hours(e.target.value);
                          updateURL({ tier1Hours: e.target.value });
                        }}
                        className={cn(inputStyles.base)}
                        placeholder="10"
                        step="0.5"
                        min="0"
                      />
                      <select
                        value={tier1RateType}
                        onChange={(e) => {
                          setTier1RateType(e.target.value as OvertimeRateType);
                          updateURL({ tier1RateType: e.target.value });
                        }}
                        className={cn(inputStyles.base)}
                      >
                        <option value="std">1.5x Std</option>
                        <option value="double">2x Double</option>
                        <option value="triple">3x Triple</option>
                        <option value="custom-x">Custom x</option>
                        <option value="custom-money">Custom Money</option>
                      </select>
                    </div>
                    
                    {tier1RateType === 'custom-x' && (
                      <div className="mt-2">
                        <input
                          type="number"
                          value={tier1CustomMultiplier}
                          onChange={(e) => {
                            setTier1CustomMultiplier(e.target.value);
                            updateURL({ tier1CustomMultiplier: e.target.value });
                          }}
                          className={cn(inputStyles.base)}
                          placeholder="1.5"
                          step="0.1"
                          min="1"
                        />
                        <p className={cn("text-xs mt-1", textStyles.muted)}>
                          Custom multiplier (e.g., 1.5 for time and a half)
                        </p>
                      </div>
                    )}
                    
                    {tier1RateType === 'custom-money' && (
                      <div className="mt-2">
                        <div className="relative">
                          <span className="absolute left-3 top-1/2 -translate-y-1/2 text-gray-500">
                            {currency === 'EUR' ? '€' : currency === 'GBP' ? '£' : currency === 'JPY' || currency === 'CNY' ? '¥' : '$'}
                          </span>
                          <input
                            type="number"
                            value={tier1CustomMoney}
                            onChange={(e) => {
                              setTier1CustomMoney(e.target.value);
                              updateURL({ tier1CustomMoney: e.target.value });
                            }}
                            className={cn(inputStyles.base, "pl-8")}
                            placeholder="37.50"
                            step="0.01"
                            min="0"
                          />
                        </div>
                        <p className={cn("text-xs mt-1", textStyles.muted)}>
                          Fixed hourly rate for this tier
                        </p>
                      </div>
                    )}
                  </div>
                  
                  <div>
                    <label className={cn(textStyles.label, "block mb-2")}>Tier 2 Overtime Hours (per week)</label>
                    <div className="grid grid-cols-2 gap-2">
                      <input
                        type="number"
                        value={tier2Hours}
                        onChange={(e) => {
                          setTier2Hours(e.target.value);
                          updateURL({ tier2Hours: e.target.value });
                        }}
                        className={cn(inputStyles.base)}
                        placeholder="5"
                        step="0.5"
                        min="0"
                      />
                      <select
                        value={tier2RateType}
                        onChange={(e) => {
                          setTier2RateType(e.target.value as OvertimeRateType);
                          updateURL({ tier2RateType: e.target.value });
                        }}
                        className={cn(inputStyles.base)}
                      >
                        <option value="std">1.5x Std</option>
                        <option value="double">2x Double</option>
                        <option value="triple">3x Triple</option>
                        <option value="custom-x">Custom x</option>
                        <option value="custom-money">Custom Money</option>
                      </select>
                    </div>
                    
                    {tier2RateType === 'custom-x' && (
                      <div className="mt-2">
                        <input
                          type="number"
                          value={tier2CustomMultiplier}
                          onChange={(e) => {
                            setTier2CustomMultiplier(e.target.value);
                            updateURL({ tier2CustomMultiplier: e.target.value });
                          }}
                          className={cn(inputStyles.base)}
                          placeholder="2"
                          step="0.1"
                          min="1"
                        />
                        <p className={cn("text-xs mt-1", textStyles.muted)}>
                          Custom multiplier (e.g., 2 for double time)
                        </p>
                      </div>
                    )}
                    
                    {tier2RateType === 'custom-money' && (
                      <div className="mt-2">
                        <div className="relative">
                          <span className="absolute left-3 top-1/2 -translate-y-1/2 text-gray-500">
                            {currency === 'EUR' ? '€' : currency === 'GBP' ? '£' : currency === 'JPY' || currency === 'CNY' ? '¥' : '$'}
                          </span>
                          <input
                            type="number"
                            value={tier2CustomMoney}
                            onChange={(e) => {
                              setTier2CustomMoney(e.target.value);
                              updateURL({ tier2CustomMoney: e.target.value });
                            }}
                            className={cn(inputStyles.base, "pl-8")}
                            placeholder="50"
                            step="0.01"
                            min="0"
                          />
                        </div>
                        <p className={cn("text-xs mt-1", textStyles.muted)}>
                          Fixed hourly rate for this tier
                        </p>
                      </div>
                    )}
                  </div>
                </div>
                
                {/* Results */}
                <div className="space-y-6">
                  <h2 className={cn(textStyles.h2, "mb-6 text-purple-600 dark:text-purple-400")}>Calculation Results</h2>
                  
                  <div className="space-y-4">
                    <div className={cn("p-4 rounded-lg", "bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800")}>
                      <div className="flex justify-between items-center">
                        <span className={textStyles.body}>Standard Rate</span>
                        <span className={cn("font-medium", textStyles.body)}>{formatCurrency(result.standardHourlyRate)}/hr</span>
                      </div>
                    </div>
                    
                    <div className={cn("p-4 rounded-lg", "bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800")}>
                      <div className="flex justify-between items-center">
                        <span className={textStyles.body}>Regular Pay (Weekly)</span>
                        <span className={cn("font-medium", textStyles.body)}>{formatCurrency(result.regularPay)}</span>
                      </div>
                      <div className="text-xs text-gray-600 dark:text-gray-400 mt-1">
                        {formatNumber(result.regularHours)} hrs × {formatCurrency(result.standardHourlyRate)}/hr
                      </div>
                    </div>
                    
                    <div className={cn("p-4 rounded-lg", "bg-green-50 dark:bg-green-900/20 border border-green-200 dark:border-green-800")}>
                      <div className="flex justify-between items-center">
                        <span className={textStyles.body}>Tier 1 Pay ({getRateTypeLabel(tier1RateType, tier1CustomMultiplier, tier1CustomMoney)})</span>
                        <span className={cn("font-medium", textStyles.body)}>{formatCurrency(result.tier1Pay)}</span>
                      </div>
                      <div className="text-xs text-gray-600 dark:text-gray-400 mt-1">
                        {formatNumber(result.tier1Hours)} hrs × {formatCurrency(result.tier1Rate)}/hr
                      </div>
                    </div>
                    
                    <div className={cn("p-4 rounded-lg", "bg-purple-50 dark:bg-purple-900/20 border border-purple-200 dark:border-purple-800")}>
                      <div className="flex justify-between items-center">
                        <span className={textStyles.body}>Tier 2 Pay ({getRateTypeLabel(tier2RateType, tier2CustomMultiplier, tier2CustomMoney)})</span>
                        <span className={cn("font-medium", textStyles.body)}>{formatCurrency(result.tier2Pay)}</span>
                      </div>
                      <div className="text-xs text-gray-600 dark:text-gray-400 mt-1">
                        {formatNumber(result.tier2Hours)} hrs × {formatCurrency(result.tier2Rate)}/hr
                      </div>
                    </div>
                    
                    <div className={cn("p-4 rounded-lg", "bg-gray-100 dark:bg-gray-800")}>
                      <div className="flex justify-between items-center">
                        <span className={cn("font-semibold", textStyles.body)}>Weekly Gross Pay</span>
                        <span className={cn("font-semibold", textStyles.body)}>{formatCurrency(result.grossPay)}</span>
                      </div>
                    </div>
                    
                    <div className={cn("p-4 rounded-lg", "bg-yellow-50 dark:bg-yellow-900/20 border border-yellow-200 dark:border-yellow-800")}>
                      <div className="flex justify-between items-center">
                        <span className={cn("font-bold text-lg", textStyles.body)}>Pay Per Period</span>
                        <span className={cn("font-bold text-2xl", "text-yellow-600 dark:text-yellow-400")}>{formatCurrency(result.payPerPeriod)}</span>
                      </div>
                      <div className="text-xs text-gray-600 dark:text-gray-400 mt-1">
                        Based on {payFrequency} pay schedule
                      </div>
                    </div>
                    
                    <div className={cn("p-4 rounded-lg", "bg-orange-50 dark:bg-orange-900/20 border border-orange-200 dark:border-orange-800")}>
                      <div className="flex justify-between items-center">
                        <span className={textStyles.body}>Annual Salary</span>
                        <span className={cn("font-medium", textStyles.body)}>{formatCurrency(result.annualSalary)}</span>
                      </div>
                    </div>
                    
                    <div className={cn("p-4 rounded-lg", "bg-gray-50 dark:bg-gray-800")}>
                      <div className="flex justify-between items-center">
                        <span className={textStyles.body}>Effective Hourly Rate</span>
                        <span className={cn("font-medium", textStyles.body)}>{formatCurrency(result.effectiveHourlyRate)}/hr</span>
                      </div>
                      <div className="text-xs text-gray-600 dark:text-gray-400 mt-1">
                        Average rate including overtime
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>

            {/* Rate Summary */}
            <div className={cn(containerStyles.card, "p-6 mb-8")}>
              <h3 className={cn(textStyles.h3, "mb-4")}>Rate Summary</h3>
              <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
                <div className="text-center">
                  <div className="text-2xl font-bold text-blue-600 dark:text-blue-400 mb-2">
                    {formatCurrency(result.standardHourlyRate)}
                  </div>
                  <div className="text-sm font-medium mb-1">Standard Rate</div>
                  <div className="text-xs text-gray-600 dark:text-gray-400">Regular hours</div>
                </div>
                <div className="text-center">
                  <div className="text-2xl font-bold text-green-600 dark:text-green-400 mb-2">
                    {formatCurrency(result.tier1Rate)}
                  </div>
                  <div className="text-sm font-medium mb-1">Tier 1 Rate</div>
                  <div className="text-xs text-gray-600 dark:text-gray-400">{getRateTypeLabel(tier1RateType, tier1CustomMultiplier, tier1CustomMoney)}</div>
                </div>
                <div className="text-center">
                  <div className="text-2xl font-bold text-purple-600 dark:text-purple-400 mb-2">
                    {formatCurrency(result.tier2Rate)}
                  </div>
                  <div className="text-sm font-medium mb-1">Tier 2 Rate</div>
                  <div className="text-xs text-gray-600 dark:text-gray-400">{getRateTypeLabel(tier2RateType, tier2CustomMultiplier, tier2CustomMoney)}</div>
                </div>
                <div className="text-center">
                  <div className="text-2xl font-bold text-orange-600 dark:text-orange-400 mb-2">
                    {formatCurrency(result.effectiveHourlyRate)}
                  </div>
                  <div className="text-sm font-medium mb-1">Effective Rate</div>
                  <div className="text-xs text-gray-600 dark:text-gray-400">Average hourly income</div>
                </div>
              </div>
            </div>

            {/* Information Section */}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-8 mb-8">
              <div className={cn(containerStyles.card, "p-6")}>
                <h3 className={cn(textStyles.h3, "mb-4")}>Overtime Rules</h3>
                <ul className={cn("space-y-3 text-sm", textStyles.body)}>
                  <li className="flex items-start space-x-2">
                    <span className="text-blue-500 mt-1">•</span>
                    <span><strong>Federal Law:</strong> Overtime after 40 hours per week</span>
                  </li>
                  <li className="flex items-start space-x-2">
                    <span className="text-blue-500 mt-1">•</span>
                    <span><strong>Time and a Half:</strong> 1.5x regular rate for overtime</span>
                  </li>
                  <li className="flex items-start space-x-2">
                    <span className="text-blue-500 mt-1">•</span>
                    <span><strong>Double Time:</strong> 2x rate for holidays or long shifts</span>
                  </li>
                  <li className="flex items-start space-x-2">
                    <span className="text-blue-500 mt-1">•</span>
                    <span><strong>State Laws:</strong> Some states have daily overtime rules</span>
                  </li>
                </ul>
              </div>

              <div className={cn(containerStyles.card, "p-6")}>
                <h3 className={cn(textStyles.h3, "mb-4")}>Pay Frequency Impact</h3>
                <ul className={cn("space-y-3 text-sm", textStyles.body)}>
                  <li className="flex items-start space-x-2">
                    <span className="text-green-500 mt-1">•</span>
                    <span><strong>Weekly:</strong> 52 pay periods per year</span>
                  </li>
                  <li className="flex items-start space-x-2">
                    <span className="text-green-500 mt-1">•</span>
                    <span><strong>Bi-weekly:</strong> 26 pay periods per year</span>
                  </li>
                  <li className="flex items-start space-x-2">
                    <span className="text-green-500 mt-1">•</span>
                    <span><strong>Semi-monthly:</strong> 24 pay periods per year</span>
                  </li>
                  <li className="flex items-start space-x-2">
                    <span className="text-green-500 mt-1">•</span>
                    <span><strong>Monthly:</strong> 12 pay periods per year</span>
                  </li>
                </ul>
              </div>
            </div>

            {/* Examples Section */}
            <div className={cn(containerStyles.card, "p-6 mb-8")}>
              <h3 className={cn(textStyles.h3, "mb-4")}>Overtime Examples</h3>
              <div className="space-y-4">
                <div className={cn("p-4 rounded-lg", "bg-gray-50 dark:bg-gray-800")}>
                  <h4 className={cn("font-semibold mb-2", textStyles.body)}>Healthcare Worker: $30/hr base, 40 regular + 10 tier-1 (1.5x) + 5 tier-2 (2x)</h4>
                  <div className="grid grid-cols-1 md:grid-cols-4 gap-4 text-sm">
                    <div>
                      <span className="font-medium">Regular:</span> 40 hrs × $30 = $1,200
                    </div>
                    <div>
                      <span className="font-medium">Tier 1:</span> 10 hrs × $45 = $450
                    </div>
                    <div>
                      <span className="font-medium">Tier 2:</span> 5 hrs × $60 = $300
                    </div>
                    <div>
                      <span className="font-medium text-green-600 dark:text-green-400">Weekly Total:</span> $1,950
                    </div>
                  </div>
                </div>

                <div className={cn("p-4 rounded-lg", "bg-gray-50 dark:bg-gray-800")}>
                  <h4 className={cn("font-semibold mb-2", textStyles.body)}>Manufacturing Worker: $25/hr base, 40 regular + 8 tier-1 (1.5x) + 2 tier-2 (3x)</h4>
                  <div className="grid grid-cols-1 md:grid-cols-4 gap-4 text-sm">
                    <div>
                      <span className="font-medium">Regular:</span> 40 hrs × $25 = $1,000
                    </div>
                    <div>
                      <span className="font-medium">Tier 1:</span> 8 hrs × $37.50 = $300
                    </div>
                    <div>
                      <span className="font-medium">Tier 2:</span> 2 hrs × $75 = $150
                    </div>
                    <div>
                      <span className="font-medium text-green-600 dark:text-green-400">Weekly Total:</span> $1,450
                    </div>
                  </div>
                </div>
              </div>
            </div>

          </div>
        </div>
      </div>
      <Footer />
    </>
  );
}

export default function OvertimeCalculator() {
  return (
    <Suspense fallback={<div>Loading...</div>}>
      <OvertimeCalculatorContent />
    </Suspense>
  );
}