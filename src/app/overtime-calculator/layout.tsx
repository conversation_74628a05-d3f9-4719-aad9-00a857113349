import type { Metadata } from 'next'

export const metadata: Metadata = {
  title: 'Overtime Calculator | Calculate Overtime Pay with Flexible Tiers - Calc9.com',
  description: 'Professional overtime calculator with tier-based rates. Calculate standard pay, overtime hours, and customize rates for different scenarios. Supports multiple pay frequencies and currencies.',
  keywords: 'overtime calculator, overtime pay calculator, time and a half calculator, double time calculator, tier overtime calculator, payroll calculator, overtime rate calculator',
  openGraph: {
    title: 'Overtime Calculator | Calculate Overtime Pay with Flexible Tiers',
    description: 'Professional overtime calculator with tier-based rates. Calculate standard pay, overtime hours, and customize rates for different scenarios. Supports multiple pay frequencies and currencies.',
    type: 'website',
  },
  alternates: {
    canonical: '/overtime-calculator',
  },
}

export default function OvertimeCalculatorLayout({
  children,
}: {
  children: React.ReactNode
}) {
  return <>{children}</>
}