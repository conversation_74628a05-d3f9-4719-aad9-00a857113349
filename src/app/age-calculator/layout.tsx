import { generatePageTitle, generateCanonicalUrl } from '@/lib/utils';
import type { Metadata } from 'next';

export const metadata: Metadata = {
  title: generatePageTitle('Age Calculator'),
  description: 'Calculate your exact age in years, months, days, hours, minutes, and seconds. Find out how old you are with our precise age calculator tool.',
  keywords: 'age calculator, calculate age, birth date calculator, age in days, age in hours, age counter, birthday calculator, age difference',
  robots: 'index, follow',
  alternates: {
    canonical: generateCanonicalUrl('/age-calculator'),
  },
  openGraph: {
    title: generatePageTitle('Age Calculator'),
    description: 'Calculate your exact age in years, months, days, hours, minutes, and seconds. Precise age calculation tool.',
    type: 'website',
    url: generateCanonicalUrl('/age-calculator'),
  },
  twitter: {
    card: 'summary',
    title: generatePageTitle('Age Calculator'),
    description: 'Calculate your exact age in years, months, days, hours, minutes, and seconds. Precise age calculation tool.',
  },
};

export default function AgeCalculatorLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  return children;
}