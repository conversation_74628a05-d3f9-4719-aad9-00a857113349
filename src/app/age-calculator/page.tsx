'use client';

import { useState, useEffect, useCallback } from 'react';
import Footer from "@/components/Footer";
import CalculatorHeader from '@/components/CalculatorHeader';
import { containerStyles } from '@/components/ui/styles';

interface AgeResult {
  years: number;
  months: number;
  days: number;
  totalDays: number;
  totalHours: number;
  totalMinutes: number;
  totalSeconds: number;
  nextBirthday: {
    days: number;
    date: string;
  };
  zodiacSign: string;
  lifeStage: string;
}

export default function AgeCalculator() {
  const [birthDate, setBirthDate] = useState<string>('');
  const [currentDate, setCurrentDate] = useState<string>('');
  const [result, setResult] = useState<AgeResult | null>(null);
  const [useCurrentDate, setUseCurrentDate] = useState<boolean>(true);

  const getZodiacSign = useCallback((date: Date): string => {
    const zodiacSigns = [
      { name: '<PERSON><PERSON><PERSON>', start: [12, 22], end: [1, 19], symbol: '♑' },
      { name: 'Aquarius', start: [1, 20], end: [2, 18], symbol: '♒' },
      { name: '<PERSON><PERSON><PERSON>', start: [2, 19], end: [3, 20], symbol: '♓' },
      { name: 'Aries', start: [3, 21], end: [4, 19], symbol: '♈' },
      { name: 'Taurus', start: [4, 20], end: [5, 20], symbol: '♉' },
      { name: 'Gemini', start: [5, 21], end: [6, 20], symbol: '♊' },
      { name: 'Cancer', start: [6, 21], end: [7, 22], symbol: '♋' },
      { name: 'Leo', start: [7, 23], end: [8, 22], symbol: '♌' },
      { name: 'Virgo', start: [8, 23], end: [9, 22], symbol: '♍' },
      { name: 'Libra', start: [9, 23], end: [10, 22], symbol: '♎' },
      { name: 'Scorpio', start: [10, 23], end: [11, 21], symbol: '♏' },
      { name: 'Sagittarius', start: [11, 22], end: [12, 21], symbol: '♐' }
    ];

    const month = date.getMonth() + 1;
    const day = date.getDate();

    for (const sign of zodiacSigns) {
      const [startMonth, startDay] = sign.start;
      const [endMonth, endDay] = sign.end;
      
      if (
        (month === startMonth && day >= startDay) ||
        (month === endMonth && day <= endDay)
      ) {
        return `${sign.symbol} ${sign.name}`;
      }
    }
    
    // Handle Capricorn wrap-around
    if ((month === 12 && day >= 22) || (month === 1 && day <= 19)) {
      return '♑ Capricorn';
    }
    
    return 'Unknown';
  }, []);

  const getLifeStage = useCallback((age: number): string => {
    if (age < 1) return 'Infant (0-1 year)';
    if (age < 3) return 'Toddler (1-3 years)';
    if (age < 6) return 'Preschooler (3-6 years)';
    if (age < 12) return 'Child (6-12 years)';
    if (age < 18) return 'Teenager (12-18 years)';
    if (age < 25) return 'Young Adult (18-25 years)';
    if (age < 40) return 'Adult (25-40 years)';
    if (age < 60) return 'Middle-aged (40-60 years)';
    if (age < 80) return 'Senior (60-80 years)';
    return 'Elderly (80+ years)';
  }, []);

  const calculateAge = useCallback(() => {
    if (!birthDate) return;

    const birth = new Date(birthDate);
    const today = useCurrentDate ? new Date() : new Date(currentDate);
    
    if (birth > today) {
      return;
    }

    // Calculate age components
    let years = today.getFullYear() - birth.getFullYear();
    let months = today.getMonth() - birth.getMonth();
    let days = today.getDate() - birth.getDate();

    if (days < 0) {
      months--;
      const lastMonthDate = new Date(today.getFullYear(), today.getMonth(), 0);
      days += lastMonthDate.getDate();
    }

    if (months < 0) {
      years--;
      months += 12;
    }

    // Calculate totals
    const totalMilliseconds = today.getTime() - birth.getTime();
    const totalDays = Math.floor(totalMilliseconds / (1000 * 60 * 60 * 24));
    const totalHours = Math.floor(totalMilliseconds / (1000 * 60 * 60));
    const totalMinutes = Math.floor(totalMilliseconds / (1000 * 60));
    const totalSeconds = Math.floor(totalMilliseconds / 1000);

    // Calculate next birthday
    const nextBirthday = new Date(today.getFullYear(), birth.getMonth(), birth.getDate());
    if (nextBirthday <= today) {
      nextBirthday.setFullYear(today.getFullYear() + 1);
    }
    const daysToNextBirthday = Math.ceil((nextBirthday.getTime() - today.getTime()) / (1000 * 60 * 60 * 24));

    const ageResult: AgeResult = {
      years,
      months,
      days,
      totalDays,
      totalHours,
      totalMinutes,
      totalSeconds,
      nextBirthday: {
        days: daysToNextBirthday,
        date: nextBirthday.toLocaleDateString('en-US', { 
          year: 'numeric', 
          month: 'long', 
          day: 'numeric' 
        })
      },
      zodiacSign: getZodiacSign(birth),
      lifeStage: getLifeStage(years)
    };

    setResult(ageResult);
  }, [birthDate, currentDate, useCurrentDate, getZodiacSign, getLifeStage]);

  useEffect(() => {
    if (birthDate) {
      calculateAge();
    }
  }, [birthDate, currentDate, useCurrentDate, calculateAge]);

  useEffect(() => {
    // Set default current date
    const today = new Date();
    setCurrentDate(today.toISOString().split('T')[0]);
  }, []);

  const formatNumber = (num: number): string => {
    return num.toLocaleString();
  };

  return (
    <>
      <div className={containerStyles.page}>
        <div className="container mx-auto px-4">
          <div className="max-w-6xl mx-auto">
            <CalculatorHeader
              title="Age Calculator"
              description="Calculate your exact age in years, months, days, hours, minutes, and seconds. Discover interesting facts about your age and when your next birthday will be."
              category="Other"
              className="text-center mb-12"
            />

            <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
              {/* Calculator Section */}
              <div className="bg-white dark:bg-gray-800 rounded-lg shadow-lg dark:shadow-gray-700/50 p-6 border border-gray-200 dark:border-gray-700">
                <h2 className="text-2xl font-semibold text-gray-900 dark:text-white mb-6">Calculate Your Age</h2>

                <div className="space-y-6">
                  {/* Birth Date Input */}
                  <div>
                    <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                      Birth Date
                    </label>
                    <input
                      type="date"
                      value={birthDate}
                      onChange={(e) => setBirthDate(e.target.value)}
                      max={new Date().toISOString().split('T')[0]}
                      className="w-full px-3 py-2 bg-white dark:bg-gray-700 border border-gray-300 dark:border-gray-600 rounded-md text-gray-900 dark:text-white placeholder-gray-500 dark:placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-blue-500 dark:focus:ring-blue-400 focus:border-blue-500 dark:focus:border-blue-400 transition-colors"
                    />
                  </div>

                  {/* Current Date Options */}
                  <div>
                    <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                      Calculate Age As Of
                    </label>
                    <div className="space-y-3">
                      <label className="flex items-center text-gray-700 dark:text-gray-300">
                        <input
                          type="radio"
                          checked={useCurrentDate}
                          onChange={() => setUseCurrentDate(true)}
                          className="mr-2 text-blue-600 dark:text-blue-400 focus:ring-blue-500 dark:focus:ring-blue-400"
                        />
                        <span>Today (Live calculation)</span>
                      </label>
                      <label className="flex items-center text-gray-700 dark:text-gray-300">
                        <input
                          type="radio"
                          checked={!useCurrentDate}
                          onChange={() => setUseCurrentDate(false)}
                          className="mr-2 text-blue-600 dark:text-blue-400 focus:ring-blue-500 dark:focus:ring-blue-400"
                        />
                        <span>Specific date</span>
                      </label>
                      
                      {!useCurrentDate && (
                        <input
                          type="date"
                          value={currentDate}
                          onChange={(e) => setCurrentDate(e.target.value)}
                          min={birthDate || undefined}
                          className="w-full px-3 py-2 bg-white dark:bg-gray-700 border border-gray-300 dark:border-gray-600 rounded-md text-gray-900 dark:text-white placeholder-gray-500 dark:placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-blue-500 dark:focus:ring-blue-400 focus:border-blue-500 dark:focus:border-blue-400 transition-colors"
                        />
                      )}
                    </div>
                  </div>

                  {/* Quick Birth Year Buttons */}
                  <div className="border-t border-gray-200 dark:border-gray-600 pt-4">
                    <h3 className="text-sm font-medium text-gray-700 dark:text-gray-300 mb-3">Quick Select Birth Year</h3>
                    <div className="grid grid-cols-4 gap-2">
                      {[1990, 1995, 2000, 2005, 2010, 2015, 2020].map((year) => (
                        <button
                          key={year}
                          onClick={() => setBirthDate(`${year}-01-01`)}
                          className="px-3 py-2 text-sm bg-gray-100 dark:bg-gray-700 border border-gray-300 dark:border-gray-600 rounded text-gray-700 dark:text-gray-300 hover:bg-gray-200 dark:hover:bg-gray-600 transition-colors focus:outline-none focus:ring-2 focus:ring-blue-500 dark:focus:ring-blue-400"
                        >
                          {year}
                        </button>
                      ))}
                    </div>
                  </div>
                </div>
              </div>

              {/* Results Section */}
              <div className="space-y-6">
                {result && (
                  <>
                    {/* Main Age Display */}
                    <div className="bg-white dark:bg-gray-800 rounded-lg shadow-lg dark:shadow-gray-700/50 p-6 border-l-4 border-blue-500 dark:border-blue-400 border border-gray-200 dark:border-gray-700">
                      <h3 className="text-xl font-semibold text-gray-900 dark:text-white mb-4">🎂 Your Age</h3>
                      <div className="text-center mb-4">
                        <div className="text-4xl font-bold text-blue-600 dark:text-blue-400 mb-2">
                          {result.years} years
                        </div>
                        <div className="text-lg text-gray-600 dark:text-gray-300">
                          {result.months} months, {result.days} days
                        </div>
                      </div>
                      
                      <div className="grid grid-cols-2 gap-4 text-sm">
                        <div className="text-center p-3 bg-blue-50 dark:bg-blue-900/50 rounded border border-blue-200 dark:border-blue-700">
                          <div className="font-semibold text-blue-800 dark:text-blue-200">Life Stage</div>
                          <div className="text-blue-600 dark:text-blue-300">{result.lifeStage}</div>
                        </div>
                        <div className="text-center p-3 bg-purple-50 dark:bg-purple-900/50 rounded border border-purple-200 dark:border-purple-700">
                          <div className="font-semibold text-purple-800 dark:text-purple-200">Zodiac Sign</div>
                          <div className="text-purple-600 dark:text-purple-300">{result.zodiacSign}</div>
                        </div>
                      </div>
                    </div>

                    {/* Detailed Breakdown */}
                    <div className="bg-white dark:bg-gray-800 rounded-lg shadow-lg dark:shadow-gray-700/50 p-6 border border-gray-200 dark:border-gray-700">
                      <h3 className="text-xl font-semibold text-gray-900 dark:text-white mb-4">📊 Detailed Breakdown</h3>
                      <div className="space-y-3">
                        <div className="flex justify-between items-center p-3 bg-gray-50 dark:bg-gray-700 rounded border border-gray-200 dark:border-gray-600">
                          <span className="font-medium text-gray-700 dark:text-gray-300">Total Days</span>
                          <span className="text-lg font-bold text-green-600 dark:text-green-400">{formatNumber(result.totalDays)}</span>
                        </div>
                        <div className="flex justify-between items-center p-3 bg-gray-50 dark:bg-gray-700 rounded border border-gray-200 dark:border-gray-600">
                          <span className="font-medium text-gray-700 dark:text-gray-300">Total Hours</span>
                          <span className="text-lg font-bold text-orange-600 dark:text-orange-400">{formatNumber(result.totalHours)}</span>
                        </div>
                        <div className="flex justify-between items-center p-3 bg-gray-50 dark:bg-gray-700 rounded border border-gray-200 dark:border-gray-600">
                          <span className="font-medium text-gray-700 dark:text-gray-300">Total Minutes</span>
                          <span className="text-lg font-bold text-purple-600 dark:text-purple-400">{formatNumber(result.totalMinutes)}</span>
                        </div>
                        <div className="flex justify-between items-center p-3 bg-gray-50 dark:bg-gray-700 rounded border border-gray-200 dark:border-gray-600">
                          <span className="font-medium text-gray-700 dark:text-gray-300">Total Seconds</span>
                          <span className="text-lg font-bold text-red-600 dark:text-red-400">{formatNumber(result.totalSeconds)}</span>
                        </div>
                      </div>
                    </div>

                    {/* Next Birthday */}
                    <div className="bg-yellow-50 dark:bg-yellow-900/20 border border-yellow-200 dark:border-yellow-700 rounded-lg p-6">
                      <h3 className="text-xl font-semibold text-yellow-800 dark:text-yellow-200 mb-4">🎉 Next Birthday</h3>
                      <div className="text-center">
                        <div className="text-2xl font-bold text-yellow-600 dark:text-yellow-400 mb-2">
                          {result.nextBirthday.days} days
                        </div>
                        <div className="text-yellow-700 dark:text-yellow-300">
                          {result.nextBirthday.date}
                        </div>
                        <div className="text-sm text-yellow-600 dark:text-yellow-400 mt-2">
                          You&apos;ll turn {result.years + 1} years old!
                        </div>
                      </div>
                    </div>
                  </>
                )}

                {/* Age Facts */}
                <div className="bg-indigo-50 dark:bg-indigo-900/20 border border-indigo-200 dark:border-indigo-700 rounded-lg p-6">
                  <h3 className="text-xl font-semibold text-indigo-800 dark:text-indigo-200 mb-4">🧠 Did You Know?</h3>
                  <ul className="space-y-2 text-indigo-700 dark:text-indigo-300 text-sm">
                    <li>• Your heart beats approximately 100,000 times per day</li>
                    <li>• You blink about 15-20 times per minute</li>
                    <li>• The average person walks 7,500 steps per day</li>
                    <li>• You breathe about 20,000 times per day</li>
                    <li>• Your brain uses 20% of your body&apos;s energy</li>
                  </ul>
                </div>
              </div>
            </div>

            {/* Additional Information */}
            <div className="mt-12 bg-white dark:bg-gray-800 rounded-lg p-8 border border-gray-200 dark:border-gray-700 shadow-lg dark:shadow-gray-700/50">
              <h2 className="text-2xl font-bold text-gray-900 dark:text-white mb-6 text-center">About Age Calculation</h2>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                  <h3 className="font-semibold text-gray-800 dark:text-gray-200 mb-2">How Age is Calculated</h3>
                  <p className="text-gray-600 dark:text-gray-400 text-sm">
                    Age is calculated by finding the difference between your birth date and the current date (or specified date). 
                    The calculation accounts for leap years and varying month lengths to provide accurate results.
                  </p>
                </div>
                <div>
                  <h3 className="font-semibold text-gray-800 dark:text-gray-200 mb-2">Zodiac Signs</h3>
                  <p className="text-gray-600 dark:text-gray-400 text-sm">
                    Your zodiac sign is determined by the position of the sun at the time of your birth. 
                    There are 12 zodiac signs, each spanning approximately one month of the year.
                  </p>
                </div>
                <div>
                  <h3 className="font-semibold text-gray-800 dark:text-gray-200 mb-2">Life Stages</h3>
                  <p className="text-gray-600 dark:text-gray-400 text-sm">
                    Life stages are general categories based on typical human development patterns. 
                    These stages help understand different phases of physical, cognitive, and social development.
                  </p>
                </div>
                <div>
                  <h3 className="font-semibold text-gray-800 dark:text-gray-200 mb-2">Time Calculations</h3>
                  <p className="text-gray-600 dark:text-gray-400 text-sm">
                    Total time calculations show your age in different units - from days to seconds. 
                    These numbers help put your life span into perspective across different time scales.
                  </p>
                </div>
              </div>
            </div>
          </div>
        </div>
        <Footer />
      </div>
    </>
  );
}