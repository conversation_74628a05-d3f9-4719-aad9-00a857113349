'use client';

import { useState, useEffect, useCallback, Suspense } from 'react';
import { useRouter, useSearchParams } from 'next/navigation';
import Footer from "@/components/Footer";
import CalculatorHeader from '@/components/CalculatorHeader';
import { containerStyles, textStyles, inputStyles, buttonStyles } from '@/components/ui/styles';
import { getVersionInfo } from '../../../lib/version';

// 个人所得税税率表
const TAX_BRACKETS = [
    { min: 0, max: 36000, rate: 0.03, deduction: 0 },
    { min: 36000, max: 144000, rate: 0.10, deduction: 2520 },
    { min: 144000, max: 300000, rate: 0.20, deduction: 16920 },
    { min: 300000, max: 420000, rate: 0.25, deduction: 31920 },
    { min: 420000, max: 660000, rate: 0.30, deduction: 52920 },
    { min: 660000, max: 960000, rate: 0.35, deduction: 85920 },
    { min: 960000, max: Infinity, rate: 0.45, deduction: 181920 },
];

interface SocialInsuranceItem {
    name: string;
    base: string;
    personalRate: string;
    color: string;
}

interface SpecialDeduction {
    name: string;
    amount: string;
}

interface SavedRecord {
    id: string;
    timestamp: number;
    grossSalary: string;
    socialInsurance: SocialInsuranceItem[];
    otherDeductions: string;
    includeInTax: boolean;
    specialDeductions: SpecialDeduction[];
    otherIncome: string;
    otherIncomeInTax: boolean;
    result: CalculationResult;
    name?: string;
}

interface CalculationResult {
    grossSalary: number;
    socialInsuranceTotal: number;
    specialDeductionsTotal: number;
    taxableIncome: number;
    incomeTax: number;
    otherDeductions: number;
    otherIncome: number;
    netSalary: number;
    socialInsuranceDetails: Array<{
        name: string;
        base: number;
        rate: number;
        amount: number;
    }>;
}

function ChinaSalaryCalculatorContent() {
    const router = useRouter();
    const searchParams = useSearchParams();

    // 基本工资
    const [grossSalary, setGrossSalary] = useState<string>('5000');

    // 五险一金
    const [socialInsurance, setSocialInsurance] = useState<SocialInsuranceItem[]>([
        { name: '养老保险', base: '5000', personalRate: '8', color: 'bg-blue-500' },
        { name: '医疗保险（生育险）', base: '5000', personalRate: '2', color: 'bg-green-500' },
        { name: '工伤保险', base: '5000', personalRate: '0', color: 'bg-yellow-500' },
        { name: '失业保险', base: '5000', personalRate: '0.2', color: 'bg-red-500' },
        { name: '住房公积金', base: '5000', personalRate: '12', color: 'bg-purple-500' },
    ]);

    // 其他扣除
    const [otherDeductions, setOtherDeductions] = useState<string>('0');
    const [includeInTax, setIncludeInTax] = useState<boolean>(false);

    // 专项扣除
    const [specialDeductions, setSpecialDeductions] = useState<SpecialDeduction[]>([
        { name: '子女教育扣除', amount: '0' },
        { name: '继续教育扣除', amount: '0' },
        { name: '赡养老人扣除', amount: '0' },
        { name: '住房贷款利息扣除', amount: '0' },
        { name: '住房租金扣除', amount: '0' },
        { name: '3岁以下婴幼儿照护', amount: '0' },
        { name: '个人养老金', amount: '0' },
    ]);

    // 其他收入
    const [otherIncome, setOtherIncome] = useState<string>('0');
    const [otherIncomeInTax, setOtherIncomeInTax] = useState<boolean>(false);

    // 暂存记录
    const [savedRecords, setSavedRecords] = useState<SavedRecord[]>([]);
    const [showDetailModal, setShowDetailModal] = useState<boolean>(false);
    const [selectedRecord, setSelectedRecord] = useState<SavedRecord | null>(null);

    // 计算结果
    const [result, setResult] = useState<CalculationResult>({
        grossSalary: 5000,
        socialInsuranceTotal: 0,
        specialDeductionsTotal: 0,
        taxableIncome: 0,
        incomeTax: 0,
        otherDeductions: 0,
        otherIncome: 0,
        netSalary: 0,
        socialInsuranceDetails: [],
    });

    // URL参数管理
    const updateURL = useCallback(() => {
        const params = new URLSearchParams();

        // 基本工资
        if (grossSalary !== '5000') params.set('grossSalary', grossSalary);

        // 五险一金
        socialInsurance.forEach((item, index) => {
            if (item.base !== '5000') params.set(`si${index}_base`, item.base);
            const defaultRates = ['8', '2', '0', '0.2', '12'];
            if (item.personalRate !== defaultRates[index]) params.set(`si${index}_rate`, item.personalRate);
        });

        // 其他扣除
        if (otherDeductions !== '0') params.set('otherDeductions', otherDeductions);
        if (includeInTax) params.set('includeInTax', 'true');

        // 专项扣除
        specialDeductions.forEach((item, index) => {
            if (item.amount !== '0') params.set(`sd${index}`, item.amount);
        });

        // 其他收入
        if (otherIncome !== '0') params.set('otherIncome', otherIncome);
        if (otherIncomeInTax) params.set('otherIncomeInTax', 'true');

        router.push(`?${params.toString()}`, { scroll: false });
    }, [grossSalary, socialInsurance, otherDeductions, includeInTax, specialDeductions, otherIncome, otherIncomeInTax, router]);

    // 从URL参数初始化数据
    useEffect(() => {
        // 只在首次加载时从URL初始化数据
        if (searchParams.toString() === '') return;

        const urlGrossSalary = searchParams.get('grossSalary');
        if (urlGrossSalary) setGrossSalary(urlGrossSalary);

        // 初始化五险一金
        const updatedSocialInsurance = socialInsurance.map((item, index) => {
            const base = searchParams.get(`si${index}_base`) || item.base;
            const rate = searchParams.get(`si${index}_rate`) || item.personalRate;
            return { ...item, base, personalRate: rate };
        });
        setSocialInsurance(updatedSocialInsurance);

        // 初始化其他扣除
        const urlOtherDeductions = searchParams.get('otherDeductions');
        if (urlOtherDeductions) setOtherDeductions(urlOtherDeductions);

        const urlIncludeInTax = searchParams.get('includeInTax');
        if (urlIncludeInTax) setIncludeInTax(urlIncludeInTax === 'true');

        // 初始化专项扣除
        const updatedSpecialDeductions = specialDeductions.map((item, index) => {
            const amount = searchParams.get(`sd${index}`) || item.amount;
            return { ...item, amount };
        });
        setSpecialDeductions(updatedSpecialDeductions);

        // 初始化其他收入
        const urlOtherIncome = searchParams.get('otherIncome');
        if (urlOtherIncome) setOtherIncome(urlOtherIncome);

        const urlOtherIncomeInTax = searchParams.get('otherIncomeInTax');
        if (urlOtherIncomeInTax) setOtherIncomeInTax(urlOtherIncomeInTax === 'true');
    }, [searchParams]);

    // 加载暂存记录
    useEffect(() => {
        const savedData = localStorage.getItem('china-salary-calculator-saved-records');
        if (savedData) {
            try {
                const records = JSON.parse(savedData);
                setSavedRecords(records);
            } catch (error) {
                console.error('Failed to load saved records:', error);
            }
        }
    }, []);

    // 保存暂存记录到localStorage
    const saveRecordsToStorage = (records: SavedRecord[]) => {
        localStorage.setItem('china-salary-calculator-saved-records', JSON.stringify(records));
    };

    // 保存当前计算结果到暂存
    const saveCurrentRecord = () => {
        const newRecord: SavedRecord = {
            id: Date.now().toString(),
            timestamp: Date.now(),
            grossSalary,
            socialInsurance: [...socialInsurance],
            otherDeductions,
            includeInTax,
            specialDeductions: [...specialDeductions],
            otherIncome,
            otherIncomeInTax,
            result: { ...result },
            name: `计算记录-${new Date().toLocaleDateString()}`
        };

        const updatedRecords = [newRecord, ...savedRecords];
        setSavedRecords(updatedRecords);
        saveRecordsToStorage(updatedRecords);
        alert('计算结果已保存到暂存！');
    };

    // 删除暂存记录
    const deleteRecord = (recordId: string) => {
        const updatedRecords = savedRecords.filter(record => record.id !== recordId);
        setSavedRecords(updatedRecords);
        saveRecordsToStorage(updatedRecords);
    };

    // 导出暂存记录
    const exportRecords = () => {
        // 获取当前版本信息
        const versionInfo = getVersionInfo();

        const exportData = {
            meta: {
                source: 'Calc9',
                version: versionInfo.version,
                exportTime: new Date().toISOString(),
                calculatorName: '中国工资计算器（五险一金缴存计算器）',
                calculatorUrl: 'https://calc9.com/china-salary-calculator',
                description: '中国工资计算器暂存记录数据导出',
                dataFormat: 'calc9-china-salary-calculator-records-v1'
            },
            records: savedRecords
        };

        const dataStr = JSON.stringify(exportData, null, 2);
        const dataBlob = new Blob([dataStr], { type: 'application/json' });
        const url = URL.createObjectURL(dataBlob);

        const timestamp = new Date().toISOString().replace(/[:.]/g, '-').slice(0, 19);
        const filename = `calc9-中国工资计算器-暂存记录-${timestamp}.json`;

        const link = document.createElement('a');
        link.href = url;
        link.download = filename;
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
        URL.revokeObjectURL(url);

        alert(`已导出 ${savedRecords.length} 条记录到文件: ${filename}`);
    };

    // 检查记录是否重复
    const isDuplicateRecord = (newRecord: SavedRecord, existingRecords: SavedRecord[]): boolean => {
        return existingRecords.some(existing => {
            // 比较核心数据字段来判断是否重复
            return (
                existing.grossSalary === newRecord.grossSalary &&
                existing.otherDeductions === newRecord.otherDeductions &&
                existing.includeInTax === newRecord.includeInTax &&
                existing.otherIncome === newRecord.otherIncome &&
                existing.otherIncomeInTax === newRecord.otherIncomeInTax &&
                JSON.stringify(existing.socialInsurance) === JSON.stringify(newRecord.socialInsurance) &&
                JSON.stringify(existing.specialDeductions) === JSON.stringify(newRecord.specialDeductions)
            );
        });
    };

    // 导入暂存记录
    const importRecords = (event: React.ChangeEvent<HTMLInputElement>) => {
        const file = event.target.files?.[0];
        if (!file) return;

        const reader = new FileReader();
        reader.onload = (e) => {
            try {
                const content = e.target?.result as string;
                const importData = JSON.parse(content);

                // 验证数据格式
                if (!importData.records || !Array.isArray(importData.records)) {
                    throw new Error('数据格式不正确：缺少 records 数组');
                }

                // 处理导入的记录，确保兼容性
                const importTime = Date.now();
                const processedRecords: SavedRecord[] = importData.records.map((record: Partial<SavedRecord>, index: number) => {
                    try {
                        // 确保必要字段存在
                        const processedRecord: SavedRecord = {
                            id: `imported-${importTime}-${index}`,
                            timestamp: importTime + index, // 为每个记录分配略有不同的时间戳以避免重复
                            grossSalary: record.grossSalary || '0',
                            socialInsurance: record.socialInsurance || [
                                { name: '养老保险', base: '0', personalRate: '8', color: 'bg-blue-500' },
                                { name: '医疗保险（生育险）', base: '0', personalRate: '2', color: 'bg-green-500' },
                                { name: '工伤保险', base: '0', personalRate: '0', color: 'bg-yellow-500' },
                                { name: '失业保险', base: '0', personalRate: '0.2', color: 'bg-red-500' },
                                { name: '住房公积金', base: '0', personalRate: '12', color: 'bg-purple-500' },
                            ],
                            otherDeductions: record.otherDeductions || '0',
                            includeInTax: record.includeInTax || false,
                            specialDeductions: record.specialDeductions || [
                                { name: '子女教育扣除', amount: '0' },
                                { name: '继续教育扣除', amount: '0' },
                                { name: '赡养老人扣除', amount: '0' },
                                { name: '住房贷款利息扣除', amount: '0' },
                                { name: '住房租金扣除', amount: '0' },
                                { name: '3岁以下婴幼儿照护', amount: '0' },
                                { name: '个人养老金', amount: '0' },
                            ],
                            otherIncome: record.otherIncome || '0',
                            otherIncomeInTax: record.otherIncomeInTax || false,
                            result: record.result || {
                                grossSalary: 0,
                                socialInsuranceTotal: 0,
                                specialDeductionsTotal: 0,
                                taxableIncome: 0,
                                incomeTax: 0,
                                otherDeductions: 0,
                                otherIncome: 0,
                                netSalary: 0,
                                socialInsuranceDetails: []
                            },
                            name: record.name || `导入记录-${new Date().toLocaleDateString()}`
                        };
                        return processedRecord;
                    } catch (error) {
                        console.warn(`处理第 ${index + 1} 条记录时出错，跳过该记录:`, error);
                        return null;
                    }
                }).filter((record: SavedRecord | null): record is SavedRecord => record !== null);

                if (processedRecords.length === 0) {
                    alert('没有找到有效的记录数据');
                    return;
                }

                // 过滤掉重复记录
                const nonDuplicateRecords = processedRecords.filter(record => !isDuplicateRecord(record, savedRecords));
                const duplicateCount = processedRecords.length - nonDuplicateRecords.length;

                if (nonDuplicateRecords.length === 0) {
                    alert('所有记录都是重复的，未导入任何新记录');
                    return;
                }

                // 追加到现有记录
                const updatedRecords = [...nonDuplicateRecords, ...savedRecords];
                setSavedRecords(updatedRecords);
                saveRecordsToStorage(updatedRecords);

                const message = duplicateCount > 0
                    ? `成功导入 ${nonDuplicateRecords.length} 条记录，跳过 ${duplicateCount} 条重复记录`
                    : `成功导入 ${nonDuplicateRecords.length} 条记录`;
                alert(message);

            } catch (error) {
                console.error('导入失败:', error);
                alert('导入失败：文件格式不正确或数据损坏');
            }
        };

        reader.readAsText(file);
        // 重置文件输入
        event.target.value = '';
    };

    // 清空所有暂存记录
    const clearAllRecords = () => {
        if (savedRecords.length === 0) {
            alert('没有暂存记录需要清空');
            return;
        }

        const confirmClear = confirm(`确定要清空所有 ${savedRecords.length} 条暂存记录吗？此操作不可恢复。`);
        if (confirmClear) {
            setSavedRecords([]);
            localStorage.removeItem('china-salary-calculator-saved-records');
            alert('所有暂存记录已清空');
        }
    };

    // 填充暂存记录到当前计算器
    const loadRecord = (record: SavedRecord) => {
        setGrossSalary(record.grossSalary);
        setSocialInsurance([...record.socialInsurance]);
        setOtherDeductions(record.otherDeductions);
        setIncludeInTax(record.includeInTax);
        setSpecialDeductions([...record.specialDeductions]);
        setOtherIncome(record.otherIncome);
        setOtherIncomeInTax(record.otherIncomeInTax);
        // 滚动到页面顶部
        window.scrollTo({ top: 0, behavior: 'smooth' });
    };

    // 显示详情
    const showRecordDetail = (record: SavedRecord) => {
        setSelectedRecord(record);
        setShowDetailModal(true);
    };

    // 关闭详情模态框
    const closeDetailModal = () => {
        setShowDetailModal(false);
        setSelectedRecord(null);
    };

    // 键盘事件处理
    useEffect(() => {
        const handleKeyDown = (event: KeyboardEvent) => {
            if (event.key === 'Escape' && showDetailModal) {
                closeDetailModal();
            }
        };

        if (showDetailModal) {
            document.addEventListener('keydown', handleKeyDown);
            // 防止背景滚动
            document.body.style.overflow = 'hidden';
        }

        return () => {
            document.removeEventListener('keydown', handleKeyDown);
            document.body.style.overflow = 'unset';
        };
    }, [showDetailModal]);

    // 分享功能
    const shareCalculation = useCallback(async () => {
        const currentUrl = window.location.href;
        try {
            await navigator.clipboard.writeText(currentUrl);
            alert('链接已复制到剪贴板！');
        } catch (err) {
            console.error('复制失败:', err);
            // 降级处理
            const textArea = document.createElement('textarea');
            textArea.value = currentUrl;
            document.body.appendChild(textArea);
            textArea.select();
            document.execCommand('copy');
            document.body.removeChild(textArea);
            alert('链接已复制到剪贴板！');
        }
    }, []);

    // 计算个人所得税
    const calculateIncomeTax = useCallback((annualTaxableIncome: number): number => {
        if (annualTaxableIncome <= 0) return 0;

        for (const bracket of TAX_BRACKETS) {
            if (annualTaxableIncome > bracket.min && annualTaxableIncome <= bracket.max) {
                return (annualTaxableIncome * bracket.rate - bracket.deduction) / 12;
            }
        }
        return 0;
    }, []);

    // 计算工资详情
    const calculateSalary = useCallback(() => {
        const gross = parseFloat(grossSalary) || 0;
        const otherDeduct = parseFloat(otherDeductions) || 0;
        const otherIncomeAmount = parseFloat(otherIncome) || 0;

        // 计算五险一金
        const socialInsuranceDetails = socialInsurance.map(item => {
            const base = parseFloat(item.base) || 0;
            const rate = parseFloat(item.personalRate) || 0;
            const amount = base * (rate / 100);
            return {
                name: item.name,
                base,
                rate,
                amount,
            };
        });

        const socialInsuranceTotal = socialInsuranceDetails.reduce((sum, item) => sum + item.amount, 0);

        // 计算专项扣除总额
        const specialDeductionsTotal = specialDeductions.reduce((sum, item) => {
            return sum + (parseFloat(item.amount) || 0);
        }, 0);

        // 计算应纳税所得额（包含其他收入）
        const totalIncome = gross + (otherIncomeInTax ? otherIncomeAmount : 0);
        const monthlyTaxableIncome = totalIncome - socialInsuranceTotal - specialDeductionsTotal - 5000;
        const annualTaxableIncome = monthlyTaxableIncome * 12;

        // 计算个人所得税
        const incomeTax = calculateIncomeTax(annualTaxableIncome);

        // 计算到手工资（包含其他收入）
        const netSalary = gross + otherIncomeAmount - socialInsuranceTotal - incomeTax - (includeInTax ? 0 : otherDeduct);

        setResult({
            grossSalary: gross,
            socialInsuranceTotal,
            specialDeductionsTotal,
            taxableIncome: monthlyTaxableIncome,
            incomeTax,
            otherDeductions: otherDeduct,
            otherIncome: otherIncomeAmount,
            netSalary,
            socialInsuranceDetails,
        });
    }, [grossSalary, socialInsurance, specialDeductions, otherDeductions, includeInTax, otherIncome, otherIncomeInTax, calculateIncomeTax]);

    useEffect(() => {
        calculateSalary();
    }, [calculateSalary]);

    // 更新URL当数据变化时
    useEffect(() => {
        updateURL();
    }, [updateURL]);

    // 更新基本工资
    const updateGrossSalary = (value: string) => {
        setGrossSalary(value);
    };

    // 更新五险一金
    const updateSocialInsurance = (index: number, field: keyof SocialInsuranceItem, value: string) => {
        const updated = [...socialInsurance];
        updated[index] = { ...updated[index], [field]: value };
        setSocialInsurance(updated);
    };

    // 更新其他扣除
    const updateOtherDeductions = (value: string) => {
        setOtherDeductions(value);
    };

    // 更新是否计入个税
    const updateIncludeInTax = (value: boolean) => {
        setIncludeInTax(value);
    };

    // 更新专项扣除
    const updateSpecialDeduction = (index: number, amount: string) => {
        const updated = [...specialDeductions];
        updated[index] = { ...updated[index], amount };
        setSpecialDeductions(updated);
    };

    // 更新其他收入
    const updateOtherIncome = (value: string) => {
        setOtherIncome(value);
    };

    // 更新其他收入是否计入个税
    const updateOtherIncomeInTax = (value: boolean) => {
        setOtherIncomeInTax(value);
    };

    // 格式化货币
    const formatCurrency = (amount: number): string => {
        return new Intl.NumberFormat('zh-CN', {
            style: 'currency',
            currency: 'CNY',
            minimumFractionDigits: 2,
            maximumFractionDigits: 2,
        }).format(amount);
    };

    return (
        <>
            <div className={containerStyles.page}>
                <div className="container mx-auto px-4 py-8">
                    <div className="max-w-6xl mx-auto">
                        <CalculatorHeader
                            title="China Salary Calculator"
                            description="中国工资计算器 - 五险一金缴存计算器。计算扣除五险一金和个人所得税后的到手工资。"
                            category="Financial"
                            className="text-center mb-12"
                        />

                        <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
                            {/* 输入区域 */}
                            <div className="space-y-6">
                                {/* 基本工资 */}
                                <div className={containerStyles.form}>
                                    <h3 className={`${textStyles.h3} mb-4`}>基本工资</h3>
                                    <div>
                                        <label className={textStyles.label}>税前基本工资 (必填)</label>
                                        <input
                                            type="number"
                                            className={inputStyles.base}
                                            value={grossSalary}
                                            onChange={(e) => updateGrossSalary(e.target.value)}
                                            placeholder="请输入税前基本工资"
                                        />
                                    </div>
                                </div>

                                {/* 五险一金 */}
                                <div className={containerStyles.form}>
                                    <h3 className={`${textStyles.h3} mb-4`}>五险一金</h3>
                                    <div className="space-y-4">
                                        {socialInsurance.map((item, index) => (
                                            <div key={index} className={`p-4 rounded-lg border-l-4 ${item.color.replace('bg-', 'border-')} bg-gray-50 dark:bg-gray-800/50`}>
                                                <h4 className={`${textStyles.h4} mb-3`}>{item.name}</h4>
                                                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                                                    <div>
                                                        <label className={textStyles.label}>缴费基数 (元)</label>
                                                        <input
                                                            type="number"
                                                            className={inputStyles.base}
                                                            value={item.base}
                                                            onChange={(e) => updateSocialInsurance(index, 'base', e.target.value)}
                                                            placeholder="缴费基数"
                                                        />
                                                    </div>
                                                    <div>
                                                        <label className={textStyles.label}>个人缴纳比例 (%)</label>
                                                        <input
                                                            type="number"
                                                            step="0.1"
                                                            className={inputStyles.base}
                                                            value={item.personalRate}
                                                            onChange={(e) => updateSocialInsurance(index, 'personalRate', e.target.value)}
                                                            placeholder="个人缴纳比例"
                                                        />
                                                    </div>
                                                </div>
                                            </div>
                                        ))}
                                    </div>
                                </div>

                                {/* 其他扣除 */}
                                <div className={containerStyles.form}>
                                    <h3 className={`${textStyles.h3} mb-4`}>其他扣除</h3>
                                    <div className="space-y-4">
                                        <div>
                                            <label className={textStyles.label}>其他扣除金额 (元)</label>
                                            <input
                                                type="number"
                                                className={inputStyles.base}
                                                value={otherDeductions}
                                                onChange={(e) => updateOtherDeductions(e.target.value)}
                                                placeholder="其他扣除金额"
                                            />
                                        </div>
                                        <div className="flex items-center space-x-2">
                                            <input
                                                type="checkbox"
                                                id="includeInTax"
                                                checked={includeInTax}
                                                onChange={(e) => updateIncludeInTax(e.target.checked)}
                                                className="w-4 h-4 text-blue-600 bg-gray-100 border-gray-300 rounded focus:ring-blue-500"
                                            />
                                            <label htmlFor="includeInTax" className={textStyles.label}>
                                                计入个税计算 (默认不计入)
                                            </label>
                                        </div>
                                    </div>
                                </div>

                                {/* 专项扣除 */}
                                <div className={containerStyles.form}>
                                    <h3 className={`${textStyles.h3} mb-4`}>专项扣除</h3>
                                    <div className="space-y-4">
                                        {specialDeductions.map((item, index) => (
                                            <div key={index}>
                                                <label className={textStyles.label}>{item.name} (元/月)</label>
                                                <input
                                                    type="number"
                                                    className={inputStyles.base}
                                                    value={item.amount}
                                                    onChange={(e) => updateSpecialDeduction(index, e.target.value)}
                                                    placeholder="每月扣除金额"
                                                />
                                            </div>
                                        ))}
                                    </div>
                                </div>

                                {/* 其他收入 */}
                                <div className={containerStyles.form}>
                                    <h3 className={`${textStyles.h3} mb-4`}>其他收入</h3>
                                    <div className="space-y-4">
                                        <div>
                                            <label className={textStyles.label}>其他收入金额 (元)</label>
                                            <input
                                                type="number"
                                                className={inputStyles.base}
                                                value={otherIncome}
                                                onChange={(e) => updateOtherIncome(e.target.value)}
                                                placeholder="基本工资之外的其他收入"
                                            />
                                        </div>
                                        <div className="flex items-center space-x-2">
                                            <input
                                                type="checkbox"
                                                id="otherIncomeInTax"
                                                checked={otherIncomeInTax}
                                                onChange={(e) => updateOtherIncomeInTax(e.target.checked)}
                                                className="w-4 h-4 text-blue-600 bg-gray-100 border-gray-300 rounded focus:ring-blue-500"
                                            />
                                            <label htmlFor="otherIncomeInTax" className={textStyles.label}>
                                                计入个税计算 (默认不计入)
                                            </label>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            {/* 计算结果 */}
                            <div className="space-y-6">
                                {/* 工资计算结果 */}
                                <div className={containerStyles.card}>
                                    <div className="p-6">
                                        <h3 className={`${textStyles.h3} mb-6`}>工资计算结果</h3>

                                        {/* 基本信息 */}
                                        <div className="space-y-4 mb-6">
                                            <div className="flex justify-between items-center py-2 border-b dark:border-gray-700">
                                                <span className={textStyles.label}>税前基本工资</span>
                                                <span className={`${textStyles.h4} text-green-600`}>{formatCurrency(result.grossSalary)}</span>
                                            </div>
                                            {result.otherIncome > 0 && (
                                                <div className="flex justify-between items-center py-2 border-b dark:border-gray-700">
                                                    <span className={textStyles.label}>其他收入</span>
                                                    <span className={`${textStyles.h4} text-green-600`}>+{formatCurrency(result.otherIncome)}</span>
                                                </div>
                                            )}
                                            <div className="flex justify-between items-center py-2 border-b dark:border-gray-700">
                                                <span className={textStyles.label}>五险一金个人缴纳</span>
                                                <span className={`${textStyles.h4} text-red-600`}>-{formatCurrency(result.socialInsuranceTotal)}</span>
                                            </div>
                                            <div className="flex justify-between items-center py-2 border-b dark:border-gray-700">
                                                <span className={textStyles.label}>个人所得税</span>
                                                <span className={`${textStyles.h4} text-red-600`}>-{formatCurrency(result.incomeTax)}</span>
                                            </div>
                                            {result.otherDeductions > 0 && (
                                                <div className="flex justify-between items-center py-2 border-b dark:border-gray-700">
                                                    <span className={textStyles.label}>其他扣除</span>
                                                    <span className={`${textStyles.h4} text-red-600`}>-{formatCurrency(result.otherDeductions)}</span>
                                                </div>
                                            )}
                                            <div className="flex justify-between items-center py-3 border-t-2 border-blue-500 bg-blue-50 dark:bg-blue-900/20 rounded-lg px-4">
                                                <span className={`${textStyles.h3} font-bold`}>到手工资</span>
                                                <span className={`${textStyles.h2} font-bold text-blue-600`}>{formatCurrency(result.netSalary)}</span>
                                            </div>
                                        </div>

                                        {/* 五险一金详情 */}
                                        <div className="space-y-3">
                                            <h4 className={`${textStyles.h4} mb-4`}>五险一金详情</h4>
                                            {result.socialInsuranceDetails.map((item, index) => (
                                                <div key={index} className="bg-gray-50 dark:bg-gray-800/50 rounded-lg p-3">
                                                    <div className="flex justify-between items-center mb-2">
                                                        <span className={textStyles.label}>{item.name}</span>
                                                        <span className={`${textStyles.body} font-semibold`}>{formatCurrency(item.amount)}</span>
                                                    </div>
                                                    <div className="text-xs text-gray-500 dark:text-gray-400">
                                                        基数: {formatCurrency(item.base)} × {item.rate}%
                                                    </div>
                                                </div>
                                            ))}
                                            <div className="bg-blue-50 dark:bg-blue-900/20 rounded-lg p-3 border-l-4 border-blue-500">
                                                <div className="flex justify-between items-center">
                                                    <span className={`${textStyles.label} font-bold`}>五险一金总扣除金额</span>
                                                    <span className={`${textStyles.h4} font-bold text-blue-600`}>{formatCurrency(result.socialInsuranceTotal)}</span>
                                                </div>
                                            </div>
                                        </div>

                                        {/* 个税计算详情 */}
                                        <div className="mt-6 space-y-3">
                                            <h4 className={`${textStyles.h4} mb-4`}>个人所得税计算详情</h4>
                                            <div className="bg-gray-50 dark:bg-gray-800/50 rounded-lg p-4 space-y-2">
                                                <div className="flex justify-between">
                                                    <span className={textStyles.bodySmall}>应纳税所得额(月)</span>
                                                    <span className={textStyles.bodySmall}>{formatCurrency(result.taxableIncome)}</span>
                                                </div>
                                                <div className="flex justify-between">
                                                    <span className={textStyles.bodySmall}>应纳税所得额(年)</span>
                                                    <span className={textStyles.bodySmall}>{formatCurrency(result.taxableIncome * 12)}</span>
                                                </div>
                                                <div className="flex justify-between">
                                                    <span className={textStyles.bodySmall}>专项扣除总额(月)</span>
                                                    <span className={textStyles.bodySmall}>{formatCurrency(result.specialDeductionsTotal)}</span>
                                                </div>
                                                <div className="text-xs text-gray-500 dark:text-gray-400 mt-2">
                                                    计算公式: 应纳税所得额 = (税前工资 + 其他收入) - 五险一金 - 专项扣除 - 5000元(起征点)
                                                </div>
                                            </div>
                                            <div className="bg-red-50 dark:bg-red-900/20 rounded-lg p-3 border-l-4 border-red-500">
                                                <div className="flex justify-between items-center">
                                                    <span className={`${textStyles.label} font-bold`}>本月个人所得税扣除金额</span>
                                                    <span className={`${textStyles.h4} font-bold text-red-600`}>{formatCurrency(result.incomeTax)}</span>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                {/* 税率表 */}
                                <div className={containerStyles.card}>
                                    <div className="p-6">
                                        <h4 className={`${textStyles.h4} mb-4`}>个人所得税税率表（年）</h4>
                                        <div className="overflow-x-auto">
                                            <table className="w-full text-sm">
                                                <thead>
                                                    <tr className="border-b dark:border-gray-700">
                                                        <th className="text-left py-2 px-3">应纳税所得额</th>
                                                        <th className="text-left py-2 px-3">税率</th>
                                                        <th className="text-left py-2 px-3">速算扣除数</th>
                                                    </tr>
                                                </thead>
                                                <tbody>
                                                    {TAX_BRACKETS.map((bracket, index) => (
                                                        <tr key={index} className="border-b dark:border-gray-700">
                                                            <td className="py-2 px-3">
                                                                {bracket.min === 0 ? '不超过' : '超过'}
                                                                {bracket.min > 0 && formatCurrency(bracket.min)}
                                                                {bracket.max !== Infinity && (
                                                                    <>至{formatCurrency(bracket.max)}的部分</>
                                                                )}
                                                                {bracket.max === Infinity && '的部分'}
                                                            </td>
                                                            <td className="py-2 px-3">{(bracket.rate * 100).toFixed(0)}%</td>
                                                            <td className="py-2 px-3">{formatCurrency(bracket.deduction)}</td>
                                                        </tr>
                                                    ))}
                                                </tbody>
                                            </table>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                {/* 分享和暂存按钮 */}
                <div className="max-w-6xl mx-auto px-4 py-6">
                    <div className="text-center space-y-4">
                        <div className="flex justify-center space-x-4">
                            <button
                                onClick={shareCalculation}
                                className={`${buttonStyles.primary} inline-flex items-center space-x-2`}
                            >
                                <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8.684 13.342C8.886 12.938 9 12.482 9 12c0-.482-.114-.938-.316-1.342m0 2.684a3 3 0 110-2.684m0 2.684l6.632 3.316m-6.632-6l6.632-3.316m0 0a3 3 0 105.367-2.684 3 3 0 00-5.367 2.684zm0 9.316a3 3 0 105.367 2.684 3 3 0 00-5.367-2.684z" />
                                </svg>
                                <span>分享计算结果</span>
                            </button>
                            <button
                                onClick={saveCurrentRecord}
                                className={`${buttonStyles.primary} bg-gradient-to-r from-green-600 to-green-700 hover:from-green-700 hover:to-green-800 inline-flex items-center space-x-2`}
                            >
                                <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 7H5a2 2 0 00-2 2v9a2 2 0 002 2h14a2 2 0 002-2V9a2 2 0 00-2-2h-3m-1 4l-3-3m0 0l-3 3m3-3v12" />
                                </svg>
                                <span>暂存计算结果</span>
                            </button>
                        </div>
                        <p className={`${textStyles.bodySmall}`}>
                            分享链接包含所有计算参数 | 暂存记录保存在浏览器中，方便对比不同策略
                        </p>
                    </div>
                </div>

                {/* 暂存记录表格 */}
                {savedRecords.length > 0 && (
                    <div className="max-w-6xl mx-auto px-4 py-6">
                        <div className={containerStyles.card}>
                            <div className="p-6">
                                <h3 className={`${textStyles.h3} mb-6`}>暂存记录 ({savedRecords.length}条)</h3>

                                {/* 记录操作按钮 */}
                                <div className="flex flex-wrap gap-3 mb-6">
                                    <button
                                        onClick={exportRecords}
                                        className="px-4 py-2 bg-green-500 text-white rounded hover:bg-green-600 transition-colors flex items-center gap-2"
                                    >
                                        <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 10v6m0 0l-3-3m3 3l3-3M3 17V7a2 2 0 012-2h6l2 2h6a2 2 0 012 2v10a2 2 0 01-2 2H5a2 2 0 01-2-2z" />
                                        </svg>
                                        导出记录
                                    </button>

                                    <input
                                        type="file"
                                        accept=".json"
                                        onChange={importRecords}
                                        className="hidden"
                                        id="importFileInput"
                                    />
                                    <button
                                        onClick={() => document.getElementById('importFileInput')?.click()}
                                        className="px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600 transition-colors flex items-center gap-2"
                                    >
                                        <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M9 19l3 3m0 0l3-3m-3 3V10" />
                                        </svg>
                                        导入记录
                                    </button>

                                    <button
                                        onClick={clearAllRecords}
                                        className="px-4 py-2 bg-red-500 text-white rounded hover:bg-red-600 transition-colors flex items-center gap-2"
                                    >
                                        <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
                                        </svg>
                                        清空记录
                                    </button>
                                </div>

                                <div className="overflow-x-auto">
                                    <table className="w-full text-sm">
                                        <thead>
                                            <tr className="border-b dark:border-gray-700">
                                                <th className="text-left py-3 px-2">保存时间</th>
                                                <th className="text-left py-3 px-2">税前基本工资</th>
                                                <th className="text-left py-3 px-2">其他收入</th>
                                                <th className="text-left py-3 px-2">五险一金</th>
                                                <th className="text-left py-3 px-2">个人所得税</th>
                                                <th className="text-left py-3 px-2">到手工资</th>
                                                <th className="text-left py-3 px-2">操作</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            {savedRecords.map((record) => (
                                                <tr key={record.id} className="border-b dark:border-gray-700 hover:bg-gray-50 dark:hover:bg-gray-800/50">
                                                    <td className="py-3 px-2">
                                                        <div className="text-xs text-gray-500">
                                                            {new Date(record.timestamp).toLocaleDateString()}
                                                        </div>
                                                        <div className="text-xs text-gray-400">
                                                            {new Date(record.timestamp).toLocaleTimeString()}
                                                        </div>
                                                    </td>
                                                    <td className="py-3 px-2">
                                                        <div className="font-medium">{formatCurrency(record.result.grossSalary)}</div>
                                                    </td>
                                                    <td className="py-3 px-2">
                                                        <div className="font-medium">
                                                            {record.result.otherIncome > 0 ? formatCurrency(record.result.otherIncome) : '-'}
                                                        </div>
                                                        {record.otherIncomeInTax && record.result.otherIncome > 0 && (
                                                            <div className="text-xs text-blue-600">计入个税</div>
                                                        )}
                                                    </td>
                                                    <td className="py-3 px-2">
                                                        <div className="font-medium text-red-600">-{formatCurrency(record.result.socialInsuranceTotal)}</div>
                                                    </td>
                                                    <td className="py-3 px-2">
                                                        <div className="font-medium text-red-600">-{formatCurrency(record.result.incomeTax)}</div>
                                                    </td>
                                                    <td className="py-3 px-2">
                                                        <div className="font-bold text-green-600">{formatCurrency(record.result.netSalary)}</div>
                                                    </td>
                                                    <td className="py-3 px-2">
                                                        <div className="flex space-x-2">
                                                            <button
                                                                onClick={() => showRecordDetail(record)}
                                                                className="px-3 py-1 bg-purple-500 text-white text-xs rounded hover:bg-purple-600 transition-colors"
                                                            >
                                                                详情
                                                            </button>
                                                            <button
                                                                onClick={() => loadRecord(record)}
                                                                className="px-3 py-1 bg-blue-500 text-white text-xs rounded hover:bg-blue-600 transition-colors"
                                                            >
                                                                填充
                                                            </button>
                                                            <button
                                                                onClick={() => deleteRecord(record.id)}
                                                                className="px-3 py-1 bg-red-500 text-white text-xs rounded hover:bg-red-600 transition-colors"
                                                            >
                                                                删除
                                                            </button>
                                                        </div>
                                                    </td>
                                                </tr>
                                            ))}
                                        </tbody>
                                    </table>
                                </div>
                                <div className="mt-4 text-xs text-gray-500 dark:text-gray-400">
                                    暂存记录保存在浏览器本地存储中，清除浏览器数据会丢失这些记录。<br />
                                    支持导出记录到JSON文件，或导入JSON文件中的记录数据。导入时会自动设置为当前时间，并跳过重复记录。
                                </div>
                            </div>
                        </div>
                    </div>
                )}
            </div>

            {/* 详情模态框 */}
            {showDetailModal && selectedRecord && (
                <div
                    className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4"
                    onClick={closeDetailModal}
                >
                    <div
                        className="bg-white dark:bg-gray-800 rounded-lg shadow-xl max-w-4xl w-full max-h-[90vh] overflow-y-auto"
                        onClick={(e) => e.stopPropagation()}
                    >
                        <div className="p-6 border-b dark:border-gray-700">
                            <div className="flex justify-between items-center">
                                <h2 className={`${textStyles.h2}`}>计算记录详情</h2>
                                <button
                                    onClick={closeDetailModal}
                                    className="text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-200"
                                >
                                    <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                                    </svg>
                                </button>
                            </div>
                            <div className="mt-2 text-sm text-gray-500 dark:text-gray-400">
                                保存时间：{new Date(selectedRecord.timestamp).toLocaleString()}
                            </div>
                        </div>

                        <div className="p-6 space-y-8">
                            {/* 基本信息 */}
                            <div>
                                <h3 className={`${textStyles.h3} mb-4`}>基本信息</h3>
                                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                                    <div className="bg-gray-50 dark:bg-gray-700 rounded-lg p-4">
                                        <div className="text-sm text-gray-500 dark:text-gray-400">税前基本工资</div>
                                        <div className="text-lg font-semibold text-green-600">{formatCurrency(selectedRecord.result.grossSalary)}</div>
                                    </div>
                                    <div className="bg-gray-50 dark:bg-gray-700 rounded-lg p-4">
                                        <div className="text-sm text-gray-500 dark:text-gray-400">其他收入</div>
                                        <div className="text-lg font-semibold text-green-600">
                                            {selectedRecord.result.otherIncome > 0 ? formatCurrency(selectedRecord.result.otherIncome) : '无'}
                                        </div>
                                        {selectedRecord.otherIncomeInTax && selectedRecord.result.otherIncome > 0 && (
                                            <div className="text-xs text-blue-600 mt-1">计入个税</div>
                                        )}
                                    </div>
                                    <div className="bg-gray-50 dark:bg-gray-700 rounded-lg p-4">
                                        <div className="text-sm text-gray-500 dark:text-gray-400">其他扣除</div>
                                        <div className="text-lg font-semibold text-red-600">
                                            {selectedRecord.result.otherDeductions > 0 ? '-' + formatCurrency(selectedRecord.result.otherDeductions) : '无'}
                                        </div>
                                        {selectedRecord.includeInTax && selectedRecord.result.otherDeductions > 0 && (
                                            <div className="text-xs text-blue-600 mt-1">计入个税</div>
                                        )}
                                    </div>
                                    <div className="bg-blue-50 dark:bg-blue-900/20 rounded-lg p-4 border border-blue-200 dark:border-blue-800">
                                        <div className="text-sm text-gray-500 dark:text-gray-400">到手工资</div>
                                        <div className="text-xl font-bold text-blue-600">{formatCurrency(selectedRecord.result.netSalary)}</div>
                                    </div>
                                </div>
                            </div>

                            {/* 五险一金详情 */}
                            <div>
                                <h3 className={`${textStyles.h3} mb-4`}>五险一金详情</h3>
                                <div className="space-y-3">
                                    {selectedRecord.socialInsurance.map((item, index) => (
                                        <div key={index} className="bg-gray-50 dark:bg-gray-700 rounded-lg p-4">
                                            <div className="flex justify-between items-center">
                                                <div>
                                                    <div className="font-medium">{item.name}</div>
                                                    <div className="text-sm text-gray-500 dark:text-gray-400">
                                                        基数: {formatCurrency(parseFloat(item.base))} × {item.personalRate}%
                                                    </div>
                                                </div>
                                                <div className="text-right">
                                                    <div className="font-semibold text-red-600">
                                                        -{formatCurrency(parseFloat(item.base) * (parseFloat(item.personalRate) / 100))}
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    ))}
                                    <div className="bg-blue-50 dark:bg-blue-900/20 rounded-lg p-4 border border-blue-200 dark:border-blue-800">
                                        <div className="flex justify-between items-center">
                                            <div className="font-bold">五险一金总计</div>
                                            <div className="text-lg font-bold text-blue-600">-{formatCurrency(selectedRecord.result.socialInsuranceTotal)}</div>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            {/* 专项扣除详情 */}
                            <div>
                                <h3 className={`${textStyles.h3} mb-4`}>专项扣除详情</h3>
                                <div className="space-y-3">
                                    {selectedRecord.specialDeductions.filter(item => parseFloat(item.amount) > 0).map((item, index) => (
                                        <div key={index} className="bg-gray-50 dark:bg-gray-700 rounded-lg p-4">
                                            <div className="flex justify-between items-center">
                                                <div className="font-medium">{item.name}</div>
                                                <div className="font-semibold text-orange-600">-{formatCurrency(parseFloat(item.amount))}</div>
                                            </div>
                                        </div>
                                    ))}
                                    {selectedRecord.result.specialDeductionsTotal > 0 && (
                                        <div className="bg-orange-50 dark:bg-orange-900/20 rounded-lg p-4 border border-orange-200 dark:border-orange-800">
                                            <div className="flex justify-between items-center">
                                                <div className="font-bold">专项扣除总计</div>
                                                <div className="text-lg font-bold text-orange-600">-{formatCurrency(selectedRecord.result.specialDeductionsTotal)}</div>
                                            </div>
                                        </div>
                                    )}
                                    {selectedRecord.result.specialDeductionsTotal === 0 && (
                                        <div className="text-gray-500 dark:text-gray-400 text-center py-4">
                                            未设置专项扣除
                                        </div>
                                    )}
                                </div>
                            </div>

                            {/* 个税计算详情 */}
                            <div>
                                <h3 className={`${textStyles.h3} mb-4`}>个人所得税计算详情</h3>
                                <div className="bg-gray-50 dark:bg-gray-700 rounded-lg p-4 space-y-3">
                                    <div className="flex justify-between">
                                        <span>应纳税所得额（月）</span>
                                        <span className="font-semibold">{formatCurrency(selectedRecord.result.taxableIncome)}</span>
                                    </div>
                                    <div className="flex justify-between">
                                        <span>应纳税所得额（年）</span>
                                        <span className="font-semibold">{formatCurrency(selectedRecord.result.taxableIncome * 12)}</span>
                                    </div>
                                    <div className="border-t pt-3">
                                        <div className="flex justify-between">
                                            <span className="font-bold">个人所得税（月）</span>
                                            <span className="text-lg font-bold text-red-600">-{formatCurrency(selectedRecord.result.incomeTax)}</span>
                                        </div>
                                    </div>
                                    <div className="text-xs text-gray-500 dark:text-gray-400">
                                        计算公式：应纳税所得额 = (税前工资 + 其他收入) - 五险一金 - 专项扣除 - 5000元(起征点)
                                    </div>
                                </div>
                            </div>

                            {/* 工资汇总 */}
                            <div>
                                <h3 className={`${textStyles.h3} mb-4`}>工资汇总</h3>
                                <div className="bg-gradient-to-r from-blue-50 to-green-50 dark:from-blue-900/20 dark:to-green-900/20 rounded-lg p-6 border border-blue-200 dark:border-blue-800">
                                    <div className="space-y-3">
                                        <div className="flex justify-between text-lg">
                                            <span>税前基本工资</span>
                                            <span className="font-semibold text-green-600">+{formatCurrency(selectedRecord.result.grossSalary)}</span>
                                        </div>
                                        {selectedRecord.result.otherIncome > 0 && (
                                            <div className="flex justify-between text-lg">
                                                <span>其他收入</span>
                                                <span className="font-semibold text-green-600">+{formatCurrency(selectedRecord.result.otherIncome)}</span>
                                            </div>
                                        )}
                                        <div className="flex justify-between text-lg">
                                            <span>五险一金</span>
                                            <span className="font-semibold text-red-600">-{formatCurrency(selectedRecord.result.socialInsuranceTotal)}</span>
                                        </div>
                                        <div className="flex justify-between text-lg">
                                            <span>个人所得税</span>
                                            <span className="font-semibold text-red-600">-{formatCurrency(selectedRecord.result.incomeTax)}</span>
                                        </div>
                                        {selectedRecord.result.otherDeductions > 0 && (
                                            <div className="flex justify-between text-lg">
                                                <span>其他扣除</span>
                                                <span className="font-semibold text-red-600">-{formatCurrency(selectedRecord.result.otherDeductions)}</span>
                                            </div>
                                        )}
                                        <div className="border-t-2 border-blue-500 pt-3">
                                            <div className="flex justify-between text-xl font-bold">
                                                <span>到手工资</span>
                                                <span className="text-blue-600">{formatCurrency(selectedRecord.result.netSalary)}</span>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div className="p-6 border-t dark:border-gray-700">
                            <div className="flex justify-end space-x-4">
                                <button
                                    onClick={() => {
                                        loadRecord(selectedRecord);
                                        closeDetailModal();
                                    }}
                                    className={`${buttonStyles.primary} bg-gradient-to-r from-blue-600 to-blue-700 hover:from-blue-700 hover:to-blue-800`}
                                >
                                    填充到计算器
                                </button>
                                <button
                                    onClick={closeDetailModal}
                                    className="px-6 py-3 bg-gray-300 dark:bg-gray-600 text-gray-700 dark:text-gray-200 rounded-xl hover:bg-gray-400 dark:hover:bg-gray-500 transition-colors"
                                >
                                    关闭
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            )}

            {/* 参考文献 */}
            <div className="max-w-6xl mx-auto px-4 py-8 border-t border-gray-200 dark:border-gray-700">
                <div className="text-center">
                    <h3 className={`${textStyles.h3} mb-4`}>参考文献</h3>
                    <div className="text-sm text-gray-600 dark:text-gray-400">
                        <p className="mb-4">本计算器基于以下法律法规和官方文件制作：</p>
                        <div className="space-y-3">
                            <div className="flex items-center justify-center space-x-2">
                                <svg className="w-4 h-4 text-gray-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M10 6H6a2 2 0 00-2 2v10a2 2 0 002 2h10a2 2 0 002-2v-4M14 4h6m0 0v6m0-6L10 14" />
                                </svg>
                                <a
                                    href="https://www.gov.cn/xinwen/2018-09/01/content_5318233.htm"
                                    target="_blank"
                                    rel="noopener noreferrer"
                                    className="text-blue-600 dark:text-blue-400 hover:text-blue-800 dark:hover:text-blue-300 underline"
                                >
                                    中华人民共和国个人所得税法 - 中国政府网
                                </a>
                            </div>
                            <div className="flex items-center justify-center space-x-2">
                                <svg className="w-4 h-4 text-gray-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M10 6H6a2 2 0 00-2 2v10a2 2 0 002 2h10a2 2 0 002-2v-4M14 4h6m0 0v6m0-6L10 14" />
                                </svg>
                                <a
                                    href="https://czj.gz.gov.cn/zwgk/zfxxgkml/czyjs/jsjfxx/content/post_4466375.html"
                                    target="_blank"
                                    rel="noopener noreferrer"
                                    className="text-blue-600 dark:text-blue-400 hover:text-blue-800 dark:hover:text-blue-300 underline"
                                >
                                    中华人民共和国个人所得税法 - 广州市财政局
                                </a>
                            </div>
                            <div className="flex items-center justify-center space-x-2">
                                <svg className="w-4 h-4 text-gray-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M10 6H6a2 2 0 00-2 2v10a2 2 0 002 2h10a2 2 0 002-2v-4M14 4h6m0 0v6m0-6L10 14" />
                                </svg>
                                <a
                                    href="https://baike.baidu.com/item/%E4%BA%94%E9%99%A9%E4%B8%80%E9%87%91/637098"
                                    target="_blank"
                                    rel="noopener noreferrer"
                                    className="text-blue-600 dark:text-blue-400 hover:text-blue-800 dark:hover:text-blue-300 underline"
                                >
                                    五险一金 - 百度百科
                                </a>
                            </div>
                            <div className="flex items-center justify-center space-x-2">
                                <svg className="w-4 h-4 text-gray-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M10 6H6a2 2 0 00-2 2v10a2 2 0 002 2h10a2 2 0 002-2v-4M14 4h6m0 0v6m0-6L10 14" />
                                </svg>
                                <a
                                    href="https://zh.wikipedia.org/wiki/%E4%BA%94%E9%99%A9%E4%B8%80%E9%87%91"
                                    target="_blank"
                                    rel="noopener noreferrer"
                                    className="text-blue-600 dark:text-blue-400 hover:text-blue-800 dark:hover:text-blue-300 underline"
                                >
                                    五险一金 - 维基百科
                                </a>
                            </div>
                        </div>
                        <p className="mt-4 text-xs text-gray-500 dark:text-gray-500">
                            * 税率表和计算规则遵循现行个人所得税法规定<br />
                            * 本计算器仅供参考，具体税务问题请咨询当地税务机关
                        </p>
                    </div>
                </div>
            </div>

            <Footer />
        </>
    );
}

export default function ChinaSalaryCalculator() {
    return (
        <Suspense fallback={<div className="flex items-center justify-center min-h-screen">Loading...</div>}>
            <ChinaSalaryCalculatorContent />
        </Suspense>
    );
} 