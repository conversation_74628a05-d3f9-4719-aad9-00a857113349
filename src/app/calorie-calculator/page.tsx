'use client';

import { useState } from 'react';
import Footer from '@/components/Footer';
import CalculatorHeader from '@/components/CalculatorHeader';
import { containerStyles, textStyles, inputStyles, buttonStyles, statusStyles, cn } from '@/components/ui/styles';

interface CalorieForm {
  age: string;
  gender: 'male' | 'female' | '';
  height: string;
  weight: string;
  activityLevel: 'sedentary' | 'light' | 'moderate' | 'active' | 'very-active' | '';
  goal: 'maintain' | 'lose' | 'gain' | '';
}

interface CalorieResult {
  bmr: number;
  tdee: number;
  maintain: number;
  mildLoss: number;
  moderateLoss: number;
  extremeLoss: number;
  mildGain: number;
  moderateGain: number;
  extremeGain: number;
}

export default function CalorieCalculator() {
  const [form, setForm] = useState<CalorieForm>({
    age: '',
    gender: '',
    height: '',
    weight: '',
    activityLevel: '',
    goal: ''
  });

  const [result, setResult] = useState<CalorieResult | null>(null);
  const [isCalculating, setIsCalculating] = useState(false);

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement>) => {
    const { name, value } = e.target;
    setForm(prev => ({ ...prev, [name]: value }));
  };

  const calculateCalories = () => {
    setIsCalculating(true);
    
    const age = parseInt(form.age);
    const height = parseFloat(form.height);
    const weight = parseFloat(form.weight);

    // Calculate BMR using Mifflin-St Jeor Equation
    let bmr: number;
    if (form.gender === 'male') {
      bmr = 10 * weight + 6.25 * height - 5 * age + 5;
    } else {
      bmr = 10 * weight + 6.25 * height - 5 * age - 161;
    }

    // Activity level multipliers
    const activityMultipliers = {
      sedentary: 1.2,
      light: 1.375,
      moderate: 1.55,
      active: 1.725,
      'very-active': 1.9
    };

    const tdee = bmr * activityMultipliers[form.activityLevel as keyof typeof activityMultipliers];

    const calorieResult: CalorieResult = {
      bmr: Math.round(bmr),
      tdee: Math.round(tdee),
      maintain: Math.round(tdee),
      mildLoss: Math.round(tdee - 250),
      moderateLoss: Math.round(tdee - 500),
      extremeLoss: Math.round(tdee - 750),
      mildGain: Math.round(tdee + 250),
      moderateGain: Math.round(tdee + 500),
      extremeGain: Math.round(tdee + 750)
    };

    setTimeout(() => {
      setResult(calorieResult);
      setIsCalculating(false);
    }, 500);
  };

  const resetForm = () => {
    setForm({
      age: '',
      gender: '',
      height: '',
      weight: '',
      activityLevel: '',
      goal: ''
    });
    setResult(null);
  };

  const isFormValid = form.age && form.gender && form.height && form.weight && form.activityLevel;

  return (
    <>
      <div className={containerStyles.page}>
        <div className="container mx-auto px-4 py-8">
          <div className="max-w-4xl mx-auto">
            <CalculatorHeader
              title="Calorie Calculator"
              description="Calculate your daily calorie needs based on your personal information and activity level. Get recommendations for weight maintenance, loss, or gain."
              category="Health"
              className="text-center mb-8"
            />

            <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
              {/* Input Form */}
              <div className={containerStyles.form}>
                <h2 className={cn(textStyles.h2, "mb-6")}>Your Information</h2>
                
                <div className="space-y-4">
                  {/* Age */}
                  <div>
                    <label className={cn(textStyles.label, "block mb-2")}>
                      Age (years) *
                    </label>
                    <input
                      type="number"
                      name="age"
                      value={form.age}
                      onChange={handleInputChange}
                      min="10"
                      max="120"
                      className={inputStyles.base}
                      placeholder="Enter your age"
                    />
                  </div>

                  {/* Gender */}
                  <div>
                    <label className={cn(textStyles.label, "block mb-2")}>
                      Gender *
                    </label>
                    <select
                      name="gender"
                      value={form.gender}
                      onChange={handleInputChange}
                      className={inputStyles.select}
                    >
                      <option value="">Select gender</option>
                      <option value="male">Male</option>
                      <option value="female">Female</option>
                    </select>
                  </div>

                  {/* Height */}
                  <div>
                    <label className={cn(textStyles.label, "block mb-2")}>
                      Height (cm) *
                    </label>
                    <input
                      type="number"
                      name="height"
                      value={form.height}
                      onChange={handleInputChange}
                      min="100"
                      max="250"
                      step="0.1"
                      className={inputStyles.base}
                      placeholder="Enter your height in cm"
                    />
                  </div>

                  {/* Weight */}
                  <div>
                    <label className={cn(textStyles.label, "block mb-2")}>
                      Weight (kg) *
                    </label>
                    <input
                      type="number"
                      name="weight"
                      value={form.weight}
                      onChange={handleInputChange}
                      min="20"
                      max="300"
                      step="0.1"
                      className={inputStyles.base}
                      placeholder="Enter your weight in kg"
                    />
                  </div>

                  {/* Activity Level */}
                  <div>
                    <label className={cn(textStyles.label, "block mb-2")}>
                      Activity Level *
                    </label>
                    <select
                      name="activityLevel"
                      value={form.activityLevel}
                      onChange={handleInputChange}
                      className={inputStyles.select}
                    >
                      <option value="">Select activity level</option>
                      <option value="sedentary">Sedentary (little or no exercise)</option>
                      <option value="light">Light (light exercise 1-3 days/week)</option>
                      <option value="moderate">Moderate (moderate exercise 3-5 days/week)</option>
                      <option value="active">Active (hard exercise 6-7 days/week)</option>
                      <option value="very-active">Very Active (very hard exercise, physical job)</option>
                    </select>
                  </div>

                  {/* Buttons */}
                  <div className="grid grid-cols-3 gap-4 pt-4">
                    <button
                      onClick={calculateCalories}
                      disabled={!isFormValid || isCalculating}
                      className={cn("col-span-2", buttonStyles.primaryLarge)}
                    >
                      {isCalculating ? 'Calculating...' : 'Calculate Calories'}
                    </button>
                    <button
                      onClick={resetForm}
                      className={buttonStyles.secondaryLarge}
                    >
                      Reset
                    </button>
                  </div>
                </div>
              </div>

              {/* Results */}
              <div className={containerStyles.form}>
                <h2 className={cn(textStyles.h2, "mb-6")}>Your Results</h2>
                
                {!result ? (
                  <div className="text-center py-8">
                    <div className="w-16 h-16 bg-gray-200 dark:bg-gray-600 rounded-full flex items-center justify-center mx-auto mb-4">
                      <svg className={cn("w-8 h-8", textStyles.muted)} fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 7h6m0 10v-3m-3 3h.01M9 17h.01M9 14h.01M12 14h.01M15 11h.01M12 11h.01M9 11h.01M7 21h10a2 2 0 002-2V5a2 2 0 00-2-2H7a2 2 0 00-2 2v14a2 2 0 002 2z" />
                      </svg>
                    </div>
                    <p className={textStyles.muted}>Enter your information and click calculate to see your daily calorie needs.</p>
                  </div>
                ) : (
                  <div className="space-y-6">
                    {/* BMR and TDEE */}
                    <div className="grid grid-cols-2 gap-4">
                      <div className={containerStyles.cardSmall + " p-4"}>
                        <div className={cn("mb-1", textStyles.bodySmall)}>BMR (Basal Metabolic Rate)</div>
                        <div className="text-2xl font-bold text-blue-600 dark:text-blue-400">{result.bmr}</div>
                        <div className={cn("text-xs", textStyles.muted)}>calories/day</div>
                      </div>
                      <div className={containerStyles.cardSmall + " p-4"}>
                        <div className={cn("mb-1", textStyles.bodySmall)}>TDEE (Total Daily Energy)</div>
                        <div className="text-2xl font-bold text-green-600 dark:text-green-400">{result.tdee}</div>
                        <div className={cn("text-xs", textStyles.muted)}>calories/day</div>
                      </div>
                    </div>

                    {/* Weight Goals */}
                    <div>
                      <h3 className={cn(textStyles.h3, "mb-4")}>Calorie Goals</h3>
                      <div className="space-y-3">
                        <div className={containerStyles.cardSmall + " p-4"}>
                          <div className={cn("font-medium mb-2", textStyles.body)}>Maintain Weight</div>
                          <div className={cn("text-xl font-bold", textStyles.h3)}>{result.maintain} calories/day</div>
                        </div>
                        
                        <div className={containerStyles.cardSmall + " p-4"}>
                          <div className="font-medium text-red-600 dark:text-red-400 mb-2">Weight Loss</div>
                          <div className={cn("space-y-2", textStyles.bodySmall)}>
                            <div className="flex justify-between">
                              <span>Mild (0.25 kg/week):</span>
                              <span className="font-semibold">{result.mildLoss} cal/day</span>
                            </div>
                            <div className="flex justify-between">
                              <span>Moderate (0.5 kg/week):</span>
                              <span className="font-semibold">{result.moderateLoss} cal/day</span>
                            </div>
                            <div className="flex justify-between">
                              <span>Extreme (0.75 kg/week):</span>
                              <span className="font-semibold">{result.extremeLoss} cal/day</span>
                            </div>
                          </div>
                        </div>

                        <div className={containerStyles.cardSmall + " p-4"}>
                          <div className="font-medium text-blue-600 dark:text-blue-400 mb-2">Weight Gain</div>
                          <div className={cn("space-y-2", textStyles.bodySmall)}>
                            <div className="flex justify-between">
                              <span>Mild (0.25 kg/week):</span>
                              <span className="font-semibold">{result.mildGain} cal/day</span>
                            </div>
                            <div className="flex justify-between">
                              <span>Moderate (0.5 kg/week):</span>
                              <span className="font-semibold">{result.moderateGain} cal/day</span>
                            </div>
                            <div className="flex justify-between">
                              <span>Extreme (0.75 kg/week):</span>
                              <span className="font-semibold">{result.extremeGain} cal/day</span>
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                )}
              </div>
            </div>

            {/* Information */}
            <div className={cn("mt-8", statusStyles.info.container)}>
              <h3 className={statusStyles.info.title}>Important Information</h3>
              <ul className={cn(statusStyles.info.text, "space-y-1 text-sm")}>
                <li>• BMR is the number of calories your body needs at rest</li>
                <li>• TDEE includes calories burned through daily activities and exercise</li>
                <li>• These are estimates based on general formulas and may vary by individual</li>
                <li>• Consult with a healthcare professional for personalized nutrition advice</li>
                <li>• Extreme calorie restrictions should be done under medical supervision</li>
              </ul>
            </div>
          </div>
        </div>
        <Footer />
      </div>
    </>
  );
}