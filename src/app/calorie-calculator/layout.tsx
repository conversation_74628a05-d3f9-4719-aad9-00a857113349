import { generatePageTitle, generateCanonicalUrl } from '@/lib/utils';

export const metadata = {
  title: generatePageTitle("Calorie Calculator"),
  description: "Calculate your daily calorie needs based on age, gender, weight, height, and activity level. Free calorie calculator with weight loss and gain recommendations.",
  keywords: "calorie calculator, daily calories, BMR, TDEE, weight loss, weight gain, nutrition calculator",
  robots: "index, follow",
  alternates: {
    canonical: generateCanonicalUrl("/calorie-calculator"),
  },
};

export default function CalorieCalculatorLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  return children;
}