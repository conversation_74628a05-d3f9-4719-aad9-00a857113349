import type { Metadata } from 'next'

export const metadata: Metadata = {
  title: 'Time and a Half Calculator | Overtime Pay Calculator - Calc9.com',
  description: 'Calculate your time and a half overtime pay accurately. Supports hourly, daily, weekly, monthly, and annual pay rates with detailed breakdowns and effective hourly rate calculations.',
  keywords: 'time and a half calculator, overtime pay calculator, time and half pay, overtime rate calculator, 1.5x pay calculator, overtime wages, effective hourly rate',
  openGraph: {
    title: 'Time and a Half Calculator | Overtime Pay Calculator',
    description: 'Calculate your time and a half overtime pay accurately. Supports hourly, daily, weekly, monthly, and annual pay rates with detailed breakdowns and effective hourly rate calculations.',
    type: 'website',
  },
  alternates: {
    canonical: '/time-and-a-half-calculator',
  },
}

export default function TimeAndHalfCalculatorLayout({
  children,
}: {
  children: React.ReactNode
}) {
  return <>{children}</>
}