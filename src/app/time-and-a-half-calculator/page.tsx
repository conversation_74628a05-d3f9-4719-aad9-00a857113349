'use client';

import { useState, useEffect, useCallback, Suspense } from 'react';
import { useRouter, useSearchParams } from 'next/navigation';
import Footer from "@/components/Footer";
import CalculatorHeader from '@/components/CalculatorHeader';
import { containerStyles, textStyles, inputStyles, cn } from '@/components/ui/styles';

interface TimeAndHalfResult {
  standardRate: number;
  timeAndHalfRate: number;
  standardHours: number;
  timeAndHalfHours: number;
  standardPay: number;
  timeAndHalfPay: number;
  totalPay: number;
  effectiveHourlyRate?: number;
}

function TimeAndHalfCalculatorContent() {
  const router = useRouter();
  const searchParams = useSearchParams();
  
  // Form inputs
  const [payRateChoice, setPayRateChoice] = useState<string>('hourly');
  const [standardPayRate, setStandardPayRate] = useState<string>('25');
  const [hoursPerStandardDay, setHoursPerStandardDay] = useState<string>('8');
  const [standardHoursWorked, setStandardHoursWorked] = useState<string>('40');
  const [timeAndHalfHours, setTimeAndHalfHours] = useState<string>('10');
  const [currency, setCurrency] = useState<string>('USD');
  
  // Results
  const [result, setResult] = useState<TimeAndHalfResult>({
    standardRate: 25,
    timeAndHalfRate: 37.5,
    standardHours: 40,
    timeAndHalfHours: 10,
    standardPay: 1000,
    timeAndHalfPay: 375,
    totalPay: 1375
  });

  // URL state management
  const updateURL = useCallback((params: Record<string, string>) => {
    const newParams = new URLSearchParams(searchParams);
    Object.entries(params).forEach(([key, value]) => {
      if (value && value !== '') {
        newParams.set(key, value);
      } else {
        newParams.delete(key);
      }
    });
    router.push(`?${newParams.toString()}`, { scroll: false });
  }, [router, searchParams]);

  // Initialize from URL
  useEffect(() => {
    const choice = searchParams.get('payRateChoice') || 'hourly';
    const rate = searchParams.get('standardPayRate') || '25';
    const hoursPerDay = searchParams.get('hoursPerStandardDay') || '8';
    const standardHours = searchParams.get('standardHoursWorked') || '40';
    const timeHalfHours = searchParams.get('timeAndHalfHours') || '10';
    const curr = searchParams.get('currency') || 'USD';

    setPayRateChoice(choice);
    setStandardPayRate(rate);
    setHoursPerStandardDay(hoursPerDay);
    setStandardHoursWorked(standardHours);
    setTimeAndHalfHours(timeHalfHours);
    setCurrency(curr);
  }, [searchParams]);

  // Convert pay rate to hourly rate
  const getHourlyRate = useCallback(() => {
    const inputRate = parseFloat(standardPayRate) || 0;
    const hoursPerDay = parseFloat(hoursPerStandardDay) || 8;
    
    switch (payRateChoice) {
      case 'hourly':
        return inputRate;
      case 'daily':
        return inputRate / hoursPerDay;
      case 'weekly':
        return inputRate / (hoursPerDay * 5); // 5 days per week
      case 'monthly':
        return inputRate / (hoursPerDay * 5 * 4.33); // ~21.65 days per month
      case 'annual':
        return inputRate / (hoursPerDay * 5 * 52); // 260 days per year
      default:
        return inputRate;
    }
  }, [standardPayRate, payRateChoice, hoursPerStandardDay]);

  // Calculate results
  const calculateTimeAndHalf = useCallback(() => {
    const hourlyRate = getHourlyRate();
    const timeAndHalfRate = hourlyRate * 1.5;
    const standardHours = parseFloat(standardHoursWorked) || 0;
    const timeHalfHours = parseFloat(timeAndHalfHours) || 0;
    
    const standardPay = hourlyRate * standardHours;
    const timeAndHalfPay = timeAndHalfRate * timeHalfHours;
    const totalPay = standardPay + timeAndHalfPay;
    
    // Calculate effective hourly rate
    const totalHours = standardHours + timeHalfHours;
    const effectiveHourlyRate = totalHours > 0 ? totalPay / totalHours : 0;
    
    setResult({
      standardRate: hourlyRate,
      timeAndHalfRate,
      standardHours,
      timeAndHalfHours: timeHalfHours,
      standardPay,
      timeAndHalfPay,
      totalPay,
      effectiveHourlyRate
    });
  }, [getHourlyRate, standardHoursWorked, timeAndHalfHours]);

  useEffect(() => {
    calculateTimeAndHalf();
  }, [calculateTimeAndHalf]);

  // Format currency
  const formatCurrency = (amount: number): string => {
    const currencyMap: Record<string, string> = {
      USD: 'en-US',
      EUR: 'de-DE',
      GBP: 'en-GB',
      JPY: 'ja-JP',
      CNY: 'zh-CN',
      CAD: 'en-CA',
      AUD: 'en-AU',
    };
    
    return new Intl.NumberFormat(currencyMap[currency] || 'en-US', {
      style: 'currency',
      currency: currency,
      minimumFractionDigits: currency === 'JPY' ? 0 : 2,
      maximumFractionDigits: currency === 'JPY' ? 0 : 2,
    }).format(amount);
  };

  // Format number
  const formatNumber = (num: number): string => {
    return new Intl.NumberFormat('en-US', {
      minimumFractionDigits: 1,
      maximumFractionDigits: 2,
    }).format(num);
  };

  // Get pay period label
  const getPayPeriodLabel = () => {
    switch (payRateChoice) {
      case 'hourly': return 'per hour';
      case 'daily': return 'per day';
      case 'weekly': return 'per week';
      case 'monthly': return 'per month';
      case 'annual': return 'per year';
      default: return 'per hour';
    }
  };

  return (
    <>
      <div className={containerStyles.page}>
        <div className="container mx-auto px-4 py-8">
          <div className="max-w-5xl mx-auto">
            <CalculatorHeader
              title="Time and a Half Calculator"
              description="Calculate your time and a half overtime pay accurately. Enter your standard pay rate and overtime hours to get instant results with detailed breakdowns."
              category="Financial"
              className="text-center mb-12"
            />

            {/* Calculator Input Section */}
            <div className={cn(containerStyles.card, "p-8 mb-8")}>
              <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
                <div className="space-y-6">
                  <h2 className={cn(textStyles.h2, "mb-6 text-blue-600 dark:text-blue-400")}>Pay Information</h2>
                  
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                      <label className={cn(textStyles.label, "block mb-2")}>Pay Rate Choice</label>
                      <select
                        value={payRateChoice}
                        onChange={(e) => {
                          setPayRateChoice(e.target.value);
                          updateURL({ payRateChoice: e.target.value });
                        }}
                        className={cn(inputStyles.base)}
                      >
                        <option value="hourly">Hourly</option>
                        <option value="daily">Daily</option>
                        <option value="weekly">Weekly</option>
                        <option value="monthly">Monthly</option>
                        <option value="annual">Annual</option>
                      </select>
                    </div>
                    
                    <div>
                      <label className={cn(textStyles.label, "block mb-2")}>Currency</label>
                      <select
                        value={currency}
                        onChange={(e) => {
                          setCurrency(e.target.value);
                          updateURL({ currency: e.target.value });
                        }}
                        className={cn(inputStyles.base)}
                      >
                        <option value="USD">USD ($)</option>
                        <option value="EUR">EUR (€)</option>
                        <option value="GBP">GBP (£)</option>
                        <option value="JPY">JPY (¥)</option>
                        <option value="CNY">CNY (¥)</option>
                        <option value="CAD">CAD ($)</option>
                        <option value="AUD">AUD ($)</option>
                      </select>
                    </div>
                  </div>
                  
                  <div>
                    <label className={cn(textStyles.label, "block mb-2")}>
                      Standard Pay Rate ({getPayPeriodLabel()})
                    </label>
                    <div className="relative">
                      <span className="absolute left-3 top-1/2 -translate-y-1/2 text-gray-500">
                        {currency === 'EUR' ? '€' : currency === 'GBP' ? '£' : currency === 'JPY' || currency === 'CNY' ? '¥' : '$'}
                      </span>
                      <input
                        type="number"
                        value={standardPayRate}
                        onChange={(e) => {
                          setStandardPayRate(e.target.value);
                          updateURL({ standardPayRate: e.target.value });
                        }}
                        className={cn(inputStyles.base, "pl-8")}
                        placeholder="25.00"
                        step="0.01"
                        min="0"
                      />
                    </div>
                    <p className={cn("text-xs mt-1", textStyles.muted)}>
                      Your standard {payRateChoice === 'hourly' ? 'hourly' : payRateChoice === 'daily' ? 'daily' : payRateChoice === 'weekly' ? 'weekly' : payRateChoice === 'monthly' ? 'monthly' : 'annual'} pay rate
                    </p>
                  </div>

                  {payRateChoice !== 'hourly' && (
                    <div>
                      <label className={cn(textStyles.label, "block mb-2")}>
                        Hours per Standard Day
                      </label>
                      <input
                        type="number"
                        value={hoursPerStandardDay}
                        onChange={(e) => {
                          setHoursPerStandardDay(e.target.value);
                          updateURL({ hoursPerStandardDay: e.target.value });
                        }}
                        className={cn(inputStyles.base)}
                        placeholder="8"
                        step="0.5"
                        min="0"
                        max="24"
                      />
                      <p className={cn("text-xs mt-1", textStyles.muted)}>
                        Number of hours in a standard work day
                      </p>
                    </div>
                  )}
                  
                  <div>
                    <label className={cn(textStyles.label, "block mb-2")}>Standard Hours Worked</label>
                    <input
                      type="number"
                      value={standardHoursWorked}
                      onChange={(e) => {
                        setStandardHoursWorked(e.target.value);
                        updateURL({ standardHoursWorked: e.target.value });
                      }}
                      className={cn(inputStyles.base)}
                      placeholder="40"
                      step="0.5"
                      min="0"
                    />
                    <p className={cn("text-xs mt-1", textStyles.muted)}>
                      Hours worked at standard pay rate
                    </p>
                  </div>
                  
                  <div>
                    <label className={cn(textStyles.label, "block mb-2")}>Hours Worked at Time and a Half</label>
                    <input
                      type="number"
                      value={timeAndHalfHours}
                      onChange={(e) => {
                        setTimeAndHalfHours(e.target.value);
                        updateURL({ timeAndHalfHours: e.target.value });
                      }}
                      className={cn(inputStyles.base)}
                      placeholder="10"
                      step="0.5"
                      min="0"
                    />
                    <p className={cn("text-xs mt-1", textStyles.muted)}>
                      Overtime hours paid at 1.5x rate
                    </p>
                  </div>
                </div>
                
                <div className="space-y-6">
                  <h2 className={cn(textStyles.h2, "mb-6 text-green-600 dark:text-green-400")}>Calculation Results</h2>
                  
                  <div className="space-y-4">
                    <div className={cn("p-4 rounded-lg", "bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800")}>
                      <div className="flex justify-between items-center">
                        <span className={textStyles.body}>Standard Hourly Rate</span>
                        <span className={cn("font-medium", textStyles.body)}>{formatCurrency(result.standardRate)}/hr</span>
                      </div>
                    </div>
                    
                    <div className={cn("p-4 rounded-lg", "bg-green-50 dark:bg-green-900/20 border border-green-200 dark:border-green-800")}>
                      <div className="flex justify-between items-center">
                        <span className={textStyles.body}>Time and a Half Rate</span>
                        <span className={cn("font-medium", textStyles.body)}>{formatCurrency(result.timeAndHalfRate)}/hr</span>
                      </div>
                    </div>
                    
                    <div className={cn("p-4 rounded-lg", "bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800")}>
                      <div className="flex justify-between items-center">
                        <span className={textStyles.body}>Standard Pay</span>
                        <span className={cn("font-medium", textStyles.body)}>{formatCurrency(result.standardPay)}</span>
                      </div>
                      <div className="text-xs text-gray-600 dark:text-gray-400 mt-1">
                        {formatNumber(result.standardHours)} hours × {formatCurrency(result.standardRate)}/hr
                      </div>
                    </div>
                    
                    <div className={cn("p-4 rounded-lg", "bg-green-50 dark:bg-green-900/20 border border-green-200 dark:border-green-800")}>
                      <div className="flex justify-between items-center">
                        <span className={textStyles.body}>Time and a Half Pay</span>
                        <span className={cn("font-medium", textStyles.body)}>{formatCurrency(result.timeAndHalfPay)}</span>
                      </div>
                      <div className="text-xs text-gray-600 dark:text-gray-400 mt-1">
                        {formatNumber(result.timeAndHalfHours)} hours × {formatCurrency(result.timeAndHalfRate)}/hr
                      </div>
                    </div>
                    
                    <div className={cn("p-4 rounded-lg", "bg-yellow-50 dark:bg-yellow-900/20 border border-yellow-200 dark:border-yellow-800")}>
                      <div className="flex justify-between items-center">
                        <span className={cn("font-bold text-lg", textStyles.body)}>Total Pay</span>
                        <span className={cn("font-bold text-2xl", "text-yellow-600 dark:text-yellow-400")}>{formatCurrency(result.totalPay)}</span>
                      </div>
                    </div>
                    
                    {result.effectiveHourlyRate && (
                      <div className={cn("p-4 rounded-lg", "bg-purple-50 dark:bg-purple-900/20 border border-purple-200 dark:border-purple-800")}>
                        <div className="flex justify-between items-center">
                          <span className={textStyles.body}>Effective Hourly Rate</span>
                          <span className={cn("font-medium", textStyles.body)}>{formatCurrency(result.effectiveHourlyRate)}/hr</span>
                        </div>
                        <div className="text-xs text-gray-600 dark:text-gray-400 mt-1">
                          Total pay ÷ Total hours worked
                        </div>
                      </div>
                    )}
                    
                    <div className={cn("p-4 rounded-lg", "bg-gray-50 dark:bg-gray-800")}>
                      <div className="flex justify-between items-center">
                        <span className={textStyles.body}>Total Hours Worked</span>
                        <span className={cn("font-medium", textStyles.body)}>{formatNumber(result.standardHours + result.timeAndHalfHours)} hours</span>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>

            {/* Rate Comparison */}
            <div className={cn(containerStyles.card, "p-6 mb-8")}>
              <h3 className={cn(textStyles.h3, "mb-4")}>Rate Comparison</h3>
              <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                <div className="text-center">
                  <div className="text-2xl font-bold text-blue-600 dark:text-blue-400 mb-2">
                    {formatCurrency(result.standardRate)}
                  </div>
                  <div className="text-sm font-medium mb-1">Standard Rate</div>
                  <div className="text-xs text-gray-600 dark:text-gray-400">Regular working hours</div>
                </div>
                <div className="text-center">
                  <div className="text-2xl font-bold text-green-600 dark:text-green-400 mb-2">
                    {formatCurrency(result.timeAndHalfRate)}
                  </div>
                  <div className="text-sm font-medium mb-1">Time and a Half Rate</div>
                  <div className="text-xs text-gray-600 dark:text-gray-400">1.5x overtime pay</div>
                </div>
                <div className="text-center">
                  <div className="text-2xl font-bold text-purple-600 dark:text-purple-400 mb-2">
                    {result.effectiveHourlyRate ? formatCurrency(result.effectiveHourlyRate) : '--'}
                  </div>
                  <div className="text-sm font-medium mb-1">Effective Rate</div>
                  <div className="text-xs text-gray-600 dark:text-gray-400">Average hourly income</div>
                </div>
              </div>
            </div>

            {/* Information Section */}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-8 mb-8">
              <div className={cn(containerStyles.card, "p-6")}>
                <h3 className={cn(textStyles.h3, "mb-4")}>Time and a Half Rules</h3>
                <ul className={cn("space-y-3 text-sm", textStyles.body)}>
                  <li className="flex items-start space-x-2">
                    <span className="text-blue-500 mt-1">•</span>
                    <span><strong>Federal Law:</strong> Overtime required for hours over 40 per week</span>
                  </li>
                  <li className="flex items-start space-x-2">
                    <span className="text-blue-500 mt-1">•</span>
                    <span><strong>Time and a Half:</strong> 1.5 times regular hourly rate</span>
                  </li>
                  <li className="flex items-start space-x-2">
                    <span className="text-blue-500 mt-1">•</span>
                    <span><strong>Coverage:</strong> Non-exempt employees only</span>
                  </li>
                  <li className="flex items-start space-x-2">
                    <span className="text-blue-500 mt-1">•</span>
                    <span><strong>State Laws:</strong> Some states have additional requirements</span>
                  </li>
                </ul>
              </div>

              <div className={cn(containerStyles.card, "p-6")}>
                <h3 className={cn(textStyles.h3, "mb-4")}>Calculation Features</h3>
                <ul className={cn("space-y-3 text-sm", textStyles.body)}>
                  <li className="flex items-start space-x-2">
                    <span className="text-green-500 mt-1">•</span>
                    <span><strong>Rate Conversion:</strong> Automatically converts daily, weekly, etc. to hourly</span>
                  </li>
                  <li className="flex items-start space-x-2">
                    <span className="text-green-500 mt-1">•</span>
                    <span><strong>Effective Rate:</strong> Shows average income including overtime</span>
                  </li>
                  <li className="flex items-start space-x-2">
                    <span className="text-green-500 mt-1">•</span>
                    <span><strong>Flexible Input:</strong> Supports multiple pay calculation methods</span>
                  </li>
                  <li className="flex items-start space-x-2">
                    <span className="text-green-500 mt-1">•</span>
                    <span><strong>Accurate Results:</strong> Follows federal overtime standards</span>
                  </li>
                </ul>
              </div>
            </div>

            {/* Examples Section */}
            <div className={cn(containerStyles.card, "p-6 mb-8")}>
              <h3 className={cn(textStyles.h3, "mb-4")}>Calculation Examples</h3>
              <div className="space-y-4">
                <div className={cn("p-4 rounded-lg", "bg-gray-50 dark:bg-gray-800")}>
                  <h4 className={cn("font-semibold mb-2", textStyles.body)}>Hourly Employee</h4>
                  <div className="grid grid-cols-1 md:grid-cols-4 gap-4 text-sm">
                    <div>
                      <span className="font-medium">Standard Rate:</span> $20/hour
                    </div>
                    <div>
                      <span className="font-medium">Standard Pay:</span> 40 hours = $800
                    </div>
                    <div>
                      <span className="font-medium">Time and a Half:</span> 10 hours × $30 = $300
                    </div>
                    <div>
                      <span className="font-medium text-green-600 dark:text-green-400">Total:</span> $1,100
                    </div>
                  </div>
                </div>

                <div className={cn("p-4 rounded-lg", "bg-gray-50 dark:bg-gray-800")}>
                  <h4 className={cn("font-semibold mb-2", textStyles.body)}>Daily Rate Employee (Daily pay $200, 8 hours/day)</h4>
                  <div className="grid grid-cols-1 md:grid-cols-4 gap-4 text-sm">
                    <div>
                      <span className="font-medium">Equivalent Rate:</span> $25/hour
                    </div>
                    <div>
                      <span className="font-medium">Standard Pay:</span> 40 hours = $1,000
                    </div>
                    <div>
                      <span className="font-medium">Time and a Half:</span> 8 hours × $37.50 = $300
                    </div>
                    <div>
                      <span className="font-medium text-green-600 dark:text-green-400">Total:</span> $1,300
                    </div>
                  </div>
                </div>
              </div>
            </div>

          </div>
        </div>
      </div>
      <Footer />
    </>
  );
}

export default function TimeAndHalfCalculator() {
  return (
    <Suspense fallback={<div>Loading...</div>}>
      <TimeAndHalfCalculatorContent />
    </Suspense>
  );
}