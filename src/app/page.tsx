import Link from "next/link";
import Footer from "@/components/Footer";
import Logo from "@/components/Logo";
import { calculators, getCategoryStyle } from "@/data/calculators";
import { containerStyles } from '@/components/ui/styles';

export default function Home() {
  return (
    <div className={containerStyles.page}>
      <div className="container mx-auto px-4 py-8">
        <header className="text-center mb-12">
          <div className="flex justify-center items-center mb-4">
            <Logo
              width={64}
              height={64}
              className="h-12 md:h-16 w-auto mr-3"
            />
            <h1 className="text-4xl md:text-6xl font-bold text-gray-900 dark:text-white">
              Calc9
            </h1>
          </div>
          <p className="text-xl text-gray-600 dark:text-blue-100 max-w-2xl mx-auto">
            Professional calculator tools for health, fitness, and everyday calculations.
            Fast, accurate, and mobile-friendly.
          </p>
        </header>

        <main className="max-w-4xl mx-auto">
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {calculators
              .sort((a, b) => (b.priority || 0) - (a.priority || 0))
              .map((calculator) => (
                <Link key={calculator.href} href={calculator.href} className="group">
                  <div className="relative bg-white/80 dark:bg-gray-800/80 backdrop-blur-sm rounded-lg shadow-md dark:shadow-xl hover:shadow-lg dark:hover:shadow-2xl transition-all duration-300 p-6 border border-gray-200 dark:border-gray-700 hover:border-blue-500 dark:hover:border-blue-400">
                    {/* Category badge in top-right corner */}
                    <div className="absolute top-3 right-3">
                      <span className={`inline-block px-2 py-1 text-xs font-medium rounded-full transition-colors duration-300 ${getCategoryStyle(calculator.category)}`}>
                        {calculator.category}
                      </span>
                    </div>

                    <div className="flex items-center mb-3 pr-16">
                      <div className={`${calculator.iconBg} p-3 rounded-full mr-4 transition-colors duration-300`}>
                        <svg className={`w-6 h-6 ${calculator.iconColor}`} fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d={calculator.iconPath} />
                        </svg>
                      </div>
                      <h2 className="text-xl font-semibold text-gray-900 dark:text-white">{calculator.title}</h2>
                    </div>

                    <p className="text-gray-600 dark:text-slate-300 mb-4">
                      {calculator.description}
                    </p>
                    <div className="mt-4 text-blue-600 dark:text-blue-400 font-medium group-hover:text-blue-700 dark:group-hover:text-blue-300 transition-colors duration-300">
                      Calculate Now →
                    </div>
                  </div>
                </Link>
              ))}
          </div>

          {/* Features Section */}
          <div className="mt-16">
            <h3 className="text-2xl font-semibold text-gray-900 dark:text-white mb-8 text-center">
              Why Choose Our Calculators?
            </h3>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
              <div className="text-center">
                <div className="flex justify-center mb-4">
                  <svg className="w-10 h-10 text-green-600 dark:text-green-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 10V3L4 14h7v7l9-11h-7z" />
                  </svg>
                </div>
                <h4 className="text-lg font-semibold text-gray-900 dark:text-white mb-2">Lightning Fast</h4>
                <p className="text-gray-600 dark:text-gray-300">Get instant results with our optimized calculators</p>
              </div>
              <div className="text-center">
                <div className="flex justify-center mb-4">
                  <svg className="w-10 h-10 text-purple-600 dark:text-purple-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                  </svg>
                </div>
                <h4 className="text-lg font-semibold text-gray-900 dark:text-white mb-2">Accurate</h4>
                <p className="text-gray-600 dark:text-gray-300">Scientifically backed formulas and calculations</p>
              </div>
              <div className="text-center">
                <div className="flex justify-center mb-4">
                  <svg className="w-10 h-10 text-orange-600 dark:text-orange-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 18h.01M8 21h8a2 2 0 002-2V5a2 2 0 00-2-2H8a2 2 0 00-2 2v14a2 2 0 002 2z" />
                  </svg>
                </div>
                <h4 className="text-lg font-semibold text-gray-900 dark:text-white mb-2">Mobile Friendly</h4>
                <p className="text-gray-600 dark:text-gray-300">Perfect experience on any device</p>
              </div>
            </div>
          </div>
        </main>
      </div>
      <Footer />
    </div>
  );
}