'use client';

import { useState } from 'react';
import Footer from '@/components/Footer';
import CalculatorHeader from '@/components/CalculatorHeader';
import { containerStyles, textStyles, inputStyles, buttonStyles, statusStyles, cn } from '@/components/ui/styles';

interface ConcreteForm {
  shape: 'slab' | 'footing' | 'column' | 'stairs' | '';
  length: string;
  width: string;
  thickness: string;
  diameter: string; // for columns
  height: string; // for columns/stairs
  steps: string; // for stairs
  stepDepth: string; // for stairs
  unit: 'metric' | 'imperial';
}

interface ConcreteResult {
  volume: number;
  volumeWithWaste: number;
  weight: number;
  bags20kg: number;
  bags25kg: number;
  bags40kg: number;
  bags60lb: number;
  bags80lb: number;
  cost20kg?: number;
  cost25kg?: number;
  cost40kg?: number;
}

export default function ConcreteCalculator() {
  const [form, setForm] = useState<ConcreteForm>({
    shape: 'slab',
    length: '',
    width: '',
    thickness: '',
    diameter: '',
    height: '',
    steps: '',
    stepDepth: '',
    unit: 'metric'
  });

  const [result, setResult] = useState<ConcreteResult | null>(null);
  const [isCalculating, setIsCalculating] = useState(false);
  const [wastePercentage, setWastePercentage] = useState(10);

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement>) => {
    const { name, value } = e.target;
    setForm(prev => ({ ...prev, [name]: value }));
  };

  const calculateConcrete = () => {
    setIsCalculating(true);
    
    let volume = 0;
    
    // Convert to meters for calculation
    const convertToMeters = (value: string) => {
      const val = parseFloat(value);
      return form.unit === 'metric' ? val : val * 0.3048; // feet to meters
    };

    const convertToMetersFromInches = (value: string) => {
      const val = parseFloat(value);
      return form.unit === 'metric' ? val / 100 : val * 0.0254; // cm to meters or inches to meters
    };

    switch (form.shape) {
      case 'slab':
        const length = convertToMeters(form.length);
        const width = convertToMeters(form.width);
        const thickness = convertToMetersFromInches(form.thickness);
        volume = length * width * thickness;
        break;
        
      case 'footing':
        const footingLength = convertToMeters(form.length);
        const footingWidth = convertToMeters(form.width);
        const footingThickness = convertToMetersFromInches(form.thickness);
        volume = footingLength * footingWidth * footingThickness;
        break;
        
      case 'column':
        const radius = convertToMetersFromInches(form.diameter) / 2;
        const columnHeight = convertToMeters(form.height);
        volume = Math.PI * radius * radius * columnHeight;
        break;
        
      case 'stairs':
        const stairLength = convertToMeters(form.length);
        const stairWidth = convertToMeters(form.width);
        const numSteps = parseFloat(form.steps);
        const stepHeight = convertToMetersFromInches(form.thickness);
        
        // Calculate volume for triangular stairs
        const totalHeight = numSteps * stepHeight;
        volume = 0.5 * stairLength * stairWidth * totalHeight;
        break;
    }

    // Add waste
    const volumeWithWaste = volume * (1 + wastePercentage / 100);
    
    // Convert volumes for display
    const displayVolume = form.unit === 'metric' ? volume : volume * 35.3147; // cubic meters to cubic feet
    const displayVolumeWithWaste = form.unit === 'metric' ? volumeWithWaste : volumeWithWaste * 35.3147;
    
    // Calculate weight (concrete density: ~2400 kg/m³ or 150 lb/ft³)
    const density = form.unit === 'metric' ? 2400 : 150;
    const weight = displayVolumeWithWaste * density;
    
    // Calculate concrete bags needed
    const bags20kg = Math.ceil(volumeWithWaste * 108); // ~108 x 20kg bags per m³
    const bags25kg = Math.ceil(volumeWithWaste * 86);  // ~86 x 25kg bags per m³
    const bags40kg = Math.ceil(volumeWithWaste * 54);  // ~54 x 40kg bags per m³
    const bags60lb = Math.ceil(volumeWithWaste * 90);  // ~90 x 60lb bags per m³
    const bags80lb = Math.ceil(volumeWithWaste * 68);  // ~68 x 80lb bags per m³

    const concreteResult: ConcreteResult = {
      volume: Math.round(displayVolume * 100) / 100,
      volumeWithWaste: Math.round(displayVolumeWithWaste * 100) / 100,
      weight: Math.round(weight),
      bags20kg,
      bags25kg,
      bags40kg,
      bags60lb,
      bags80lb
    };

    setTimeout(() => {
      setResult(concreteResult);
      setIsCalculating(false);
    }, 500);
  };

  const resetForm = () => {
    setForm({
      shape: 'slab',
      length: '',
      width: '',
      thickness: '',
      diameter: '',
      height: '',
      steps: '',
      stepDepth: '',
      unit: 'metric'
    });
    setResult(null);
  };

  const isFormValid = () => {
    switch (form.shape) {
      case 'slab':
      case 'footing':
        return form.length && form.width && form.thickness;
      case 'column':
        return form.diameter && form.height;
      case 'stairs':
        return form.length && form.width && form.steps && form.stepDepth && form.thickness;
      default:
        return false;
    }
  };

  return (
    <>
      <div className={containerStyles.page}>
        <div className="container mx-auto px-4 py-8">
          <div className="max-w-4xl mx-auto">
            <CalculatorHeader
              title="Concrete Calculator"
              description="Calculate the volume and weight of concrete needed for your construction project. Supports slabs, footings, columns, and stairs with waste calculation."
              category="Other"
              className="text-center mb-8"
            />

            <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
              {/* Input Form */}
              <div className={containerStyles.form}>
                <h2 className={cn(textStyles.h2, "mb-6")}>Project Details</h2>
                
                <div className="space-y-4">
                  {/* Unit System */}
                  <div>
                    <label className={cn(textStyles.label, "block mb-2")}>
                      Unit System
                    </label>
                    <div className="flex space-x-4">
                      <label className={cn("flex items-center", textStyles.body)}>
                        <input
                          type="radio"
                          name="unit"
                          value="metric"
                          checked={form.unit === 'metric'}
                          onChange={handleInputChange}
                          className="mr-2 text-blue-600 focus:ring-blue-500 dark:focus:ring-blue-400"
                        />
                        Metric (m, cm)
                      </label>
                      <label className={cn("flex items-center", textStyles.body)}>
                        <input
                          type="radio"
                          name="unit"
                          value="imperial"
                          checked={form.unit === 'imperial'}
                          onChange={handleInputChange}
                          className="mr-2 text-blue-600 focus:ring-blue-500 dark:focus:ring-blue-400"
                        />
                        Imperial (ft, in)
                      </label>
                    </div>
                  </div>

                  {/* Shape */}
                  <div>
                    <label className={cn(textStyles.label, "block mb-2")}>
                      Shape/Type *
                    </label>
                    <select
                      name="shape"
                      value={form.shape}
                      onChange={handleInputChange}
                      className={inputStyles.select}
                    >
                      <option value="slab">Slab/Rectangle</option>
                      <option value="footing">Footing</option>
                      <option value="column">Column (Circular)</option>
                      <option value="stairs">Stairs</option>
                    </select>
                  </div>

                  {/* Conditional Fields based on shape */}
                  {(form.shape === 'slab' || form.shape === 'footing' || form.shape === 'stairs') && (
                    <>
                      <div>
                        <label className={cn(textStyles.label, "block mb-2")}>
                          Length ({form.unit === 'metric' ? 'm' : 'ft'}) *
                        </label>
                        <input
                          type="number"
                          name="length"
                          value={form.length}
                          onChange={handleInputChange}
                          min="0"
                          step="0.1"
                          className={inputStyles.base}
                          placeholder="Enter length"
                        />
                      </div>

                      <div>
                        <label className={cn(textStyles.label, "block mb-2")}>
                          Width ({form.unit === 'metric' ? 'm' : 'ft'}) *
                        </label>
                        <input
                          type="number"
                          name="width"
                          value={form.width}
                          onChange={handleInputChange}
                          min="0"
                          step="0.1"
                          className={inputStyles.base}
                          placeholder="Enter width"
                        />
                      </div>
                    </>
                  )}

                  {form.shape === 'column' && (
                    <>
                      <div>
                        <label className={cn(textStyles.label, "block mb-2")}>
                          Diameter ({form.unit === 'metric' ? 'cm' : 'in'}) *
                        </label>
                        <input
                          type="number"
                          name="diameter"
                          value={form.diameter}
                          onChange={handleInputChange}
                          min="0"
                          step="0.1"
                          className={inputStyles.base}
                          placeholder="Enter diameter"
                        />
                      </div>

                      <div>
                        <label className={cn(textStyles.label, "block mb-2")}>
                          Height ({form.unit === 'metric' ? 'm' : 'ft'}) *
                        </label>
                        <input
                          type="number"
                          name="height"
                          value={form.height}
                          onChange={handleInputChange}
                          min="0"
                          step="0.1"
                          className={inputStyles.base}
                          placeholder="Enter height"
                        />
                      </div>
                    </>
                  )}

                  {form.shape === 'stairs' && (
                    <>
                      <div>
                        <label className={cn(textStyles.label, "block mb-2")}>
                          Number of Steps *
                        </label>
                        <input
                          type="number"
                          name="steps"
                          value={form.steps}
                          onChange={handleInputChange}
                          min="1"
                          className={inputStyles.base}
                          placeholder="Enter number of steps"
                        />
                      </div>

                      <div>
                        <label className={cn(textStyles.label, "block mb-2")}>
                          Step Depth ({form.unit === 'metric' ? 'cm' : 'in'}) *
                        </label>
                        <input
                          type="number"
                          name="stepDepth"
                          value={form.stepDepth}
                          onChange={handleInputChange}
                          min="0"
                          step="0.1"
                          className={inputStyles.base}
                          placeholder="Enter step depth"
                        />
                      </div>
                    </>
                  )}

                  {(form.shape === 'slab' || form.shape === 'footing' || form.shape === 'stairs') && (
                    <div>
                      <label className={cn(textStyles.label, "block mb-2")}>
                        Thickness ({form.unit === 'metric' ? 'cm' : 'in'}) *
                      </label>
                      <input
                        type="number"
                        name="thickness"
                        value={form.thickness}
                        onChange={handleInputChange}
                        min="0"
                        step="0.1"
                        className={inputStyles.base}
                        placeholder="Enter thickness"
                      />
                    </div>
                  )}

                  {/* Waste Percentage */}
                  <div>
                    <label className={cn(textStyles.label, "block mb-2")}>
                      Waste Percentage: {wastePercentage}%
                    </label>
                    <input
                      type="range"
                      min="5"
                      max="20"
                      value={wastePercentage}
                      onChange={(e) => setWastePercentage(parseInt(e.target.value))}
                      className="w-full h-2 bg-gray-200 dark:bg-gray-600 rounded-lg appearance-none cursor-pointer slider"
                    />
                    <div className={cn("flex justify-between text-xs mt-1", textStyles.muted)}>
                      <span>5%</span>
                      <span>20%</span>
                    </div>
                  </div>

                  {/* Buttons */}
                  <div className="grid grid-cols-3 gap-4 pt-4">
                    <button
                      onClick={calculateConcrete}
                      disabled={!isFormValid() || isCalculating}
                      className={cn("col-span-2", buttonStyles.primaryLarge)}
                    >
                      {isCalculating ? 'Calculating...' : 'Calculate'}
                    </button>
                    <button
                      onClick={resetForm}
                      className={buttonStyles.secondaryLarge}
                    >
                      Reset
                    </button>
                  </div>
                </div>
              </div>

              {/* Results */}
              <div className={containerStyles.form}>
                <h2 className={cn(textStyles.h2, "mb-6")}>Results</h2>
                
                {!result ? (
                  <div className="text-center py-8">
                    <div className="w-16 h-16 bg-gray-200 dark:bg-gray-600 rounded-full flex items-center justify-center mx-auto mb-4">
                      <svg className={cn("w-8 h-8", textStyles.muted)} fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4" />
                      </svg>
                    </div>
                    <p className={textStyles.muted}>Enter your project dimensions to calculate concrete requirements.</p>
                  </div>
                ) : (
                  <div className="space-y-6">
                    {/* Volume */}
                    <div className="grid grid-cols-2 gap-4">
                      <div className={containerStyles.cardSmall + " p-4"}>
                        <div className={cn("mb-1", textStyles.bodySmall)}>Volume Needed</div>
                        <div className="text-xl font-bold text-blue-600 dark:text-blue-400">{result.volume}</div>
                        <div className={cn("text-xs", textStyles.muted)}>{form.unit === 'metric' ? 'm³' : 'ft³'}</div>
                      </div>
                      <div className={containerStyles.cardSmall + " p-4"}>
                        <div className={cn("mb-1", textStyles.bodySmall)}>With {wastePercentage}% Waste</div>
                        <div className="text-xl font-bold text-green-600 dark:text-green-400">{result.volumeWithWaste}</div>
                        <div className={cn("text-xs", textStyles.muted)}>{form.unit === 'metric' ? 'm³' : 'ft³'}</div>
                      </div>
                    </div>

                    {/* Weight */}
                    <div className={containerStyles.cardSmall + " p-4"}>
                      <div className={cn("mb-1", textStyles.bodySmall)}>Estimated Weight</div>
                      <div className={cn("text-2xl font-bold", textStyles.h2)}>{result.weight.toLocaleString()}</div>
                      <div className={cn("text-xs", textStyles.muted)}>{form.unit === 'metric' ? 'kg' : 'lbs'}</div>
                    </div>

                    {/* Bags Needed */}
                    <div>
                      <h3 className={cn(textStyles.h3, "mb-4")}>Concrete Bags Needed</h3>
                      <div className="space-y-3">
                        {form.unit === 'metric' ? (
                          <>
                            <div className={cn(containerStyles.cardSmall, "p-3 flex justify-between items-center")}>
                              <span className={textStyles.body}>20kg bags:</span>
                              <span className={cn("font-semibold", textStyles.body)}>{result.bags20kg}</span>
                            </div>
                            <div className={cn(containerStyles.cardSmall, "p-3 flex justify-between items-center")}>
                              <span className={textStyles.body}>25kg bags:</span>
                              <span className={cn("font-semibold", textStyles.body)}>{result.bags25kg}</span>
                            </div>
                            <div className={cn(containerStyles.cardSmall, "p-3 flex justify-between items-center")}>
                              <span className={textStyles.body}>40kg bags:</span>
                              <span className={cn("font-semibold", textStyles.body)}>{result.bags40kg}</span>
                            </div>
                          </>
                        ) : (
                          <>
                            <div className={cn(containerStyles.cardSmall, "p-3 flex justify-between items-center")}>
                              <span className={textStyles.body}>60lb bags:</span>
                              <span className={cn("font-semibold", textStyles.body)}>{result.bags60lb}</span>
                            </div>
                            <div className={cn(containerStyles.cardSmall, "p-3 flex justify-between items-center")}>
                              <span className={textStyles.body}>80lb bags:</span>
                              <span className={cn("font-semibold", textStyles.body)}>{result.bags80lb}</span>
                            </div>
                          </>
                        )}
                      </div>
                    </div>
                  </div>
                )}
              </div>
            </div>

            {/* Information */}
            <div className={cn("mt-8", statusStyles.info.container)}>
              <h3 className={statusStyles.info.title}>Important Tips</h3>
              <ul className={cn(statusStyles.info.text, "space-y-1 text-sm")}>
                <li>• Always add 5-15% extra concrete to account for waste and spillage</li>
                <li>• Consider ordering slightly more concrete than calculated to avoid running short</li>
                <li>• Different concrete mixes may have different coverage per bag</li>
                <li>• Weather conditions can affect concrete setting time and requirements</li>
                <li>• Consult with concrete suppliers for specific mix recommendations</li>
              </ul>
            </div>
          </div>
        </div>
        <Footer />
      </div>
    </>
  );
}