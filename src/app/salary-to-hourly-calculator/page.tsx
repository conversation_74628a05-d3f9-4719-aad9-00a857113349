'use client';

import { useState, useEffect, useCallback, Suspense } from 'react';
import { useRouter, useSearchParams } from 'next/navigation';
import Footer from "@/components/Footer";
import CalculatorHeader from '@/components/CalculatorHeader';
import { containerStyles, textStyles, inputStyles, cn } from '@/components/ui/styles';

interface SalaryToHourlyResult {
  annualSalary: number;
  hourlyRate: number;
  weeklyPay: number;
  monthlyPay: number;
  biweeklyPay: number;
  hoursPerWeek: number;
  weeksPerYear: number;
}

function SalaryToHourlyCalculatorContent() {
  const router = useRouter();
  const searchParams = useSearchParams();
  
  // Form inputs
  const [payPeriod, setPayPeriod] = useState<string>('annual');
  const [salaryAmount, setSalaryAmount] = useState<string>('52000');
  const [hoursPerWeek, setHoursPerWeek] = useState<string>('40');
  const [weeksPerYear, setWeeksPerYear] = useState<string>('52');
  const [currency, setCurrency] = useState<string>('USD');
  
  // Results
  const [result, setResult] = useState<SalaryToHourlyResult>({
    annualSalary: 52000,
    hourlyRate: 25,
    weeklyPay: 1000,
    monthlyPay: 4333.33,
    biweeklyPay: 2000,
    hoursPerWeek: 40,
    weeksPerYear: 52
  });

  // URL state management
  const updateURL = useCallback((params: Record<string, string>) => {
    const newParams = new URLSearchParams(searchParams);
    Object.entries(params).forEach(([key, value]) => {
      if (value && value !== '') {
        newParams.set(key, value);
      } else {
        newParams.delete(key);
      }
    });
    router.push(`?${newParams.toString()}`, { scroll: false });
  }, [router, searchParams]);

  // Initialize from URL
  useEffect(() => {
    const period = searchParams.get('payPeriod') || 'annual';
    const salary = searchParams.get('salary') || '52000';
    const hours = searchParams.get('hoursPerWeek') || '40';
    const weeks = searchParams.get('weeksPerYear') || '52';
    const curr = searchParams.get('currency') || 'USD';

    setPayPeriod(period);
    setSalaryAmount(salary);
    setHoursPerWeek(hours);
    setWeeksPerYear(weeks);
    setCurrency(curr);
  }, [searchParams]);

  // Calculate results
  const calculateSalaryToHourly = useCallback(() => {
    const amount = parseFloat(salaryAmount) || 0;
    const hours = parseFloat(hoursPerWeek) || 40;
    const weeks = parseFloat(weeksPerYear) || 52;
    
    // Convert different pay periods to annual salary
    let annualSalary = 0;
    switch (payPeriod) {
      case 'annual':
        annualSalary = amount;
        break;
      case 'monthly':
        annualSalary = amount * 12;
        break;
      case 'weekly':
        annualSalary = amount * weeks;
        break;
      case 'biweekly':
        annualSalary = amount * 26;
        break;
      case 'daily':
        annualSalary = amount * 5 * weeks; // 5 days per week
        break;
      default:
        annualSalary = amount;
    }
    
    const totalHoursPerYear = hours * weeks;
    const hourlyRate = totalHoursPerYear > 0 ? annualSalary / totalHoursPerYear : 0;
    const weeklyPay = annualSalary / weeks;
    const monthlyPay = annualSalary / 12;
    const biweeklyPay = annualSalary / 26;
    
    setResult({
      annualSalary,
      hourlyRate,
      weeklyPay,
      monthlyPay,
      biweeklyPay,
      hoursPerWeek: hours,
      weeksPerYear: weeks
    });
  }, [salaryAmount, payPeriod, hoursPerWeek, weeksPerYear]);

  useEffect(() => {
    calculateSalaryToHourly();
  }, [calculateSalaryToHourly]);

  // Format currency
  const formatCurrency = (amount: number): string => {
    const currencyMap: Record<string, string> = {
      USD: 'en-US',
      EUR: 'de-DE',
      GBP: 'en-GB',
      INR: 'en-IN',
      JPY: 'ja-JP',
      CAD: 'en-CA',
      AUD: 'en-AU',
      CHF: 'de-CH',
      CNY: 'zh-CN',
      SGD: 'en-SG',
    };
    
    return new Intl.NumberFormat(currencyMap[currency] || 'en-US', {
      style: 'currency',
      currency: currency,
      minimumFractionDigits: currency === 'JPY' ? 0 : 2,
      maximumFractionDigits: currency === 'JPY' ? 0 : 2,
    }).format(amount);
  };

  return (
    <>
      <div className={containerStyles.page}>
        <div className="container mx-auto px-4 py-8">
          <div className="max-w-4xl mx-auto">
            <CalculatorHeader
              title="Salary to Hourly Calculator"
              description="Convert your annual salary to hourly rate. Calculate your hourly wage based on your annual salary, weekly hours, and working weeks per year."
              category="Financial"
              className="text-center mb-12"
            />

            {/* Calculator Input Section */}
            <div className={cn(containerStyles.card, "p-8 mb-8")}>
              <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
                <div className="space-y-6">
                  <h2 className={cn(textStyles.h2, "mb-6")}>Enter Your Salary Information</h2>
                  
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                      <label className={cn(textStyles.label, "block mb-2")}>Pay Period</label>
                      <select
                        value={payPeriod}
                        onChange={(e) => {
                          setPayPeriod(e.target.value);
                          updateURL({ payPeriod: e.target.value });
                        }}
                        className={cn(inputStyles.base)}
                      >
                        <option value="annual">Annual</option>
                        <option value="monthly">Monthly</option>
                        <option value="weekly">Weekly</option>
                        <option value="biweekly">Bi-weekly</option>
                        <option value="daily">Daily</option>
                      </select>
                    </div>
                    
                    <div>
                      <label className={cn(textStyles.label, "block mb-2")}>Currency</label>
                      <select
                        value={currency}
                        onChange={(e) => {
                          setCurrency(e.target.value);
                          updateURL({ currency: e.target.value });
                        }}
                        className={cn(inputStyles.base)}
                      >
                        <option value="USD">USD ($)</option>
                        <option value="EUR">EUR (€)</option>
                        <option value="GBP">GBP (£)</option>
                        <option value="INR">INR (₹)</option>
                        <option value="JPY">JPY (¥)</option>
                        <option value="CAD">CAD ($)</option>
                        <option value="AUD">AUD ($)</option>
                        <option value="CHF">CHF (₣)</option>
                        <option value="CNY">CNY (¥)</option>
                        <option value="SGD">SGD ($)</option>
                      </select>
                    </div>
                  </div>
                  <p className={cn("text-xs mt-1", textStyles.muted)}>
                    Select how your salary is paid and currency
                  </p>
                  
                  <div>
                    <label className={cn(textStyles.label, "block mb-2")}>
                      {payPeriod === 'annual' ? 'Annual Salary' : 
                       payPeriod === 'monthly' ? 'Monthly Salary' :
                       payPeriod === 'weekly' ? 'Weekly Salary' :
                       payPeriod === 'biweekly' ? 'Bi-weekly Salary' :
                       'Daily Salary'}
                    </label>
                    <div className="relative">
                      <span className="absolute left-3 top-1/2 -translate-y-1/2 text-gray-500">
                        {currency === 'EUR' ? '€' : currency === 'GBP' ? '£' : currency === 'INR' ? '₹' : currency === 'JPY' ? '¥' : currency === 'CNY' ? '¥' : currency === 'CHF' ? '₣' : '$'}
                      </span>
                      <input
                        type="number"
                        value={salaryAmount}
                        onChange={(e) => {
                          setSalaryAmount(e.target.value);
                          updateURL({ salary: e.target.value });
                        }}
                        className={cn(inputStyles.base, "pl-8")}
                        placeholder={
                          payPeriod === 'annual' ? '52000' :
                          payPeriod === 'monthly' ? '4333' :
                          payPeriod === 'weekly' ? '1000' :
                          payPeriod === 'biweekly' ? '2000' :
                          '200'
                        }
                        step={payPeriod === 'annual' ? '100' : payPeriod === 'monthly' ? '10' : '1'}
                        min="0"
                      />
                    </div>
                    <p className={cn("text-xs mt-1", textStyles.muted)}>
                      Your gross {payPeriod} salary before taxes and deductions
                    </p>
                  </div>
                  
                  <div>
                    <label className={cn(textStyles.label, "block mb-2")}>Hours per Week</label>
                    <input
                      type="number"
                      value={hoursPerWeek}
                      onChange={(e) => {
                        setHoursPerWeek(e.target.value);
                        updateURL({ hoursPerWeek: e.target.value });
                      }}
                      className={cn(inputStyles.base)}
                      placeholder="40"
                      step="0.5"
                      min="0"
                    />
                    <p className={cn("text-xs mt-1", textStyles.muted)}>
                      Number of hours you work per week (typically 40)
                    </p>
                  </div>
                  
                  <div>
                    <label className={cn(textStyles.label, "block mb-2")}>Weeks per Year</label>
                    <input
                      type="number"
                      value={weeksPerYear}
                      onChange={(e) => {
                        setWeeksPerYear(e.target.value);
                        updateURL({ weeksPerYear: e.target.value });
                      }}
                      className={cn(inputStyles.base)}
                      placeholder="52"
                      step="1"
                      min="1"
                      max="52"
                    />
                    <p className={cn("text-xs mt-1", textStyles.muted)}>
                      Working weeks per year (52 minus vacation weeks)
                    </p>
                  </div>
                </div>
                
                <div>
                  <h2 className={cn(textStyles.h2, "mb-6")}>Hourly Rate Breakdown</h2>
                  <div className="space-y-4">
                    <div className={cn("p-4 rounded-lg", "bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800")}>
                      <div className="flex justify-between items-center">
                        <span className={textStyles.body}>Hourly Rate</span>
                        <span className={cn("font-bold text-2xl", "text-blue-600 dark:text-blue-400")}>{formatCurrency(result.hourlyRate)}</span>
                      </div>
                    </div>
                    
                    <div className={cn("p-4 rounded-lg", "bg-gray-50 dark:bg-gray-800")}>
                      <div className="flex justify-between items-center">
                        <span className={textStyles.body}>Weekly Pay</span>
                        <span className={cn("font-medium", textStyles.body)}>{formatCurrency(result.weeklyPay)}</span>
                      </div>
                    </div>
                    
                    <div className={cn("p-4 rounded-lg", "bg-gray-50 dark:bg-gray-800")}>
                      <div className="flex justify-between items-center">
                        <span className={textStyles.body}>Biweekly Pay</span>
                        <span className={cn("font-medium", textStyles.body)}>{formatCurrency(result.biweeklyPay)}</span>
                      </div>
                    </div>
                    
                    <div className={cn("p-4 rounded-lg", "bg-gray-50 dark:bg-gray-800")}>
                      <div className="flex justify-between items-center">
                        <span className={textStyles.body}>Monthly Pay</span>
                        <span className={cn("font-medium", textStyles.body)}>{formatCurrency(result.monthlyPay)}</span>
                      </div>
                    </div>
                    
                    <div className={cn("p-4 rounded-lg", "bg-green-50 dark:bg-green-900/20 border border-green-200 dark:border-green-800")}>
                      <div className="flex justify-between items-center">
                        <span className={cn("font-bold", textStyles.body)}>Annual Salary</span>
                        <span className={cn("font-bold", textStyles.body)}>{formatCurrency(result.annualSalary)}</span>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>

            {/* Information Section */}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-8 mb-8">
              <div className={cn(containerStyles.card, "p-6")}>
                <h3 className={cn(textStyles.h3, "mb-4")}>Salary to Hourly Conversion</h3>
                <ul className={cn("space-y-3 text-sm", textStyles.body)}>
                  <li className="flex items-start space-x-2">
                    <span className="text-blue-500 mt-1">•</span>
                    <span><strong>Formula:</strong> Annual Salary ÷ (Hours/Week × Weeks/Year)</span>
                  </li>
                  <li className="flex items-start space-x-2">
                    <span className="text-blue-500 mt-1">•</span>
                    <span><strong>Standard Year:</strong> 2,080 hours (40 hours × 52 weeks)</span>
                  </li>
                  <li className="flex items-start space-x-2">
                    <span className="text-blue-500 mt-1">•</span>
                    <span><strong>Vacation Time:</strong> Reduces working weeks per year</span>
                  </li>
                  <li className="flex items-start space-x-2">
                    <span className="text-blue-500 mt-1">•</span>
                    <span><strong>Gross Pay:</strong> Before taxes and deductions</span>
                  </li>
                </ul>
              </div>

              <div className={cn(containerStyles.card, "p-6")}>
                <h3 className={cn(textStyles.h3, "mb-4")}>When to Use This Calculator</h3>
                <ul className={cn("space-y-3 text-sm", textStyles.body)}>
                  <li className="flex items-start space-x-2">
                    <span className="text-green-500 mt-1">•</span>
                    <span><strong>Job Comparison:</strong> Compare salary vs hourly positions</span>
                  </li>
                  <li className="flex items-start space-x-2">
                    <span className="text-green-500 mt-1">•</span>
                    <span><strong>Freelance Rates:</strong> Set hourly rates based on desired salary</span>
                  </li>
                  <li className="flex items-start space-x-2">
                    <span className="text-green-500 mt-1">•</span>
                    <span><strong>Overtime Planning:</strong> Calculate true hourly value</span>
                  </li>
                  <li className="flex items-start space-x-2">
                    <span className="text-green-500 mt-1">•</span>
                    <span><strong>Budget Planning:</strong> Understand your hourly earning power</span>
                  </li>
                </ul>
              </div>
            </div>

            {/* Examples Section */}
            <div className={cn(containerStyles.card, "p-6 mb-8")}>
              <h3 className={cn(textStyles.h3, "mb-4")}>Salary to Hourly Examples</h3>
              <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                <div className="text-center">
                  <div className="text-lg font-bold text-blue-600 dark:text-blue-400 mb-2">$40,000 → $19.23/hr</div>
                  <div className="text-sm font-medium mb-1">Entry Level</div>
                  <div className="text-xs text-gray-600 dark:text-gray-400">40 hrs/week, 52 weeks/year</div>
                </div>
                <div className="text-center">
                  <div className="text-lg font-bold text-green-600 dark:text-green-400 mb-2">$60,000 → $28.85/hr</div>
                  <div className="text-sm font-medium mb-1">Mid-Career</div>
                  <div className="text-xs text-gray-600 dark:text-gray-400">40 hrs/week, 52 weeks/year</div>
                </div>
                <div className="text-center">
                  <div className="text-lg font-bold text-purple-600 dark:text-purple-400 mb-2">$80,000 → $38.46/hr</div>
                  <div className="text-sm font-medium mb-1">Senior Level</div>
                  <div className="text-xs text-gray-600 dark:text-gray-400">40 hrs/week, 52 weeks/year</div>
                </div>
              </div>
            </div>

          </div>
        </div>
      </div>
      <Footer />
    </>
  );
}

export default function SalaryToHourlyCalculator() {
  return (
    <Suspense fallback={<div>Loading...</div>}>
      <SalaryToHourlyCalculatorContent />
    </Suspense>
  );
}