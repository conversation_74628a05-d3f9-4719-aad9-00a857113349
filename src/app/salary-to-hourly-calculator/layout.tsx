import type { Metadata } from 'next'

export const metadata: Metadata = {
  title: 'Salary to Hourly Calculator | Convert Annual Salary to Hourly Rate',
  description: 'Convert your annual salary to hourly rate. Calculate your hourly wage based on your annual salary, weekly hours, and working weeks per year.',
  keywords: 'salary to hourly calculator, annual salary to hourly rate, salary converter, hourly wage calculator, salary breakdown, hourly rate conversion',
  openGraph: {
    title: 'Salary to Hourly Calculator | Convert Annual Salary to Hourly Rate',
    description: 'Convert your annual salary to hourly rate. Calculate your hourly wage based on your annual salary, weekly hours, and working weeks per year.',
    type: 'website',
  },
  alternates: {
    canonical: '/salary-to-hourly-calculator',
  },
}

export default function SalaryToHourlyCalculatorLayout({
  children,
}: {
  children: React.ReactNode
}) {
  return <>{children}</>
}