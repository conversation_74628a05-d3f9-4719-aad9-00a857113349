'use client';

import { useState, useEffect, useCallback } from 'react';
import Footer from "@/components/Footer";
import CalculatorHeader from '@/components/CalculatorHeader';
import { containerStyles, textStyles, inputStyles, statusStyles, cn } from '@/components/ui/styles';

interface RomanNumeralRule {
  symbol: string;
  value: number;
  description: string;
}

export default function RomanNumeralConverter() {
  const [arabicInput, setArabicInput] = useState<string>('42');
  const [romanInput, setRomanInput] = useState<string>('XLII');
  const [arabicResult, setArabicResult] = useState<number>(42);
  const [romanResult, setRomanResult] = useState<string>('XLII');
  const [arabicError, setArabicError] = useState<string>('');
  const [romanError, setRomanError] = useState<string>('');

  const romanNumerals: RomanNumeralRule[] = [
    { symbol: 'I', value: 1, description: 'One' },
    { symbol: 'V', value: 5, description: 'Five' },
    { symbol: 'X', value: 10, description: 'Ten' },
    { symbol: 'L', value: 50, description: 'Fifty' },
    { symbol: 'C', value: 100, description: 'One Hundred' },
    { symbol: 'D', value: 500, description: 'Five Hundred' },
    { symbol: 'M', value: 1000, description: 'One Thousand' }
  ];

  const subtractiveRules = [
    { symbol: 'CM', value: 900, description: '1000 - 100' },
    { symbol: 'CD', value: 400, description: '500 - 100' },
    { symbol: 'XC', value: 90, description: '100 - 10' },
    { symbol: 'XL', value: 40, description: '50 - 10' },
    { symbol: 'IX', value: 9, description: '10 - 1' },
    { symbol: 'IV', value: 4, description: '5 - 1' }
  ];

  const allRomanValues = [
    ...subtractiveRules,
    ...romanNumerals
  ].sort((a, b) => b.value - a.value);

  const arabicToRoman = useCallback((num: number): string => {
    if (num <= 0 || num > 3999) {
      throw new Error('Number must be between 1 and 3999');
    }

    let result = '';
    let remaining = num;

    for (const { symbol, value } of allRomanValues) {
      while (remaining >= value) {
        result += symbol;
        remaining -= value;
      }
    }

    return result;
  }, [allRomanValues]);

  const romanToArabic = useCallback((roman: string): number => {
    if (!roman.trim()) {
      throw new Error('Roman numeral cannot be empty');
    }

    const upperRoman = roman.toUpperCase().trim();

    // Validate characters
    if (!/^[IVXLCDM]+$/.test(upperRoman)) {
      throw new Error('Invalid characters in Roman numeral');
    }

    let result = 0;
    let i = 0;

    // Process subtractive combinations first
    for (const { symbol, value } of subtractiveRules) {
      while (upperRoman.substr(i, symbol.length) === symbol) {
        result += value;
        i += symbol.length;
      }
    }

    // Process individual symbols
    while (i < upperRoman.length) {
      const char = upperRoman[i];
      const numeral = romanNumerals.find(r => r.symbol === char);

      if (!numeral) {
        throw new Error(`Invalid Roman numeral character: ${char}`);
      }

      result += numeral.value;
      i++;
    }

    // Validate the result by converting back
    const backConverted = arabicToRoman(result);
    if (backConverted !== upperRoman) {
      throw new Error('Invalid Roman numeral format');
    }

    return result;
  }, [arabicToRoman, romanNumerals, subtractiveRules]);

  const handleArabicChange = (value: string) => {
    setArabicInput(value);
    setArabicError('');

    if (!value.trim()) {
      setRomanResult('');
      return;
    }

    const num = parseInt(value);
    if (isNaN(num)) {
      setArabicError('Please enter a valid number');
      return;
    }

    try {
      const roman = arabicToRoman(num);
      setRomanResult(roman);
      setRomanInput(roman);
    } catch (error) {
      setArabicError(error instanceof Error ? error.message : 'Conversion error');
      setRomanResult('');
    }
  };

  const handleRomanChange = (value: string) => {
    setRomanInput(value);
    setRomanError('');

    if (!value.trim()) {
      setArabicResult(0);
      return;
    }

    try {
      const arabic = romanToArabic(value);
      setArabicResult(arabic);
      setArabicInput(arabic.toString());
    } catch (error) {
      setRomanError(error instanceof Error ? error.message : 'Conversion error');
      setArabicResult(0);
    }
  };

  const handleQuickExample = (arabic: number, roman: string) => {
    setArabicInput(arabic.toString());
    setRomanInput(roman);
    setArabicResult(arabic);
    setRomanResult(roman);
    setArabicError('');
    setRomanError('');
  };

  useEffect(() => {
    handleArabicChange(arabicInput);
  }, []);

  return (
    <>
      <div className={containerStyles.page}>
        <div className="container mx-auto px-4 py-8">
          <div className="max-w-6xl mx-auto">
            <CalculatorHeader
              title="Roman Numeral Converter"
              description="Convert between Arabic numbers and Roman numerals. Learn about the ancient Roman numbering system, its rules, and modern applications."
              category="Math"
              className="text-center mb-12"
            />

            <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
              {/* Converter Section */}
              <div className={containerStyles.form}>
                <h2 className={cn(textStyles.h2, "mb-6")}>Number Converter</h2>

                <div className="space-y-6">
                  {/* Arabic to Roman */}
                  <div>
                    <label className={cn(textStyles.label, "block mb-2")}>
                      Arabic Number (1-3999)
                    </label>
                    <input
                      type="number"
                      min="1"
                      max="3999"
                      value={arabicInput}
                      onChange={(e) => handleArabicChange(e.target.value)}
                      className={cn(inputStyles.base, "text-lg")}
                      placeholder="Enter Arabic number"
                    />
                    {arabicError && (
                      <p className={cn("text-sm mt-1", statusStyles.danger.text)}>{arabicError}</p>
                    )}
                  </div>

                  {/* Roman to Arabic */}
                  <div>
                    <label className={cn(textStyles.label, "block mb-2")}>
                      Roman Numeral
                    </label>
                    <input
                      type="text"
                      value={romanInput}
                      onChange={(e) => handleRomanChange(e.target.value.toUpperCase())}
                      className={cn(inputStyles.base, "font-mono text-lg uppercase")}
                      placeholder="Enter Roman numeral"
                    />
                    {romanError && (
                      <p className={cn("text-sm mt-1", statusStyles.danger.text)}>{romanError}</p>
                    )}
                  </div>

                  {/* Quick Examples */}
                  <div className="border-t border-gray-200 dark:border-gray-700 pt-4">
                    <h3 className={cn("text-sm font-medium mb-3", textStyles.h4)}>Quick Examples</h3>
                    <div className="grid grid-cols-2 gap-2">
                      <button
                        onClick={() => handleQuickExample(1994, 'MCMXCIV')}
                        className={cn(containerStyles.cardSmall, "text-left p-2 text-sm transition-colors hover:bg-gray-50 dark:hover:bg-gray-700")}
                      >
                        <div className={cn("font-medium", textStyles.body)}>1994 = MCMXCIV</div>
                        <div className={cn("text-xs", textStyles.muted)}>Complex example</div>
                      </button>
                      <button
                        onClick={() => handleQuickExample(2024, 'MMXXIV')}
                        className={cn(containerStyles.cardSmall, "text-left p-2 text-sm transition-colors hover:bg-gray-50 dark:hover:bg-gray-700")}
                      >
                        <div className={cn("font-medium", textStyles.body)}>2024 = MMXXIV</div>
                        <div className={cn("text-xs", textStyles.muted)}>Current year</div>
                      </button>
                      <button
                        onClick={() => handleQuickExample(444, 'CDXLIV')}
                        className={cn(containerStyles.cardSmall, "text-left p-2 text-sm transition-colors hover:bg-gray-50 dark:hover:bg-gray-700")}
                      >
                        <div className={cn("font-medium", textStyles.body)}>444 = CDXLIV</div>
                        <div className={cn("text-xs", textStyles.muted)}>Subtractive rules</div>
                      </button>
                      <button
                        onClick={() => handleQuickExample(3999, 'MMMCMXCIX')}
                        className={cn(containerStyles.cardSmall, "text-left p-2 text-sm transition-colors hover:bg-gray-50 dark:hover:bg-gray-700")}
                      >
                        <div className={cn("font-medium", textStyles.body)}>3999 = MMMCMXCIX</div>
                        <div className={cn("text-xs", textStyles.muted)}>Maximum value</div>
                      </button>
                    </div>
                  </div>
                </div>
              </div>

              {/* Results Section */}
              <div className="space-y-6">
                {!arabicError && !romanError && arabicInput.trim() && romanInput.trim() && (
                  <div className={cn(containerStyles.card, "p-6 border-l-4 border-amber-500 dark:border-amber-400")}>
                    <h3 className={cn(textStyles.h3, "mb-4")}>🏛️ Conversion Results</h3>
                    <div className="space-y-4">
                      <div className={cn(statusStyles.success.container, "p-4")}>
                        <div className="flex items-center justify-between">
                          <div>
                            <div className={cn("font-medium", textStyles.body)}>Arabic Number</div>
                            <div className={cn("text-sm", textStyles.muted)}>Modern decimal system</div>
                          </div>
                          <div className={cn("text-2xl font-bold", statusStyles.success.text)}>{arabicResult}</div>
                        </div>
                      </div>

                      <div className={cn(statusStyles.warning.container, "p-4")}>
                        <div className="flex items-center justify-between">
                          <div>
                            <div className={cn("font-medium", textStyles.body)}>Roman Numeral</div>
                            <div className={cn("text-sm", textStyles.muted)}>Ancient Roman system</div>
                          </div>
                          <div className={cn("text-2xl font-bold font-mono", statusStyles.warning.text)}>{romanResult}</div>
                        </div>
                      </div>
                    </div>
                  </div>
                )}

                {/* Roman Numeral Symbols */}
                <div className={cn(statusStyles.info.container, "p-6")}>
                  <h3 className={cn(statusStyles.info.title, "mb-4")}>📜 Roman Numeral Symbols</h3>
                  <div className="space-y-2">
                    {romanNumerals.map((numeral) => (
                      <div key={numeral.symbol} className={cn(containerStyles.cardSmall, "p-3")}>
                        <div className="flex items-center justify-between">
                          <div className="flex items-center space-x-3">
                            <div className={cn("font-mono text-xl font-bold", statusStyles.info.text)}>{numeral.symbol}</div>
                            <div className={cn("text-sm", textStyles.body)}>{numeral.description}</div>
                          </div>
                          <div className={cn("font-bold", textStyles.h4)}>{numeral.value}</div>
                        </div>
                      </div>
                    ))}
                  </div>
                </div>
              </div>
            </div>

            {/* Educational Content */}
            <div className="mt-12 space-y-8">
              {/* Subtractive Rules */}
              <div className={cn(containerStyles.card, "p-8")}>
                <h2 className={cn(textStyles.h2, "mb-6")}>📐 Subtractive Notation Rules</h2>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <div>
                    <h3 className={cn("text-lg font-semibold mb-3", textStyles.h3)}>Valid Subtractive Combinations</h3>
                    <div className="space-y-2">
                      {subtractiveRules.map((rule) => (
                        <div key={rule.symbol} className={cn(statusStyles.warning.container, "p-3")}>
                          <div className="flex items-center justify-between">
                            <div className="flex items-center space-x-3">
                              <div className={cn("font-mono text-lg font-bold", statusStyles.warning.text)}>{rule.symbol}</div>
                              <div className={cn("text-sm", textStyles.body)}>{rule.description}</div>
                            </div>
                            <div className={cn("font-bold", textStyles.h4)}>{rule.value}</div>
                          </div>
                        </div>
                      ))}
                    </div>
                  </div>
                  <div>
                    <h3 className={cn("text-lg font-semibold mb-3", textStyles.h3)}>Subtractive Rules</h3>
                    <ul className={cn("space-y-2", textStyles.bodySmall)}>
                      <li>• Only I, X, and C can be subtracted</li>
                      <li>• I can only be subtracted from V and X</li>
                      <li>• X can only be subtracted from L and C</li>
                      <li>• C can only be subtracted from D and M</li>
                      <li>• Only one smaller numeral can precede a larger one</li>
                      <li>• The smaller numeral must be at least 1/10 of the larger</li>
                    </ul>
                  </div>
                </div>
              </div>

              {/* Roman Numeral History */}
              <div className={cn(containerStyles.card, "p-8")}>
                <h2 className={cn(textStyles.h2, "mb-6")}>🏛️ History of Roman Numerals</h2>
                <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
                  <div>
                    <h3 className={cn("text-lg font-semibold mb-3", textStyles.h3)}>Ancient Origins</h3>
                    <p className={cn("text-sm mb-3", textStyles.bodySmall)}>
                      Roman numerals evolved from Etruscan numerals around the 7th century BC. The system was used
                      throughout the Roman Empire for trade, administration, and monumental inscriptions.
                    </p>
                    <ul className={cn("text-sm space-y-1", textStyles.bodySmall)}>
                      <li>• <strong>7th century BC:</strong> Adopted from Etruscan system</li>
                      <li>• <strong>Roman Empire:</strong> Standard for official documents</li>
                      <li>• <strong>Medieval period:</strong> Used alongside Arabic numerals</li>
                      <li>• <strong>Renaissance:</strong> Continued use in formal contexts</li>
                    </ul>
                  </div>
                  <div>
                    <h3 className={cn("text-lg font-semibold mb-3", textStyles.h3)}>Symbol Evolution</h3>
                    <p className={cn("text-sm mb-3", textStyles.bodySmall)}>
                      The symbols we use today evolved from ancient hand gestures and tally marks. Each symbol
                      has a fascinating origin story rooted in practical counting methods.
                    </p>
                    <ul className={cn("text-sm space-y-1", textStyles.bodySmall)}>
                      <li>• <strong>I:</strong> Single tally mark or finger</li>
                      <li>• <strong>V:</strong> Open hand (5 fingers)</li>
                      <li>• <strong>X:</strong> Two hands crossed (10 fingers)</li>
                      <li>• <strong>C:</strong> From Latin "centum" (hundred)</li>
                      <li>• <strong>M:</strong> From Latin "mille" (thousand)</li>
                    </ul>
                  </div>
                </div>
              </div>

              {/* Modern Applications */}
              <div className={cn(statusStyles.success.container, "p-6")}>
                <h2 className={cn(statusStyles.success.title, "mb-4")}>🌟 Modern Uses of Roman Numerals</h2>
                <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                  <div className={cn(containerStyles.cardSmall, "p-4")}>
                    <h3 className={cn("font-semibold mb-2", textStyles.h4)}>Entertainment</h3>
                    <ul className={cn("text-sm space-y-1", textStyles.bodySmall)}>
                      <li>• Movie sequels (Rocky IV)</li>
                      <li>• Super Bowl numbering</li>
                      <li>• Book chapters</li>
                      <li>• Video game titles</li>
                    </ul>
                  </div>

                  <div className={cn(containerStyles.cardSmall, "p-4")}>
                    <h3 className={cn("font-semibold mb-2", textStyles.h4)}>Formal Documents</h3>
                    <ul className={cn("text-sm space-y-1", textStyles.bodySmall)}>
                      <li>• Copyright dates</li>
                      <li>• Building cornerstones</li>
                      <li>• Legal documents</li>
                      <li>• Academic papers</li>
                    </ul>
                  </div>

                  <div className={cn(containerStyles.cardSmall, "p-4")}>
                    <h3 className={cn("font-semibold mb-2", textStyles.h4)}>Design & Art</h3>
                    <ul className={cn("text-sm space-y-1", textStyles.bodySmall)}>
                      <li>• Clock faces</li>
                      <li>• Architectural elements</li>
                      <li>• Luxury branding</li>
                      <li>• Tattoos and jewelry</li>
                    </ul>
                  </div>
                </div>
              </div>

              {/* Conversion Tips */}
              <div className={cn(statusStyles.purple.container, "p-6")}>
                <h2 className={cn(statusStyles.purple.title, "mb-4")}>💡 Conversion Tips & Tricks</h2>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <div>
                    <h3 className={cn("font-semibold mb-2", textStyles.h4)}>Reading Roman Numerals</h3>
                    <ul className={cn("text-sm space-y-1", statusStyles.purple.text)}>
                      <li>• Read from left to right</li>
                      <li>• Add values when symbols decrease or stay same</li>
                      <li>• Subtract when a smaller symbol precedes larger</li>
                      <li>• Look for subtractive combinations first</li>
                    </ul>
                  </div>
                  <div>
                    <h3 className={cn("font-semibold mb-2", textStyles.h4)}>Writing Roman Numerals</h3>
                    <ul className={cn("text-sm space-y-1", statusStyles.purple.text)}>
                      <li>• Start with largest values first</li>
                      <li>• Use subtractive notation when appropriate</li>
                      <li>• Never repeat a symbol more than 3 times</li>
                      <li>• Use standard combinations (IV not IIII)</li>
                    </ul>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
        <Footer />
      </div>
    </>
  );
}
