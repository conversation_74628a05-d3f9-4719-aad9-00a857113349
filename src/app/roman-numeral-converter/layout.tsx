import { generatePageTitle, generateCanonicalUrl } from '@/lib/utils';
import type { Metadata } from 'next';

export const metadata: Metadata = {
  title: generatePageTitle('Roman Numeral Converter'),
  description: 'Convert between Arabic numbers and Roman numerals. Learn about Roman numeral rules, history, and practical applications in modern times.',
  keywords: 'roman numerals, roman numeral converter, arabic to roman, roman to arabic, ancient numbers, roman numerals rules, roman numerals history',
  robots: 'index, follow',
  alternates: {
    canonical: generateCanonicalUrl('/roman-numeral-converter'),
  },
  openGraph: {
    title: generatePageTitle('Roman Numeral Converter'),
    description: 'Convert between Arabic numbers and Roman numerals. Educational tool for learning Roman numeral system and its history.',
    type: 'website',
    url: generateCanonicalUrl('/roman-numeral-converter'),
  },
  twitter: {
    card: 'summary',
    title: generatePageTitle('Roman Numeral Converter'),
    description: 'Convert between Arabic numbers and Roman numerals. Educational tool for learning Roman numeral system and its history.',
  },
};

export default function RomanNumeralConverterLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  return children;
}
