'use client';

import { useState, useEffect, useCallback, Suspense } from 'react';
import { useRouter, useSearchParams } from 'next/navigation';
import Footer from "@/components/Footer";
import CalculatorHeader from '@/components/CalculatorHeader';
import { containerStyles, textStyles, inputStyles, cn } from '@/components/ui/styles';

interface HourlyToSalaryResult {
  hourlyRate: number;
  annualSalary: number;
  weeklyPay: number;
  monthlyPay: number;
  biweeklyPay: number;
  hoursPerWeek: number;
  weeksPerYear: number;
}

function HourlyToSalaryCalculatorContent() {
  const router = useRouter();
  const searchParams = useSearchParams();
  
  // Form inputs
  const [hourlyRate, setHourlyRate] = useState<string>('25');
  const [hoursPerWeek, setHoursPerWeek] = useState<string>('40');
  const [weeksPerYear, setWeeksPerYear] = useState<string>('52');
  const [currency, setCurrency] = useState<string>('USD');
  
  // Results
  const [result, setResult] = useState<HourlyToSalaryResult>({
    hourlyRate: 25,
    annualSalary: 52000,
    weeklyPay: 1000,
    monthlyPay: 4333.33,
    biweeklyPay: 2000,
    hoursPerWeek: 40,
    weeksPerYear: 52
  });

  // URL state management
  const updateURL = useCallback((params: Record<string, string>) => {
    const newParams = new URLSearchParams(searchParams);
    Object.entries(params).forEach(([key, value]) => {
      if (value && value !== '') {
        newParams.set(key, value);
      } else {
        newParams.delete(key);
      }
    });
    router.push(`?${newParams.toString()}`, { scroll: false });
  }, [router, searchParams]);

  // Initialize from URL
  useEffect(() => {
    const rate = searchParams.get('rate') || '25';
    const hours = searchParams.get('hoursPerWeek') || '40';
    const weeks = searchParams.get('weeksPerYear') || '52';
    const curr = searchParams.get('currency') || 'USD';

    setHourlyRate(rate);
    setHoursPerWeek(hours);
    setWeeksPerYear(weeks);
    setCurrency(curr);
  }, [searchParams]);

  // Calculate results
  const calculateHourlyToSalary = useCallback(() => {
    const rate = parseFloat(hourlyRate) || 0;
    const hours = parseFloat(hoursPerWeek) || 40;
    const weeks = parseFloat(weeksPerYear) || 52;
    
    const annualSalary = rate * hours * weeks;
    const weeklyPay = rate * hours;
    const monthlyPay = annualSalary / 12;
    const biweeklyPay = weeklyPay * 2;
    
    setResult({
      hourlyRate: rate,
      annualSalary,
      weeklyPay,
      monthlyPay,
      biweeklyPay,
      hoursPerWeek: hours,
      weeksPerYear: weeks
    });
  }, [hourlyRate, hoursPerWeek, weeksPerYear]);

  useEffect(() => {
    calculateHourlyToSalary();
  }, [calculateHourlyToSalary]);

  // Format currency
  const formatCurrency = (amount: number): string => {
    const currencyMap: Record<string, string> = {
      USD: 'en-US',
      EUR: 'de-DE',
      GBP: 'en-GB',
      INR: 'en-IN',
      JPY: 'ja-JP',
      CAD: 'en-CA',
      AUD: 'en-AU',
      CHF: 'de-CH',
      CNY: 'zh-CN',
      SGD: 'en-SG',
    };
    
    return new Intl.NumberFormat(currencyMap[currency] || 'en-US', {
      style: 'currency',
      currency: currency,
      minimumFractionDigits: currency === 'JPY' ? 0 : 2,
      maximumFractionDigits: currency === 'JPY' ? 0 : 2,
    }).format(amount);
  };

  return (
    <>
      <div className={containerStyles.page}>
        <div className="container mx-auto px-4 py-8">
          <div className="max-w-4xl mx-auto">
            <CalculatorHeader
              title="Hourly to Salary Calculator"
              description="Convert your hourly wage to annual salary. Calculate your yearly earnings based on your hourly rate, weekly hours, and working weeks per year."
              category="Financial"
              className="text-center mb-12"
            />

            {/* Calculator Input Section */}
            <div className={cn(containerStyles.card, "p-8 mb-8")}>
              <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
                <div className="space-y-6">
                  <h2 className={cn(textStyles.h2, "mb-6")}>Enter Your Hourly Information</h2>
                  
                  <div>
                    <label className={cn(textStyles.label, "block mb-2")}>Currency</label>
                    <select
                      value={currency}
                      onChange={(e) => {
                        setCurrency(e.target.value);
                        updateURL({ currency: e.target.value });
                      }}
                      className={cn(inputStyles.base)}
                    >
                      <option value="USD">USD ($)</option>
                      <option value="EUR">EUR (€)</option>
                      <option value="GBP">GBP (£)</option>
                      <option value="INR">INR (₹)</option>
                      <option value="JPY">JPY (¥)</option>
                      <option value="CAD">CAD ($)</option>
                      <option value="AUD">AUD ($)</option>
                      <option value="CHF">CHF (₣)</option>
                      <option value="CNY">CNY (¥)</option>
                      <option value="SGD">SGD ($)</option>
                    </select>
                  </div>
                  
                  <div>
                    <label className={cn(textStyles.label, "block mb-2")}>Hourly Rate</label>
                    <div className="relative">
                      <span className="absolute left-3 top-1/2 -translate-y-1/2 text-gray-500">
                        {currency === 'EUR' ? '€' : currency === 'GBP' ? '£' : currency === 'INR' ? '₹' : currency === 'JPY' ? '¥' : currency === 'CNY' ? '¥' : currency === 'CHF' ? '₣' : '$'}
                      </span>
                      <input
                        type="number"
                        value={hourlyRate}
                        onChange={(e) => {
                          setHourlyRate(e.target.value);
                          updateURL({ rate: e.target.value });
                        }}
                        className={cn(inputStyles.base, "pl-8")}
                        placeholder="25.00"
                        step="0.01"
                        min="0"
                      />
                    </div>
                    <p className={cn("text-xs mt-1", textStyles.muted)}>
                      Your hourly wage before taxes and deductions
                    </p>
                  </div>
                  
                  <div>
                    <label className={cn(textStyles.label, "block mb-2")}>Hours per Week</label>
                    <input
                      type="number"
                      value={hoursPerWeek}
                      onChange={(e) => {
                        setHoursPerWeek(e.target.value);
                        updateURL({ hoursPerWeek: e.target.value });
                      }}
                      className={cn(inputStyles.base)}
                      placeholder="40"
                      step="0.5"
                      min="0"
                    />
                    <p className={cn("text-xs mt-1", textStyles.muted)}>
                      Number of hours you work per week (typically 40)
                    </p>
                  </div>
                  
                  <div>
                    <label className={cn(textStyles.label, "block mb-2")}>Weeks per Year</label>
                    <input
                      type="number"
                      value={weeksPerYear}
                      onChange={(e) => {
                        setWeeksPerYear(e.target.value);
                        updateURL({ weeksPerYear: e.target.value });
                      }}
                      className={cn(inputStyles.base)}
                      placeholder="52"
                      step="1"
                      min="1"
                      max="52"
                    />
                    <p className={cn("text-xs mt-1", textStyles.muted)}>
                      Working weeks per year (52 minus vacation weeks)
                    </p>
                  </div>
                </div>
                
                <div>
                  <h2 className={cn(textStyles.h2, "mb-6")}>Annual Salary Breakdown</h2>
                  <div className="space-y-4">
                    <div className={cn("p-4 rounded-lg", "bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800")}>
                      <div className="flex justify-between items-center">
                        <span className={textStyles.body}>Annual Salary</span>
                        <span className={cn("font-bold text-2xl", "text-blue-600 dark:text-blue-400")}>{formatCurrency(result.annualSalary)}</span>
                      </div>
                    </div>
                    
                    <div className={cn("p-4 rounded-lg", "bg-gray-50 dark:bg-gray-800")}>
                      <div className="flex justify-between items-center">
                        <span className={textStyles.body}>Weekly Pay</span>
                        <span className={cn("font-medium", textStyles.body)}>{formatCurrency(result.weeklyPay)}</span>
                      </div>
                    </div>
                    
                    <div className={cn("p-4 rounded-lg", "bg-gray-50 dark:bg-gray-800")}>
                      <div className="flex justify-between items-center">
                        <span className={textStyles.body}>Biweekly Pay</span>
                        <span className={cn("font-medium", textStyles.body)}>{formatCurrency(result.biweeklyPay)}</span>
                      </div>
                    </div>
                    
                    <div className={cn("p-4 rounded-lg", "bg-gray-50 dark:bg-gray-800")}>
                      <div className="flex justify-between items-center">
                        <span className={textStyles.body}>Monthly Pay</span>
                        <span className={cn("font-medium", textStyles.body)}>{formatCurrency(result.monthlyPay)}</span>
                      </div>
                    </div>
                    
                    <div className={cn("p-4 rounded-lg", "bg-green-50 dark:bg-green-900/20 border border-green-200 dark:border-green-800")}>
                      <div className="flex justify-between items-center">
                        <span className={cn("font-bold", textStyles.body)}>Hourly Rate</span>
                        <span className={cn("font-bold", textStyles.body)}>{formatCurrency(result.hourlyRate)}</span>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>

            {/* Information Section */}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-8 mb-8">
              <div className={cn(containerStyles.card, "p-6")}>
                <h3 className={cn(textStyles.h3, "mb-4")}>Hourly to Salary Conversion</h3>
                <ul className={cn("space-y-3 text-sm", textStyles.body)}>
                  <li className="flex items-start space-x-2">
                    <span className="text-blue-500 mt-1">•</span>
                    <span><strong>Formula:</strong> Hourly Rate × Hours/Week × Weeks/Year</span>
                  </li>
                  <li className="flex items-start space-x-2">
                    <span className="text-blue-500 mt-1">•</span>
                    <span><strong>Standard Year:</strong> 2,080 hours (40 hours × 52 weeks)</span>
                  </li>
                  <li className="flex items-start space-x-2">
                    <span className="text-blue-500 mt-1">•</span>
                    <span><strong>Part-Time:</strong> Adjust hours per week accordingly</span>
                  </li>
                  <li className="flex items-start space-x-2">
                    <span className="text-blue-500 mt-1">•</span>
                    <span><strong>Gross Income:</strong> Before taxes and deductions</span>
                  </li>
                </ul>
              </div>

              <div className={cn(containerStyles.card, "p-6")}>
                <h3 className={cn(textStyles.h3, "mb-4")}>Planning Considerations</h3>
                <ul className={cn("space-y-3 text-sm", textStyles.body)}>
                  <li className="flex items-start space-x-2">
                    <span className="text-green-500 mt-1">•</span>
                    <span><strong>Vacation Time:</strong> May reduce total working weeks</span>
                  </li>
                  <li className="flex items-start space-x-2">
                    <span className="text-green-500 mt-1">•</span>
                    <span><strong>Overtime:</strong> Additional pay for hours over 40/week</span>
                  </li>
                  <li className="flex items-start space-x-2">
                    <span className="text-green-500 mt-1">•</span>
                    <span><strong>Benefits:</strong> Health insurance, retirement contributions</span>
                  </li>
                  <li className="flex items-start space-x-2">
                    <span className="text-green-500 mt-1">•</span>
                    <span><strong>Job Security:</strong> Hourly vs salary stability differences</span>
                  </li>
                </ul>
              </div>
            </div>

            {/* Examples Section */}
            <div className={cn(containerStyles.card, "p-6 mb-8")}>
              <h3 className={cn(textStyles.h3, "mb-4")}>Hourly to Salary Examples</h3>
              <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                <div className="text-center">
                  <div className="text-lg font-bold text-blue-600 dark:text-blue-400 mb-2">$15/hr → $31,200</div>
                  <div className="text-sm font-medium mb-1">Minimum Wage Plus</div>
                  <div className="text-xs text-gray-600 dark:text-gray-400">40 hrs/week, 52 weeks/year</div>
                </div>
                <div className="text-center">
                  <div className="text-lg font-bold text-green-600 dark:text-green-400 mb-2">$25/hr → $52,000</div>
                  <div className="text-sm font-medium mb-1">Skilled Worker</div>
                  <div className="text-xs text-gray-600 dark:text-gray-400">40 hrs/week, 52 weeks/year</div>
                </div>
                <div className="text-center">
                  <div className="text-lg font-bold text-purple-600 dark:text-purple-400 mb-2">$35/hr → $72,800</div>
                  <div className="text-sm font-medium mb-1">Professional</div>
                  <div className="text-xs text-gray-600 dark:text-gray-400">40 hrs/week, 52 weeks/year</div>
                </div>
              </div>
            </div>

            {/* Comparison Table */}
            <div className={cn(containerStyles.card, "p-6 mb-8")}>
              <h3 className={cn(textStyles.h3, "mb-4")}>Hourly vs Salary Comparison</h3>
              <div className="overflow-x-auto">
                <table className="w-full text-sm">
                  <thead>
                    <tr className={cn("border-b", "border-gray-200 dark:border-gray-700")}>
                      <th className={cn("text-left p-3", textStyles.body)}>Aspect</th>
                      <th className={cn("text-left p-3", textStyles.body)}>Hourly</th>
                      <th className={cn("text-left p-3", textStyles.body)}>Salary</th>
                    </tr>
                  </thead>
                  <tbody>
                    <tr className={cn("border-b", "border-gray-100 dark:border-gray-800")}>
                      <td className={cn("p-3 font-medium", textStyles.body)}>Overtime Pay</td>
                      <td className={cn("p-3", textStyles.body)}>✓ Time and a half</td>
                      <td className={cn("p-3", textStyles.body)}>✗ Usually exempt</td>
                    </tr>
                    <tr className={cn("border-b", "border-gray-100 dark:border-gray-800")}>
                      <td className={cn("p-3 font-medium", textStyles.body)}>Benefits</td>
                      <td className={cn("p-3", textStyles.body)}>Varies by employer</td>
                      <td className={cn("p-3", textStyles.body)}>✓ Usually included</td>
                    </tr>
                    <tr className={cn("border-b", "border-gray-100 dark:border-gray-800")}>
                      <td className={cn("p-3 font-medium", textStyles.body)}>Job Security</td>
                      <td className={cn("p-3", textStyles.body)}>Less predictable</td>
                      <td className={cn("p-3", textStyles.body)}>✓ More stable</td>
                    </tr>
                    <tr>
                      <td className={cn("p-3 font-medium", textStyles.body)}>Flexibility</td>
                      <td className={cn("p-3", textStyles.body)}>✓ More schedule control</td>
                      <td className={cn("p-3", textStyles.body)}>Fixed schedule</td>
                    </tr>
                  </tbody>
                </table>
              </div>
            </div>

          </div>
        </div>
      </div>
      <Footer />
    </>
  );
}

export default function HourlyToSalaryCalculator() {
  return (
    <Suspense fallback={<div>Loading...</div>}>
      <HourlyToSalaryCalculatorContent />
    </Suspense>
  );
}