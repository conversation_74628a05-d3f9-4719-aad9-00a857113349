import type { Metadata } from 'next'

export const metadata: Metadata = {
  title: 'Hourly to Salary Calculator | Convert Hourly Rate to Annual Salary',
  description: 'Convert your hourly wage to annual salary. Calculate your yearly earnings based on your hourly rate, weekly hours, and working weeks per year.',
  keywords: 'hourly to salary calculator, hourly rate to annual salary, hourly wage converter, annual salary calculator, hourly to yearly pay, wage conversion',
  openGraph: {
    title: 'Hourly to Salary Calculator | Convert Hourly Rate to Annual Salary',
    description: 'Convert your hourly wage to annual salary. Calculate your yearly earnings based on your hourly rate, weekly hours, and working weeks per year.',
    type: 'website',
  },
  alternates: {
    canonical: '/hourly-to-salary-calculator',
  },
}

export default function HourlyToSalaryCalculatorLayout({
  children,
}: {
  children: React.ReactNode
}) {
  return <>{children}</>
}