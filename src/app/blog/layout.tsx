import { generatePageTitle, generateCanonicalUrl } from '@/lib/utils';

export const metadata = {
  title: generatePageTitle("Blog"),
  description: "Read our latest articles about calculators, health, fitness, and financial planning. Expert insights and tips for using our calculator tools effectively.",
  keywords: "calculator blog, health articles, fitness tips, financial planning, BMI, calorie calculation, construction calculator",
  robots: "index, follow",
  alternates: {
    canonical: generateCanonicalUrl("/blog"),
  },
};

export default function BlogLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  return children;
}