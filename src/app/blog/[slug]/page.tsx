import { blogPosts } from '@/data/blog';
import { notFound } from 'next/navigation';
import BlogLayout from '@/components/BlogLayout';
import { blogContentComponents } from '@/content/blog';

interface BlogArticlePageProps {
  params: Promise<{ slug: string }>;
}

export default async function BlogArticlePage({ params }: BlogArticlePageProps) {
  const { slug } = await params;
  
  // 检查文章是否存在
  if (!blogPosts[slug]) {
    notFound();
  }

  // 使用新的统一布局系统
  const ContentComponent = blogContentComponents[slug as keyof typeof blogContentComponents];
  if (ContentComponent) {
    return (
      <BlogLayout slug={slug}>
        <ContentComponent />
      </BlogLayout>
    );
  }

  // 如果没有找到对应的组件，返回404
  notFound();
}

// 生成静态路径
export async function generateStaticParams() {
  return Object.keys(blogPosts).map((slug) => ({
    slug,
  }));
}