import { generateBlogMetadata } from '@/lib/utils';
import { notFound } from 'next/navigation';

interface BlogArticleLayoutProps {
  children: React.ReactNode;
  params: Promise<{ slug: string }>;
}

export async function generateMetadata({ params }: { params: Promise<{ slug: string }> }) {
  const { slug } = await params;
  const metadata = generateBlogMetadata(slug);
  
  if (!metadata) {
    notFound();
  }
  
  return metadata;
}

export default function BlogArticleLayout({ children }: BlogArticleLayoutProps) {
  return children;
}