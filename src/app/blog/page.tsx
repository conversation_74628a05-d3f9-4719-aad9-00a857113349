import Link from 'next/link';
import Footer from '@/components/Footer';
import PageHeader from '@/components/PageHeader';
import { blogPosts } from '@/data/blog';
import { containerStyles } from '@/components/ui/styles';

// Convert the blog posts object to an array and sort by date (newest first)
const blogPostsArray = Object.values(blogPosts)
  .map((post, index) => ({
    id: index + 1,
    title: post.title,
    excerpt: post.description,
    date: post.date,
    readTime: post.readTime,
    category: post.category,
    slug: post.slug,
    relatedCalculators: post.relatedCalculators || []
  }))
  .sort((a, b) => new Date(b.date).getTime() - new Date(a.date).getTime());

export default function Blog() {
  return (
    <>
      <div className={containerStyles.page}>
        <div className="container mx-auto px-4">
          <div className="max-w-4xl mx-auto">
            <PageHeader
              title="Blog"
              description="Expert insights, practical guides, and the latest updates from the world of calculations and tools."
              className="text-center mb-12"
            />

            {/* Blog Posts Grid */}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-8 mb-12">
              {blogPostsArray.map((post) => (
                <article
                  key={post.id}
                  className="bg-white dark:bg-gray-800 rounded-lg shadow-md hover:shadow-lg dark:shadow-gray-700/50 transition-all duration-300 overflow-hidden group border border-gray-200 dark:border-gray-700"
                >
                  <div className="p-6">
                    {/* Category and Date */}
                    <div className="flex items-center justify-between mb-3">
                      <span className="inline-block px-3 py-1 bg-blue-100 dark:bg-blue-900/50 text-blue-800 dark:text-blue-300 text-xs font-medium rounded-full">
                        {post.category}
                      </span>
                      <span className="text-sm text-gray-500 dark:text-gray-400">{post.readTime}</span>
                    </div>

                    {/* Title */}
                    <h2 className="text-xl font-bold text-gray-800 dark:text-white mb-3 group-hover:text-blue-600 dark:group-hover:text-blue-400 transition-colors duration-300">
                      <Link href={`/blog/${post.slug}`}>
                        {post.title}
                      </Link>
                    </h2>

                    {/* Excerpt */}
                    <p className="text-gray-600 dark:text-gray-300 mb-4 line-clamp-3">
                      {post.excerpt}
                    </p>

                    {/* Meta */}
                    <div className="flex items-center justify-between">
                      <span className="text-sm text-gray-500 dark:text-gray-400">
                        {new Date(post.date).toLocaleDateString('en-US', {
                          year: 'numeric',
                          month: 'long',
                          day: 'numeric'
                        })}
                      </span>
                      <Link
                        href={`/blog/${post.slug}`}
                        className="text-blue-600 dark:text-blue-400 hover:text-blue-700 dark:hover:text-blue-300 font-medium text-sm transition-colors duration-300"
                      >
                        Read More →
                      </Link>
                    </div>
                  </div>
                </article>
              ))}
            </div>

            {/* Total Posts Info */}
            <div className="mt-8 text-center">
              <p className="text-gray-600 dark:text-gray-400">
                Showing {blogPostsArray.length} articles • Updated regularly with new insights
              </p>
            </div>
          </div>
        </div>
      </div>
      <Footer />
    </>
  );
}