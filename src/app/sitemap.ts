import { MetadataRoute } from "next";
import { SITE_CONFIG } from "@/lib/utils";
import { calculators } from "@/data/calculators";
import { blogPosts } from "@/data/blog";

export const dynamic = 'force-static';

export default function sitemap(): MetadataRoute.Sitemap {
  const baseUrl = SITE_CONFIG.baseUrl;
  
  // Generate calculator pages dynamically from the calculators array
  const calculatorPages = calculators.map((calculator) => ({
    url: `${baseUrl}${calculator.href}/`,
    lastModified: new Date(),
    changeFrequency: "monthly" as const,
    priority: calculator.priority || 0.8,
  }));

  // Generate blog article pages dynamically from the blog posts array
  const blogPages = Object.values(blogPosts).map((post) => ({
    url: `${baseUrl}/blog/${post.slug}/`,
    lastModified: new Date(post.date),
    changeFrequency: "monthly" as const,
    priority: post.priority || 0.6,
  }));

  // Static pages
  const staticPages = [
    {
      url: `${baseUrl}/`,
      lastModified: new Date(),
      changeFrequency: "weekly" as const,
      priority: 1,
    },
    {
      url: `${baseUrl}/about/`,
      lastModified: new Date(),
      changeFrequency: "monthly" as const,
      priority: 0.8,
    },
    {
      url: `${baseUrl}/blog/`,
      lastModified: new Date(),
      changeFrequency: "weekly" as const,
      priority: 0.7,
    },
    {
      url: `${baseUrl}/privacy-policy/`,
      lastModified: new Date(),
      changeFrequency: "yearly" as const,
      priority: 0.3,
    },
    {
      url: `${baseUrl}/terms-of-service/`,
      lastModified: new Date(),
      changeFrequency: "yearly" as const,
      priority: 0.3,
    },
  ];

  return [...staticPages, ...calculatorPages, ...blogPages];
}