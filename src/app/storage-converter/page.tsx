'use client';

import { useState, useEffect, useCallback } from 'react';
import Footer from "@/components/Footer";
import CalculatorHeader from '@/components/CalculatorHeader';
import { containerStyles, textStyles, inputStyles, statusStyles, cn } from '@/components/ui/styles';

interface ConversionResults {
  bytes: number;
  kilobytes: number;
  megabytes: number;
  gigabytes: number;
  terabytes: number;
  petabytes: number;
  kibibytes: number;
  mebibytes: number;
  gibibytes: number;
  tebibytes: number;
  pebibytes: number;
}

export default function StorageConverter() {
  const [inputValue, setInputValue] = useState<string>('1');
  const [fromUnit, setFromUnit] = useState<string>('megabytes');
  const [conversionType, setConversionType] = useState<string>('decimal'); // decimal or binary
  const [, setResults] = useState<ConversionResults>({
    bytes: 1000000,
    kilobytes: 1000,
    megabytes: 1,
    gigabytes: 0.001,
    terabytes: 0.000001,
    petabytes: 0.000000001,
    kibibytes: 976.5625,
    mebibytes: 0.953674,
    gibibytes: 0.000931,
    tebibytes: 0.00000091,
    pebibytes: 0.00000000089
  });

  const convertToAllUnits = useCallback((value: number, unit: string): ConversionResults => {
    // Conversion factors to bytes
    const decimalFactors = {
      bytes: 1,
      kilobytes: 1000,
      megabytes: 1000000,
      gigabytes: 1000000000,
      terabytes: 1000000000000,
      petabytes: 1000000000000000
    };

    const binaryFactors = {
      bytes: 1,
      kibibytes: 1024,
      mebibytes: 1024 * 1024,
      gibibytes: 1024 * 1024 * 1024,
      tebibytes: 1024 * 1024 * 1024 * 1024,
      pebibytes: 1024 * 1024 * 1024 * 1024 * 1024
    };

    let totalBytes: number;

    // Convert input to bytes first
    if (unit in decimalFactors) {
      totalBytes = value * decimalFactors[unit as keyof typeof decimalFactors];
    } else if (unit in binaryFactors) {
      totalBytes = value * binaryFactors[unit as keyof typeof binaryFactors];
    } else {
      totalBytes = value;
    }

    // Convert from bytes to all units
    return {
      // Decimal (SI) units
      bytes: totalBytes,
      kilobytes: totalBytes / decimalFactors.kilobytes,
      megabytes: totalBytes / decimalFactors.megabytes,
      gigabytes: totalBytes / decimalFactors.gigabytes,
      terabytes: totalBytes / decimalFactors.terabytes,
      petabytes: totalBytes / decimalFactors.petabytes,
      // Binary (IEC) units
      kibibytes: totalBytes / binaryFactors.kibibytes,
      mebibytes: totalBytes / binaryFactors.mebibytes,
      gibibytes: totalBytes / binaryFactors.gibibytes,
      tebibytes: totalBytes / binaryFactors.tebibytes,
      pebibytes: totalBytes / binaryFactors.pebibytes
    };
  }, []);

  const handleInputChange = (value: string) => {
    setInputValue(value);
    const numValue = parseFloat(value) || 0;
    setResults(convertToAllUnits(numValue, fromUnit));
  };

  const handleUnitChange = (unit: string) => {
    setFromUnit(unit);
    const numValue = parseFloat(inputValue) || 0;
    setResults(convertToAllUnits(numValue, unit));
  };

  useEffect(() => {
    const numValue = parseFloat(inputValue) || 0;
    setResults(convertToAllUnits(numValue, fromUnit));
  }, [inputValue, fromUnit, convertToAllUnits]);

  const formatNumber = (num: number): string => {
    if (num === 0) return '0';
    if (num < 0.000001) return num.toExponential(3);
    if (num < 0.001) return num.toFixed(9).replace(/\.?0+$/, '');
    if (num < 1) return num.toFixed(6).replace(/\.?0+$/, '');
    if (num < 1000) return num.toFixed(3).replace(/\.?0+$/, '');
    return num.toLocaleString('en-US', { maximumFractionDigits: 2 });
  };

  const decimalUnits = [
    { value: 'bytes', label: 'Bytes', abbr: 'B', icon: '📁' },
    { value: 'kilobytes', label: 'Kilobytes', abbr: 'KB', icon: '📄' },
    { value: 'megabytes', label: 'Megabytes', abbr: 'MB', icon: '💾' },
    { value: 'gigabytes', label: 'Gigabytes', abbr: 'GB', icon: '💿' },
    { value: 'terabytes', label: 'Terabytes', abbr: 'TB', icon: '🗄️' },
    { value: 'petabytes', label: 'Petabytes', abbr: 'PB', icon: '🏢' }
  ];

  const binaryUnits = [
    { value: 'bytes', label: 'Bytes', abbr: 'B', icon: '📁' },
    { value: 'kibibytes', label: 'Kibibytes', abbr: 'KiB', icon: '📄' },
    { value: 'mebibytes', label: 'Mebibytes', abbr: 'MiB', icon: '💾' },
    { value: 'gibibytes', label: 'Gibibytes', abbr: 'GiB', icon: '💿' },
    { value: 'tebibytes', label: 'Tebibytes', abbr: 'TiB', icon: '🗄️' },
    { value: 'pebibytes', label: 'Pebibytes', abbr: 'PiB', icon: '🏢' }
  ];

  const currentUnits = conversionType === 'decimal' ? decimalUnits : binaryUnits;

  return (
    <>
      <div className={containerStyles.page}>
        <div className="container mx-auto px-4 py-8">
          <div className="max-w-6xl mx-auto">
            <CalculatorHeader
              title="Storage Converter"
              description="Convert between storage units: bytes, KB, MB, GB, TB and binary equivalents (KiB, MiB, GiB, TiB). Perfect for understanding file sizes and storage capacities."
              category="Other"
              className="text-center mb-12"
            />

            {/* Conversion Type Toggle */}
            <div className={cn(containerStyles.card, "p-6 mb-8")}>
              <div className="flex flex-col sm:flex-row items-center justify-center space-y-4 sm:space-y-0 sm:space-x-4">
                <span className={cn("text-sm font-semibold", textStyles.label)}>Conversion Standard:</span>
                <div className="flex rounded-lg overflow-hidden border border-gray-300 dark:border-gray-600 bg-gray-50 dark:bg-gray-700">
                  <button
                    onClick={() => setConversionType('decimal')}
                    className={cn(
                      "px-6 py-3 text-sm font-medium transition-all duration-200",
                      conversionType === 'decimal' 
                        ? "bg-blue-500 dark:bg-blue-600 text-white shadow-md"
                        : cn("bg-white dark:bg-gray-600 hover:bg-gray-50 dark:hover:bg-gray-500", textStyles.body)
                    )}
                  >
                    Decimal (SI) - 1000 base
                  </button>
                  <button
                    onClick={() => setConversionType('binary')}
                    className={cn(
                      "px-6 py-3 text-sm font-medium transition-all duration-200 border-l border-gray-300 dark:border-gray-600",
                      conversionType === 'binary' 
                        ? "bg-blue-500 dark:bg-blue-600 text-white shadow-md"
                        : cn("bg-white dark:bg-gray-600 hover:bg-gray-50 dark:hover:bg-gray-500", textStyles.body)
                    )}
                  >
                    Binary (IEC) - 1024 base
                  </button>
                </div>
              </div>
            </div>

            {/* Main Calculator */}
            <div className={cn(containerStyles.card, "p-8 mb-8")}>
              <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
                {/* Input Section */}
                <div>
                  <h2 className={cn(textStyles.h2, "mb-6")}>Convert From</h2>
                  
                  <div className="space-y-6">
                    <div>
                      <label className={cn(textStyles.label, "block mb-2")}>
                        Amount
                      </label>
                      <input
                        type="number"
                        value={inputValue}
                        onChange={(e) => handleInputChange(e.target.value)}
                        className={cn(inputStyles.base, "text-lg")}
                        placeholder="Enter amount"
                        step="any"
                      />
                    </div>

                    <div>
                      <label className={cn(textStyles.label, "block mb-3")}>
                        Select Unit
                      </label>
                      <div className="grid grid-cols-3 gap-2">
                        {currentUnits.map((unit) => (
                          <button
                            key={unit.value}
                            onClick={() => handleUnitChange(unit.value)}
                                                    className={cn(
                              "p-3 rounded-lg border transition-colors duration-200 text-center",
                              fromUnit === unit.value
                                ? "bg-blue-500 dark:bg-blue-600 text-white border-blue-500 dark:border-blue-600 shadow-lg"
                                : cn(
                                    "border-gray-300 dark:border-gray-600 hover:bg-gray-50 dark:hover:bg-gray-600",
                                    "bg-white dark:bg-gray-700",
                                    textStyles.body
                                  )
                            )}
                          >
                            <div className="font-medium text-sm">{unit.abbr}</div>
                          </button>
                        ))}
                      </div>
                    </div>
                  </div>
                </div>

                {/* Results Section */}
                <div>
                  <h2 className={cn(textStyles.h2, "mb-6")}>Conversion Results</h2>
                  
                  {inputValue && fromUnit ? (
                    <div className="space-y-4">
                      {currentUnits.map((unit) => {
                        const value = convertToAllUnits(parseFloat(inputValue), fromUnit)[unit.value as keyof ConversionResults];
                        return (
                          <div key={unit.value} className={cn(containerStyles.cardSmall, "p-4")}>
                            <div className="flex justify-between items-center">
                              <div>
                                <div className={cn("font-medium", textStyles.body)}>{unit.abbr}</div>
                                <div className={cn("text-xs", textStyles.muted)}>{unit.label}</div>
                              </div>
                              <div className="text-right">
                                <div className={cn("text-lg font-bold", textStyles.h3)}>
                                  {formatNumber(value)}
                                </div>
                              </div>
                            </div>
                          </div>
                        );
                      })}
                    </div>
                  ) : (
                    <div className="text-center py-8">
                      <p className={textStyles.muted}>Enter amount and select unit to see conversions</p>
                    </div>
                  )}
                </div>
              </div>
            </div>

            {/* Quick Examples */}
            <div className={cn(statusStyles.info.container, "mb-8")}>
              <h3 className={cn(statusStyles.info.title, "mb-4")}>Quick Examples</h3>
              <div className="space-y-3">
                {[
                  { value: '1 GB', description: 'HD Movie' },
                  { value: '4.7 GB', description: 'DVD Capacity' },
                  { value: '128 GB', description: 'Smartphone Storage' },
                  { value: '1 TB', description: 'Hard Drive' }
                ].map((example, index) => (
                  <div key={index} className={cn(
                    "flex justify-between items-center p-3 rounded-lg border",
                    "bg-white dark:bg-gray-700 border-gray-200 dark:border-gray-600"
                  )}>
                    <div className={cn("font-medium", textStyles.body)}>{example.value}</div>
                    <div className={cn("text-sm", textStyles.bodySmall)}>{example.description}</div>
                  </div>
                ))}
              </div>
            </div>

            {/* Conversion Information */}
            <div className={cn(containerStyles.card, "p-6 mb-8")}>
              <h3 className={cn(textStyles.h3, "mb-4")}>Storage Standards Explained</h3>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                  <h4 className={cn("font-medium mb-3", textStyles.h4)}>Decimal (SI) Standard</h4>
                  <ul className={cn("space-y-2 text-sm", textStyles.body)}>
                    <li>• 1 KB = 1,000 bytes</li>
                    <li>• 1 MB = 1,000 KB</li>
                    <li>• 1 GB = 1,000 MB</li>
                    <li>• 1 TB = 1,000 GB</li>
                    <li>• Used by storage manufacturers</li>
                  </ul>
                </div>
                <div>
                  <h4 className={cn("font-medium mb-3", textStyles.h4)}>Binary (IEC) Standard</h4>
                  <ul className={cn("space-y-2 text-sm", textStyles.body)}>
                    <li>• 1 KiB = 1,024 bytes</li>
                    <li>• 1 MiB = 1,024 KiB</li>
                    <li>• 1 GiB = 1,024 MiB</li>
                    <li>• 1 TiB = 1,024 GiB</li>
                    <li>• Used by operating systems</li>
                  </ul>
                </div>
              </div>
            </div>

            {/* Common File Sizes Reference */}
            <div className={cn(containerStyles.card, "p-6 mb-8")}>
              <h3 className={cn(textStyles.h3, "mb-4")}>Common File Sizes</h3>
              <div className="space-y-3">
                {[
                  { type: 'Text Document', size: '1-100 KB' },
                  { type: 'Digital Photo', size: '1-10 MB' },
                  { type: 'Music Song (MP3)', size: '3-8 MB' },
                  { type: 'HD Video (1 hour)', size: '1-4 GB' },
                  { type: '4K Video (1 hour)', size: '7-18 GB' },
                  { type: 'PC Game', size: '20-100 GB' }
                ].map((file, index) => (
                  <div key={index} className={cn(
                    "flex justify-between items-center p-3 rounded-lg border",
                    "bg-white dark:bg-gray-700 border-gray-200 dark:border-gray-600"
                  )}>
                    <div className={cn("font-medium", textStyles.body)}>{file.type}</div>
                    <div className={cn("text-sm", textStyles.bodySmall)}>{file.size}</div>
                  </div>
                ))}
              </div>
            </div>

            {/* Note about standards */}
            <div className={cn(statusStyles.warning.container, "mb-8")}>
              <div className="flex items-start space-x-3">
                <span className="text-yellow-600 dark:text-yellow-400 text-2xl">ℹ️</span>
                <div>
                  <h4 className={cn("font-medium mb-2", statusStyles.warning.title)}>Why Two Standards?</h4>
                  <p className={cn("text-sm leading-relaxed", statusStyles.warning.text)}>
                    The decimal system is used by manufacturers for marketing, while the binary system is used by operating systems. This is why a &quot;1TB&quot; drive shows as ~931GB in your computer - it&apos;s the same amount of space measured differently.
                  </p>
                </div>
              </div>
            </div>

          </div>
        </div>
      </div>
      <Footer />
    </>
  );
}