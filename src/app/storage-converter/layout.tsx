import { generatePageTitle, generateCanonicalUrl } from '@/lib/utils';
import type { Metadata } from 'next';

export const metadata: Metadata = {
  title: generatePageTitle('Storage Unit Converter'),
  description: 'Convert between storage units: bytes, KB, MB, GB, TB, PB and binary equivalents (KiB, MiB, GiB). Free online storage converter for file sizes and disk space calculations.',
  keywords: 'storage converter, file size converter, bytes to GB, MB to KB, storage unit calculator, disk space converter, binary decimal storage, KiB MiB GiB converter',
  robots: 'index, follow',
  alternates: {
    canonical: generateCanonicalUrl('/storage-converter'),
  },
  openGraph: {
    title: generatePageTitle('Storage Unit Converter'),
    description: 'Convert between storage units including bytes, KB, MB, GB, TB and binary equivalents. Perfect for file size and disk space calculations.',
    type: 'website',
    url: generateCanonicalUrl('/storage-converter'),
  },
  twitter: {
    card: 'summary',
    title: generatePageTitle('Storage Unit Converter'),
    description: 'Convert between storage units including bytes, KB, MB, GB, TB and binary equivalents. Perfect for file size and disk space calculations.',
  },
};

export default function StorageConverterLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  return children;
}