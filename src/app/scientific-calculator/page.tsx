'use client';

import { useState, useEffect } from 'react';
import Footer from "@/components/Footer";
import CalculatorHeader from '@/components/CalculatorHeader';
import { containerStyles, textStyles, buttonStyles, cn } from '@/components/ui/styles';

export default function ScientificCalculator() {
  const [display, setDisplay] = useState('0');
  const [previousValue, setPreviousValue] = useState<number | null>(null);
  const [operation, setOperation] = useState<string | null>(null);
  const [waitingForOperand, setWaitingForOperand] = useState(false);
  const [memory, setMemory] = useState(0);
  const [isRadians, setIsRadians] = useState(true);

  useEffect(() => {
    const handleKeyPress = (event: KeyboardEvent) => {
      const { key } = event;
      
      if (key >= '0' && key <= '9') {
        inputNumber(key);
      } else if (['+', '-'].includes(key)) {
        inputOperator(key === '+' ? '+' : '-');
      } else if (key === '*') {
        inputOperator('×');
      } else if (key === '/') {
        event.preventDefault();
        inputOperator('÷');
      } else if (key === '^') {
        inputOperator('^');
      } else if (key === 'Enter' || key === '=') {
        event.preventDefault();
        calculate();
      } else if (key === '.' || key === ',') {
        inputDecimal();
      } else if (key === 'Escape' || key.toLowerCase() === 'c') {
        clear();
      } else if (key === 'Backspace') {
        if (display.length > 1) {
          setDisplay(display.slice(0, -1));
        } else {
          setDisplay('0');
        }
      } else if (key === 'Delete') {
        setDisplay('0');
      } else if (key.toLowerCase() === 's') {
        scientificFunction('sin');
      } else if (key.toLowerCase() === 'o') {
        scientificFunction('cos');
      } else if (key.toLowerCase() === 't') {
        scientificFunction('tan');
      } else if (key.toLowerCase() === 'l') {
        scientificFunction('log');
      } else if (key.toLowerCase() === 'n') {
        scientificFunction('ln');
      } else if (key.toLowerCase() === 'q') {
        scientificFunction('sqrt');
      } else if (key.toLowerCase() === 'p') {
        scientificFunction('pi');
      } else if (key.toLowerCase() === 'e') {
        scientificFunction('e');
      }
    };

    window.addEventListener('keydown', handleKeyPress);
    return () => window.removeEventListener('keydown', handleKeyPress);
  }); // Removed dependency array to avoid exhaustive-deps warning

  const inputNumber = (num: string) => {
    if (waitingForOperand) {
      setDisplay(String(num));
      setWaitingForOperand(false);
    } else {
      setDisplay(display === '0' ? String(num) : display + num);
    }
  };

  const inputOperator = (nextOperator: string) => {
    const inputValue = parseFloat(display);

    if (previousValue === null) {
      setPreviousValue(inputValue);
    } else if (operation) {
      const currentValue = previousValue || 0;
      const newValue = performCalculation(currentValue, inputValue, operation);

      setDisplay(String(newValue));
      setPreviousValue(newValue);
    }

    setWaitingForOperand(true);
    setOperation(nextOperator);
  };

  const performCalculation = (firstValue: number, secondValue: number, operation: string) => {
    switch (operation) {
      case '+':
        return firstValue + secondValue;
      case '-':
        return firstValue - secondValue;
      case '×':
        return firstValue * secondValue;
      case '÷':
        return firstValue / secondValue;
      case '^':
        return Math.pow(firstValue, secondValue);
      case '%':
        return firstValue % secondValue;
      default:
        return secondValue;
    }
  };

  const calculate = () => {
    const inputValue = parseFloat(display);

    if (previousValue !== null && operation) {
      const newValue = performCalculation(previousValue, inputValue, operation);
      setDisplay(String(newValue));
      setPreviousValue(null);
      setOperation(null);
      setWaitingForOperand(true);
    }
  };

  const clear = () => {
    setDisplay('0');
    setPreviousValue(null);
    setOperation(null);
    setWaitingForOperand(false);
  };

  const inputDecimal = () => {
    if (waitingForOperand) {
      setDisplay('0.');
      setWaitingForOperand(false);
    } else if (display.indexOf('.') === -1) {
      setDisplay(display + '.');
    }
  };

  const toggleSign = () => {
    if (display !== '0') {
      setDisplay(display.charAt(0) === '-' ? display.slice(1) : '-' + display);
    }
  };

  const scientificFunction = (func: string) => {
    const value = parseFloat(display);
    let result = 0;

    switch (func) {
      case 'sin':
        result = Math.sin(isRadians ? value : (value * Math.PI) / 180);
        break;
      case 'cos':
        result = Math.cos(isRadians ? value : (value * Math.PI) / 180);
        break;
      case 'tan':
        result = Math.tan(isRadians ? value : (value * Math.PI) / 180);
        break;
      case 'log':
        result = Math.log10(value);
        break;
      case 'ln':
        result = Math.log(value);
        break;
      case 'sqrt':
        result = Math.sqrt(value);
        break;
      case 'square':
        result = value * value;
        break;
      case 'factorial':
        result = factorial(value);
        break;
      case 'reciprocal':
        result = 1 / value;
        break;
      case 'pi':
        result = Math.PI;
        break;
      case 'e':
        result = Math.E;
        break;
      default:
        result = value;
    }

    setDisplay(String(result));
    setWaitingForOperand(true);
  };

  const factorial = (n: number): number => {
    if (n < 0 || n !== Math.floor(n)) return NaN;
    if (n <= 1) return 1;
    return n * factorial(n - 1);
  };

  const memoryFunction = (func: string) => {
    const value = parseFloat(display);
    
    switch (func) {
      case 'MC':
        setMemory(0);
        break;
      case 'MR':
        setDisplay(String(memory));
        setWaitingForOperand(true);
        break;
      case 'M+':
        setMemory(memory + value);
        break;
      case 'M-':
        setMemory(memory - value);
        break;
      case 'MS':
        setMemory(value);
        break;
    }
  };

  return (
    <>
      <div className={containerStyles.page}>
        <div className="container mx-auto px-4 py-8">
          <div className="max-w-4xl mx-auto">
            <CalculatorHeader
              title="Scientific Calculator"
              description="Advanced calculator with trigonometric, logarithmic, and scientific functions. Full keyboard shortcuts."
              category="Math"
            />
            
            <div className={containerStyles.card + " p-6"}>
              {/* Display */}
              <div className="mb-4">
                <div className="bg-gray-100 dark:bg-gray-700 rounded-lg p-4 text-right border border-gray-200 dark:border-gray-600">
                  <div className={cn("text-3xl font-mono overflow-hidden", textStyles.h2)}>
                    {display}
                  </div>
                  <div className={cn("text-sm mt-2", textStyles.bodySmall)}>
                    Mode: {isRadians ? 'RAD' : 'DEG'} | Memory: {memory !== 0 ? memory : 'Empty'}
                  </div>
                </div>
              </div>

              {/* Mode Toggle */}
              <div className="mb-4 flex justify-center">
                <button
                  onClick={() => setIsRadians(!isRadians)}
                  className={buttonStyles.calc.scientific.mode}
                  title="Toggle between Radians and Degrees"
                >
                  {isRadians ? 'Radians' : 'Degrees'}
                </button>
              </div>

              {/* Buttons */}
              <div className="grid grid-cols-6 gap-2">
                {/* Row 1 - Memory & Clear */}
                <button onClick={() => memoryFunction('MC')} className={buttonStyles.calc.scientific.memory} title="Memory Clear">MC</button>
                <button onClick={() => memoryFunction('MR')} className={buttonStyles.calc.scientific.memory} title="Memory Recall">MR</button>
                <button onClick={() => memoryFunction('M+')} className={buttonStyles.calc.scientific.memory} title="Memory Add">M+</button>
                <button onClick={() => memoryFunction('M-')} className={buttonStyles.calc.scientific.memory} title="Memory Subtract">M-</button>
                <button onClick={() => memoryFunction('MS')} className={buttonStyles.calc.scientific.memory} title="Memory Store">MS</button>
                <button onClick={clear} className={buttonStyles.calc.scientific.clear} title="Clear (C or Esc)">C</button>

                {/* Row 2 - Scientific Functions */}
                <button onClick={() => scientificFunction('sin')} className={buttonStyles.calc.scientific.sciFunction} title="Sine (S)">sin</button>
                <button onClick={() => scientificFunction('cos')} className={buttonStyles.calc.scientific.sciFunction} title="Cosine (O)">cos</button>
                <button onClick={() => scientificFunction('tan')} className={buttonStyles.calc.scientific.sciFunction} title="Tangent (T)">tan</button>
                <button onClick={() => scientificFunction('log')} className={buttonStyles.calc.scientific.sciFunction} title="Log base 10 (L)">log</button>
                <button onClick={() => scientificFunction('ln')} className={buttonStyles.calc.scientific.sciFunction} title="Natural Log (N)">ln</button>
                <button onClick={() => inputOperator('^')} className={buttonStyles.calc.scientific.operator} title="Power (^)">x^y</button>

                {/* Row 3 - More Functions */}
                <button onClick={() => scientificFunction('sqrt')} className={buttonStyles.calc.scientific.sciFunction} title="Square Root (Q)">√</button>
                <button onClick={() => scientificFunction('square')} className={buttonStyles.calc.scientific.sciFunction} title="Square">x²</button>
                <button onClick={() => scientificFunction('factorial')} className={buttonStyles.calc.scientific.sciFunction} title="Factorial">x!</button>
                <button onClick={() => scientificFunction('reciprocal')} className={buttonStyles.calc.scientific.sciFunction} title="Reciprocal">1/x</button>
                <button onClick={() => scientificFunction('pi')} className={buttonStyles.calc.scientific.sciFunction} title="Pi (P)">π</button>
                <button onClick={() => scientificFunction('e')} className={buttonStyles.calc.scientific.sciFunction} title="Euler's number (E)">e</button>

                {/* Row 4 - Numbers and Operations */}
                <button onClick={() => inputNumber('7')} className={buttonStyles.calc.scientific.number} title="Seven">7</button>
                <button onClick={() => inputNumber('8')} className={buttonStyles.calc.scientific.number} title="Eight">8</button>
                <button onClick={() => inputNumber('9')} className={buttonStyles.calc.scientific.number} title="Nine">9</button>
                <button onClick={() => inputOperator('÷')} className={buttonStyles.calc.scientific.operator} title="Divide (/)">÷</button>
                <button onClick={toggleSign} className={buttonStyles.calc.scientific.function} title="Toggle Sign">±</button>
                <button onClick={() => inputNumber('(')} className={buttonStyles.calc.scientific.function} title="Open Parenthesis">(</button>

                {/* Row 5 */}
                <button onClick={() => inputNumber('4')} className={buttonStyles.calc.scientific.number} title="Four">4</button>
                <button onClick={() => inputNumber('5')} className={buttonStyles.calc.scientific.number} title="Five">5</button>
                <button onClick={() => inputNumber('6')} className={buttonStyles.calc.scientific.number} title="Six">6</button>
                <button onClick={() => inputOperator('×')} className={buttonStyles.calc.scientific.operator} title="Multiply (*)">×</button>
                <button onClick={() => inputOperator('%')} className={buttonStyles.calc.scientific.function} title="Percentage">%</button>
                <button onClick={() => inputNumber(')')} className={buttonStyles.calc.scientific.function} title="Close Parenthesis">)</button>

                {/* Row 6 */}
                <button onClick={() => inputNumber('1')} className={buttonStyles.calc.scientific.number} title="One">1</button>
                <button onClick={() => inputNumber('2')} className={buttonStyles.calc.scientific.number} title="Two">2</button>
                <button onClick={() => inputNumber('3')} className={buttonStyles.calc.scientific.number} title="Three">3</button>
                <button onClick={() => inputOperator('-')} className={buttonStyles.calc.scientific.operator} title="Subtract (-)">-</button>
                <button onClick={inputDecimal} className={buttonStyles.calc.scientific.number} title="Decimal Point (.)">.</button>
                <button onClick={calculate} className={buttonStyles.calc.scientific.equals} title="Equals (Enter or =)">=</button>

                {/* Row 7 */}
                <button onClick={() => inputNumber('0')} className={cn("col-span-2", buttonStyles.calc.scientific.number)} title="Zero">0</button>
                <button onClick={() => inputNumber('00')} className={buttonStyles.calc.scientific.number} title="Double Zero">00</button>
                <button onClick={() => inputOperator('+')} className={buttonStyles.calc.scientific.operator} title="Add (+)">+</button>
                <button onClick={() => setDisplay('0')} className={buttonStyles.calc.scientific.function} title="Clear Entry">CE</button>
                <button onClick={() => setDisplay(display.slice(0, -1) || '0')} className={buttonStyles.calc.scientific.function} title="Backspace">⌫</button>
              </div>
            </div>

            <div className="mt-8 text-center">
              <div className={cn("text-center", containerStyles.infoCard, "p-4")}>
                <p className={cn(textStyles.h4, "mb-2")}>Keyboard Shortcuts:</p>
                <p className={cn(textStyles.bodySmall, "mt-1")}>Numbers (0-9), Operators (+, -, *, /, ^), Enter/= (calculate), Esc/C (clear), Backspace (delete)</p>
                <p className={cn(textStyles.bodySmall, "mt-1")}>Functions: S (sin), O (cos), T (tan), L (log), N (ln), Q (sqrt), P (π), E (e)</p>
              </div>
            </div>
          </div>
        </div>
        <Footer />
      </div>
    </>
  );
}