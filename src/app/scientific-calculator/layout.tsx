import { generatePageTitle, generateCanonicalUrl } from '@/lib/utils';

export const metadata = {
  title: generatePageTitle("Scientific Calculator"),
  description: "Advanced scientific calculator with trigonometric, logarithmic, and mathematical functions. Free online calculator with keyboard support.",
  keywords: "scientific calculator, trigonometry, logarithm, math functions, sin, cos, tan, log, calculator",
  robots: "index, follow",
  alternates: {
    canonical: generateCanonicalUrl("/scientific-calculator"),
  },
};

export default function ScientificCalculatorLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  return children;
}