import { generatePageTitle, generateCanonicalUrl } from '@/lib/utils';
import type { Metadata } from 'next';

export const metadata: Metadata = {
  title: generatePageTitle('Time Calculator'),
  description: 'Calculate time differences, add or subtract time intervals, and convert between different time formats. Professional time calculation tool.',
  keywords: 'time calculator, time difference, add time, subtract time, time conversion, hours calculator, minutes calculator, time zone calculator',
  robots: 'index, follow',
  alternates: {
    canonical: generateCanonicalUrl('/time-calculator'),
  },
  openGraph: {
    title: generatePageTitle('Time Calculator'),
    description: 'Calculate time differences, add or subtract time intervals, and convert between different time formats.',
    type: 'website',
    url: generateCanonicalUrl('/time-calculator'),
  },
  twitter: {
    card: 'summary',
    title: generatePageTitle('Time Calculator'),
    description: 'Calculate time differences, add or subtract time intervals, and convert between different time formats.',
  },
};

export default function TimeCalculatorLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  return children;
}