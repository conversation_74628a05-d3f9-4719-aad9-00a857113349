'use client';

import { useState, useEffect, useCallback } from 'react';
import Footer from "@/components/Footer";
import CalculatorHeader from '@/components/CalculatorHeader';
import { containerStyles } from '@/components/ui/styles';

interface TimeResult {
  hours: number;
  minutes: number;
  seconds: number;
  totalHours: number;
  totalMinutes: number;
  totalSeconds: number;
  formatted: string;
  resultDate?: string;
}

export default function TimeCalculator() {
  const [calculatorType, setCalculatorType] = useState<'difference' | 'add' | 'subtract'>('difference');
  
  // Time difference calculator
  const [startTime, setStartTime] = useState<string>('09:00');
  const [endTime, setEndTime] = useState<string>('17:00');
  const [includeDate, setIncludeDate] = useState<boolean>(false);
  const [startDate, setStartDate] = useState<string>('');
  const [endDate, setEndDate] = useState<string>('');
  
  // Add/Subtract calculator
  const [baseTime, setBaseTime] = useState<string>('12:00');
  const [baseDate, setBaseDate] = useState<string>('');
  const [addHours, setAddHours] = useState<number>(0);
  const [addMinutes, setAddMinutes] = useState<number>(0);
  const [addSeconds, setAddSeconds] = useState<number>(0);
  
  const [result, setResult] = useState<TimeResult | null>(null);

  const parseTimeString = useCallback((timeStr: string): { hours: number; minutes: number; seconds: number } => {
    const parts = timeStr.split(':');
    return {
      hours: parseInt(parts[0]) || 0,
      minutes: parseInt(parts[1]) || 0,
      seconds: parseInt(parts[2]) || 0
    };
  }, []);

  const formatTime = useCallback((hours: number, minutes: number, seconds: number): string => {
    const h = hours.toString().padStart(2, '0');
    const m = minutes.toString().padStart(2, '0');
    const s = seconds.toString().padStart(2, '0');
    return `${h}:${m}:${s}`;
  }, []);

  const calculateTimeDifference = useCallback(() => {
    let startDateTime: Date;
    let endDateTime: Date;

    if (includeDate && startDate && endDate) {
      const startTimeObj = parseTimeString(startTime);
      const endTimeObj = parseTimeString(endTime);
      
      startDateTime = new Date(`${startDate}T${formatTime(startTimeObj.hours, startTimeObj.minutes, startTimeObj.seconds)}`);
      endDateTime = new Date(`${endDate}T${formatTime(endTimeObj.hours, endTimeObj.minutes, endTimeObj.seconds)}`);
    } else {
      const startTimeObj = parseTimeString(startTime);
      const endTimeObj = parseTimeString(endTime);
      
      startDateTime = new Date();
      startDateTime.setHours(startTimeObj.hours, startTimeObj.minutes, startTimeObj.seconds, 0);
      
      endDateTime = new Date();
      endDateTime.setHours(endTimeObj.hours, endTimeObj.minutes, endTimeObj.seconds, 0);
      
      // If end time is before start time, assume it's the next day
      if (endDateTime <= startDateTime) {
        endDateTime.setDate(endDateTime.getDate() + 1);
      }
    }

    const diffMs = endDateTime.getTime() - startDateTime.getTime();
    
    if (diffMs < 0) {
      return null;
    }

    const totalSeconds = Math.floor(diffMs / 1000);
    const totalMinutes = Math.floor(totalSeconds / 60);
    const totalHours = totalMinutes / 60;

    const hours = Math.floor(totalSeconds / 3600);
    const minutes = Math.floor((totalSeconds % 3600) / 60);
    const seconds = totalSeconds % 60;

    return {
      hours,
      minutes,
      seconds,
      totalHours: Number(totalHours.toFixed(2)),
      totalMinutes,
      totalSeconds,
      formatted: formatTime(hours, minutes, seconds)
    };
  }, [startTime, endTime, includeDate, startDate, endDate, parseTimeString, formatTime]);

  const calculateAddSubtract = useCallback(() => {
    const baseTimeObj = parseTimeString(baseTime);
    let baseDateTime: Date;

    if (baseDate) {
      baseDateTime = new Date(`${baseDate}T${formatTime(baseTimeObj.hours, baseTimeObj.minutes, baseTimeObj.seconds)}`);
    } else {
      baseDateTime = new Date();
      baseDateTime.setHours(baseTimeObj.hours, baseTimeObj.minutes, baseTimeObj.seconds, 0);
    }

    const addTotalMs = (addHours * 3600 + addMinutes * 60 + addSeconds) * 1000;
    const resultDateTime = new Date(baseDateTime.getTime() + (calculatorType === 'add' ? addTotalMs : -addTotalMs));

    const hours = resultDateTime.getHours();
    const minutes = resultDateTime.getMinutes();
    const seconds = resultDateTime.getSeconds();

    return {
      hours,
      minutes,
      seconds,
      totalHours: Number((hours + minutes/60 + seconds/3600).toFixed(2)),
      totalMinutes: hours * 60 + minutes + Math.floor(seconds / 60),
      totalSeconds: hours * 3600 + minutes * 60 + seconds,
      formatted: formatTime(hours, minutes, seconds),
      resultDate: baseDate ? resultDateTime.toISOString().split('T')[0] : undefined
    };
  }, [baseTime, baseDate, addHours, addMinutes, addSeconds, calculatorType, parseTimeString, formatTime]);

  const handleCalculate = useCallback(() => {
    let timeResult;
    
    if (calculatorType === 'difference') {
      timeResult = calculateTimeDifference();
    } else {
      timeResult = calculateAddSubtract();
    }
    
    setResult(timeResult);
  }, [calculatorType, calculateTimeDifference, calculateAddSubtract]);

  useEffect(() => {
    handleCalculate();
  }, [handleCalculate]);

  useEffect(() => {
    // Set default dates
    const today = new Date();
    const tomorrow = new Date(today);
    tomorrow.setDate(tomorrow.getDate() + 1);
    
    setStartDate(today.toISOString().split('T')[0]);
    setEndDate(tomorrow.toISOString().split('T')[0]);
    setBaseDate(today.toISOString().split('T')[0]);
  }, []);

  return (
    <>
      <div className={containerStyles.page}>
        <div className="container mx-auto px-4">
          <div className="max-w-6xl mx-auto">
            <CalculatorHeader
              title="Time Calculator"
              description="Calculate time differences, add or subtract time intervals, and work with time in various formats. Perfect for planning, scheduling, and time management."
              category="Other"
              className="text-center mb-12"
            />

            {/* Calculator Type Toggle */}
            <div className="flex justify-center mb-8">
              <div className="bg-white dark:bg-gray-800 rounded-lg p-1 flex border border-gray-200 dark:border-gray-700 shadow-sm">
                <button
                  onClick={() => setCalculatorType('difference')}
                  className={`px-4 py-2 rounded-md font-medium transition-colors ${ 
                    calculatorType === 'difference'
                      ? 'bg-blue-500 dark:bg-blue-600 text-white shadow-md'
                      : 'text-gray-600 dark:text-gray-300 hover:text-gray-800 dark:hover:text-white hover:bg-gray-50 dark:hover:bg-gray-700'
                  }`}
                >
                  Time Difference
                </button>
                <button
                  onClick={() => setCalculatorType('add')}
                  className={`px-4 py-2 rounded-md font-medium transition-colors ${
                    calculatorType === 'add'
                      ? 'bg-green-500 dark:bg-green-600 text-white shadow-md'
                      : 'text-gray-600 dark:text-gray-300 hover:text-gray-800 dark:hover:text-white hover:bg-gray-50 dark:hover:bg-gray-700'
                  }`}
                >
                  Add Time
                </button>
                <button
                  onClick={() => setCalculatorType('subtract')}
                  className={`px-4 py-2 rounded-md font-medium transition-colors ${
                    calculatorType === 'subtract'
                      ? 'bg-red-500 dark:bg-red-600 text-white shadow-md'
                      : 'text-gray-600 dark:text-gray-300 hover:text-gray-800 dark:hover:text-white hover:bg-gray-50 dark:hover:bg-gray-700'
                  }`}
                >
                  Subtract Time
                </button>
              </div>
            </div>

            <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
              {/* Calculator Section */}
              <div className="bg-white dark:bg-gray-800 rounded-lg shadow-lg dark:shadow-gray-700/50 p-6 border border-gray-200 dark:border-gray-700">
                <h2 className="text-2xl font-semibold text-gray-900 dark:text-white mb-6">
                  {calculatorType === 'difference' && 'Calculate Time Difference'}
                  {calculatorType === 'add' && 'Add Time'}
                  {calculatorType === 'subtract' && 'Subtract Time'}
                </h2>

                <div className="space-y-6">
                  {calculatorType === 'difference' ? (
                    <>
                      {/* Time Difference Calculator */}
                      <div>
                        <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                          Start Time
                        </label>
                        <input
                          type="time"
                          step="1"
                          value={startTime}
                          onChange={(e) => setStartTime(e.target.value)}
                          className="w-full px-3 py-2 bg-white dark:bg-gray-700 border border-gray-300 dark:border-gray-600 rounded-md text-gray-900 dark:text-white focus:outline-none focus:ring-2 focus:ring-blue-500 dark:focus:ring-blue-400 focus:border-blue-500 dark:focus:border-blue-400 transition-colors"
                        />
                      </div>

                      <div>
                        <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                          End Time
                        </label>
                        <input
                          type="time"
                          step="1"
                          value={endTime}
                          onChange={(e) => setEndTime(e.target.value)}
                          className="w-full px-3 py-2 bg-white dark:bg-gray-700 border border-gray-300 dark:border-gray-600 rounded-md text-gray-900 dark:text-white focus:outline-none focus:ring-2 focus:ring-blue-500 dark:focus:ring-blue-400 focus:border-blue-500 dark:focus:border-blue-400 transition-colors"
                        />
                      </div>

                      <div>
                        <label className="flex items-center text-gray-700 dark:text-gray-300">
                          <input
                            type="checkbox"
                            checked={includeDate}
                            onChange={(e) => setIncludeDate(e.target.checked)}
                            className="mr-2 text-blue-600 dark:text-blue-400 focus:ring-blue-500 dark:focus:ring-blue-400 rounded"
                          />
                          <span className="text-sm font-medium">Include specific dates</span>
                        </label>
                      </div>

                      {includeDate && (
                        <>
                          <div>
                            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                              Start Date
                            </label>
                            <input
                              type="date"
                              value={startDate}
                              onChange={(e) => setStartDate(e.target.value)}
                              className="w-full px-3 py-2 bg-white dark:bg-gray-700 border border-gray-300 dark:border-gray-600 rounded-md text-gray-900 dark:text-white focus:outline-none focus:ring-2 focus:ring-blue-500 dark:focus:ring-blue-400 focus:border-blue-500 dark:focus:border-blue-400 transition-colors"
                            />
                          </div>
                          <div>
                            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                              End Date
                            </label>
                            <input
                              type="date"
                              value={endDate}
                              onChange={(e) => setEndDate(e.target.value)}
                              min={startDate}
                              className="w-full px-3 py-2 bg-white dark:bg-gray-700 border border-gray-300 dark:border-gray-600 rounded-md text-gray-900 dark:text-white focus:outline-none focus:ring-2 focus:ring-blue-500 dark:focus:ring-blue-400 focus:border-blue-500 dark:focus:border-blue-400 transition-colors"
                            />
                          </div>
                        </>
                      )}
                    </>
                  ) : (
                    <>
                      {/* Add/Subtract Time Calculator */}
                      <div>
                        <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                          Base Time
                        </label>
                        <input
                          type="time"
                          step="1"
                          value={baseTime}
                          onChange={(e) => setBaseTime(e.target.value)}
                          className="w-full px-3 py-2 bg-white dark:bg-gray-700 border border-gray-300 dark:border-gray-600 rounded-md text-gray-900 dark:text-white focus:outline-none focus:ring-2 focus:ring-blue-500 dark:focus:ring-blue-400 focus:border-blue-500 dark:focus:border-blue-400 transition-colors"
                        />
                      </div>

                      <div>
                        <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                          Base Date (Optional)
                        </label>
                        <input
                          type="date"
                          value={baseDate}
                          onChange={(e) => setBaseDate(e.target.value)}
                          className="w-full px-3 py-2 bg-white dark:bg-gray-700 border border-gray-300 dark:border-gray-600 rounded-md text-gray-900 dark:text-white focus:outline-none focus:ring-2 focus:ring-blue-500 dark:focus:ring-blue-400 focus:border-blue-500 dark:focus:border-blue-400 transition-colors"
                        />
                      </div>

                      <div className="grid grid-cols-3 gap-4">
                        <div>
                          <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                            Hours
                          </label>
                          <input
                            type="number"
                            min="0"
                            max="23"
                            value={addHours}
                            onChange={(e) => setAddHours(Number(e.target.value))}
                            className="w-full px-3 py-2 bg-white dark:bg-gray-700 border border-gray-300 dark:border-gray-600 rounded-md text-gray-900 dark:text-white focus:outline-none focus:ring-2 focus:ring-blue-500 dark:focus:ring-blue-400 focus:border-blue-500 dark:focus:border-blue-400 transition-colors"
                          />
                        </div>
                        <div>
                          <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                            Minutes
                          </label>
                          <input
                            type="number"
                            min="0"
                            max="59"
                            value={addMinutes}
                            onChange={(e) => setAddMinutes(Number(e.target.value))}
                            className="w-full px-3 py-2 bg-white dark:bg-gray-700 border border-gray-300 dark:border-gray-600 rounded-md text-gray-900 dark:text-white focus:outline-none focus:ring-2 focus:ring-blue-500 dark:focus:ring-blue-400 focus:border-blue-500 dark:focus:border-blue-400 transition-colors"
                          />
                        </div>
                        <div>
                          <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                            Seconds
                          </label>
                          <input
                            type="number"
                            min="0"
                            max="59"
                            value={addSeconds}
                            onChange={(e) => setAddSeconds(Number(e.target.value))}
                            className="w-full px-3 py-2 bg-white dark:bg-gray-700 border border-gray-300 dark:border-gray-600 rounded-md text-gray-900 dark:text-white focus:outline-none focus:ring-2 focus:ring-blue-500 dark:focus:ring-blue-400 focus:border-blue-500 dark:focus:border-blue-400 transition-colors"
                          />
                        </div>
                      </div>
                    </>
                  )}

                  {/* Quick Preset Buttons */}
                  <div className="border-t border-gray-200 dark:border-gray-600 pt-4">
                    <h3 className="text-sm font-medium text-gray-700 dark:text-gray-300 mb-3">Quick Presets</h3>
                    <div className="grid grid-cols-2 gap-2">
                      {calculatorType === 'difference' ? (
                        <>
                          <button
                            onClick={() => {
                              setStartTime('09:00');
                              setEndTime('17:00');
                            }}
                            className="text-left p-2 bg-gray-100 dark:bg-gray-700 border border-gray-300 dark:border-gray-600 rounded hover:bg-gray-200 dark:hover:bg-gray-600 text-sm transition-colors"
                          >
                            <div className="font-medium text-gray-800 dark:text-gray-200">Work Day</div>
                            <div className="text-gray-600 dark:text-gray-400">9 AM - 5 PM</div>
                          </button>
                          <button
                            onClick={() => {
                              setStartTime('22:00');
                              setEndTime('06:00');
                            }}
                            className="text-left p-2 bg-gray-100 dark:bg-gray-700 border border-gray-300 dark:border-gray-600 rounded hover:bg-gray-200 dark:hover:bg-gray-600 text-sm transition-colors"
                          >
                            <div className="font-medium text-gray-800 dark:text-gray-200">Night Sleep</div>
                            <div className="text-gray-600 dark:text-gray-400">10 PM - 6 AM</div>
                          </button>
                        </>
                      ) : (
                        <>
                          <button
                            onClick={() => {
                              setAddHours(1);
                              setAddMinutes(0);
                              setAddSeconds(0);
                            }}
                            className="text-left p-2 bg-gray-100 dark:bg-gray-700 border border-gray-300 dark:border-gray-600 rounded hover:bg-gray-200 dark:hover:bg-gray-600 text-sm transition-colors"
                          >
                            <div className="font-medium text-gray-800 dark:text-gray-200">1 Hour</div>
                          </button>
                          <button
                            onClick={() => {
                              setAddHours(0);
                              setAddMinutes(30);
                              setAddSeconds(0);
                            }}
                            className="text-left p-2 bg-gray-100 dark:bg-gray-700 border border-gray-300 dark:border-gray-600 rounded hover:bg-gray-200 dark:hover:bg-gray-600 text-sm transition-colors"
                          >
                            <div className="font-medium text-gray-800 dark:text-gray-200">30 Minutes</div>
                          </button>
                          <button
                            onClick={() => {
                              setAddHours(8);
                              setAddMinutes(0);
                              setAddSeconds(0);
                            }}
                            className="text-left p-2 bg-gray-100 dark:bg-gray-700 border border-gray-300 dark:border-gray-600 rounded hover:bg-gray-200 dark:hover:bg-gray-600 text-sm transition-colors"
                          >
                            <div className="font-medium text-gray-800 dark:text-gray-200">8 Hours</div>
                          </button>
                          <button
                            onClick={() => {
                              setAddHours(0);
                              setAddMinutes(15);
                              setAddSeconds(0);
                            }}
                            className="text-left p-2 bg-gray-100 dark:bg-gray-700 border border-gray-300 dark:border-gray-600 rounded hover:bg-gray-200 dark:hover:bg-gray-600 text-sm transition-colors"
                          >
                            <div className="font-medium text-gray-800 dark:text-gray-200">15 Minutes</div>
                          </button>
                        </>
                      )}
                    </div>
                  </div>
                </div>
              </div>

              {/* Results Section */}
              <div className="space-y-6">
                {result && (
                  <div className="bg-white dark:bg-gray-800 rounded-lg shadow-lg dark:shadow-gray-700/50 p-6 border-l-4 border-blue-500 dark:border-blue-400 border border-gray-200 dark:border-gray-700">
                    <h3 className="text-xl font-semibold text-gray-900 dark:text-white mb-4">
                      {calculatorType === 'difference' && '⏱️ Time Difference'}
                      {calculatorType === 'add' && '➕ Added Time Result'}
                      {calculatorType === 'subtract' && '➖ Subtracted Time Result'}
                    </h3>
                    
                    <div className="text-center mb-4">
                      <div className="text-3xl font-bold text-blue-600 dark:text-blue-400 mb-2">
                        {result.formatted}
                      </div>
                      {calculatorType !== 'difference' && result?.resultDate && (
                        <div className="text-lg text-gray-600 dark:text-gray-300">
                          on {new Date(result.resultDate).toLocaleDateString('en-US', { 
                            year: 'numeric', 
                            month: 'long', 
                            day: 'numeric' 
                          })}
                        </div>
                      )}
                    </div>

                    <div className="space-y-3">
                      <div className="flex justify-between items-center p-3 bg-blue-50 dark:bg-blue-900/50 rounded border border-blue-200 dark:border-blue-700">
                        <span className="font-medium text-blue-800 dark:text-blue-200">Hours</span>
                        <span className="text-lg font-bold text-blue-600 dark:text-blue-300">{result.hours}</span>
                      </div>
                      <div className="flex justify-between items-center p-3 bg-green-50 dark:bg-green-900/50 rounded border border-green-200 dark:border-green-700">
                        <span className="font-medium text-green-800 dark:text-green-200">Minutes</span>
                        <span className="text-lg font-bold text-green-600 dark:text-green-300">{result.minutes}</span>
                      </div>
                      <div className="flex justify-between items-center p-3 bg-purple-50 dark:bg-purple-900/50 rounded border border-purple-200 dark:border-purple-700">
                        <span className="font-medium text-purple-800 dark:text-purple-200">Seconds</span>
                        <span className="text-lg font-bold text-purple-600 dark:text-purple-300">{result.seconds}</span>
                      </div>
                    </div>
                  </div>
                )}

                {result && calculatorType === 'difference' && (
                  <div className="bg-white dark:bg-gray-800 rounded-lg shadow-lg dark:shadow-gray-700/50 p-6 border border-gray-200 dark:border-gray-700">
                    <h3 className="text-xl font-semibold text-gray-900 dark:text-white mb-4">📊 Total Time</h3>
                    <div className="space-y-3">
                      <div className="flex justify-between items-center p-3 bg-gray-50 dark:bg-gray-700 rounded border border-gray-200 dark:border-gray-600">
                        <span className="font-medium text-gray-700 dark:text-gray-300">Total Hours</span>
                        <span className="text-lg font-bold text-orange-600 dark:text-orange-400">{result.totalHours}</span>
                      </div>
                      <div className="flex justify-between items-center p-3 bg-gray-50 dark:bg-gray-700 rounded border border-gray-200 dark:border-gray-600">
                        <span className="font-medium text-gray-700 dark:text-gray-300">Total Minutes</span>
                        <span className="text-lg font-bold text-red-600 dark:text-red-400">{result.totalMinutes.toLocaleString()}</span>
                      </div>
                      <div className="flex justify-between items-center p-3 bg-gray-50 dark:bg-gray-700 rounded border border-gray-200 dark:border-gray-600">
                        <span className="font-medium text-gray-700 dark:text-gray-300">Total Seconds</span>
                        <span className="text-lg font-bold text-indigo-600 dark:text-indigo-400">{result.totalSeconds.toLocaleString()}</span>
                      </div>
                    </div>
                  </div>
                )}

                {/* Time Conversion Reference */}
                <div className="bg-yellow-50 dark:bg-yellow-900/20 border border-yellow-200 dark:border-yellow-700 rounded-lg p-6">
                  <h3 className="text-xl font-semibold text-yellow-800 dark:text-yellow-200 mb-4">⏰ Time Conversions</h3>
                  <div className="space-y-2 text-sm text-yellow-700 dark:text-yellow-300">
                    <div className="flex justify-between">
                      <span>1 hour</span>
                      <span>60 minutes = 3,600 seconds</span>
                    </div>
                    <div className="flex justify-between">
                      <span>1 day</span>
                      <span>24 hours = 1,440 minutes</span>
                    </div>
                    <div className="flex justify-between">
                      <span>1 week</span>
                      <span>168 hours = 10,080 minutes</span>
                    </div>
                    <div className="flex justify-between">
                      <span>1 month (avg)</span>
                      <span>730 hours = 43,800 minutes</span>
                    </div>
                  </div>
                </div>

                {/* Usage Tips */}
                <div className="bg-green-50 dark:bg-green-900/20 border border-green-200 dark:border-green-700 rounded-lg p-6">
                  <h3 className="text-xl font-semibold text-green-800 dark:text-green-200 mb-4">💡 Usage Tips</h3>
                  <ul className="space-y-2 text-green-700 dark:text-green-300 text-sm">
                    <li>• Use time difference for calculating work hours or event durations</li>
                    <li>• Add time to find deadlines or appointment times</li>
                    <li>• Subtract time to find when to start tasks</li>
                    <li>• Include dates for calculations spanning multiple days</li>
                    <li>• Use presets for common time intervals</li>
                  </ul>
                </div>
              </div>
            </div>
          </div>
        </div>
        <Footer />
      </div>
    </>
  );
}