export type Category = 'Financial' | 'Health' | 'Math' | 'Other';

export interface Calculator {
  href: string;
  title: string;
  description: string;
  iconBg: string;
  iconColor: string;
  iconPath: string;
  category: Category;
  priority?: number;
}

export const calculators: Calculator[] = [
  {
    href: "/body-type-calculator",
    title: "Body Type Calculator",
    description: "Determine your body type (ectomorph, mesomorph, endomorph) with our comprehensive calculator.",
    iconBg: "bg-blue-500/10 dark:bg-blue-500/15 backdrop-blur-sm border border-blue-400/30 dark:border-blue-400/40",
    iconColor: "text-blue-600 dark:text-blue-400",
    iconPath: "M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z",
    category: "Health",
    priority: 0.8
  },
  {
    href: "/calculator",
    title: "Simple Calculator",
    description: "Basic calculator for everyday arithmetic operations. Clean interface with keyboard support.",
    iconBg: "bg-green-500/10 dark:bg-green-500/15 backdrop-blur-sm border border-green-400/30 dark:border-green-400/40",
    iconColor: "text-green-600 dark:text-green-400",
    iconPath: "M9 7h6m0 10v-3m-3 3h.01M9 17h.01M9 14h.01M12 14h.01M15 11h.01M12 11h.01M9 11h.01M7 21h10a2 2 0 002-2V5a2 2 0 00-2-2H7a2 2 0 00-2 2v14a2 2 0 002 2z",
    category: "Math",
    priority: 0.8
  },
  {
    href: "/scientific-calculator",
    title: "Scientific Calculator",
    description: "Advanced calculator with trigonometric, logarithmic, and scientific functions. Full keyboard shortcuts.",
    iconBg: "bg-purple-500/10 dark:bg-purple-500/15 backdrop-blur-sm border border-purple-400/30 dark:border-purple-400/40",
    iconColor: "text-purple-600 dark:text-purple-400",
    iconPath: "M9 3v2m6-2v2M9 19v2m6-2v2M5 9H3m2 6H3m18-6h-2m2 6h-2M7 19h10a2 2 0 002-2V7a2 2 0 00-2-2H7a2 2 0 00-2 2v10a2 2 0 002 2zM9 9h6v6H9V9z",
    category: "Math",
    priority: 0.8
  },
  {
    href: "/bmi-calculator",
    title: "BMI Calculator",
    description: "Calculate your Body Mass Index and understand your weight status with healthy range recommendations.",
    iconBg: "bg-emerald-500/10 dark:bg-emerald-500/15 backdrop-blur-sm border border-emerald-400/30 dark:border-emerald-400/40",
    iconColor: "text-emerald-600 dark:text-emerald-400",
    iconPath: "M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z",
    category: "Health",
    priority: 0.8
  },
  {
    href: "/calorie-calculator",
    title: "Calorie Calculator",
    description: "Calculate your daily calorie needs based on activity level and goals for weight management.",
    iconBg: "bg-orange-500/10 dark:bg-orange-500/15 backdrop-blur-sm border border-orange-400/30 dark:border-orange-400/40",
    iconColor: "text-orange-600 dark:text-orange-400",
    iconPath: "M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z",
    category: "Health",
    priority: 0.8
  },
  {
    href: "/concrete-calculator",
    title: "Concrete Calculator",
    description: "Calculate concrete volume and weight for construction projects with waste estimation.",
    iconBg: "bg-slate-500/10 dark:bg-slate-500/15 backdrop-blur-sm border border-slate-400/30 dark:border-slate-400/40",
    iconColor: "text-slate-600 dark:text-slate-400",
    iconPath: "M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4",
    category: "Other",
    priority: 0.8
  },
  {
    href: "/cd-calculator",
    title: "CD Calculator",
    description: "Calculate Certificate of Deposit earnings with compound interest and detailed breakdown.",
    iconBg: "bg-yellow-500/10 dark:bg-yellow-500/15 backdrop-blur-sm border border-yellow-400/30 dark:border-yellow-400/40",
    iconColor: "text-yellow-600 dark:text-yellow-400",
    iconPath: "M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1",
    category: "Financial",
    priority: 0.8
  },
  {
    href: "/date-converter",
    title: "Date Converter",
    description: "Convert between days, weeks, months, and years. Perfect for project planning and time calculations.",
    iconBg: "bg-indigo-500/10 dark:bg-indigo-500/15 backdrop-blur-sm border border-indigo-400/30 dark:border-indigo-400/40",
    iconColor: "text-indigo-600 dark:text-indigo-400",
    iconPath: "M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z",
    category: "Other",
    priority: 0.8
  },
  {
    href: "/storage-converter",
    title: "Storage Converter",
    description: "Convert between storage units: bytes, KB, MB, GB, TB and binary equivalents (KiB, MiB, GiB).",
    iconBg: "bg-pink-500/10 dark:bg-pink-500/15 backdrop-blur-sm border border-pink-400/30 dark:border-pink-400/40",
    iconColor: "text-pink-600 dark:text-pink-400",
    iconPath: "M4 7v10c0 2.21 3.582 4 8 4s8-1.79 8-4V7M4 7c0 2.21 3.582 4 8 4s8-1.79 8-4M4 7c0-2.21 3.582-4 8-4s8 1.79 8 4m0 5c0 2.21-3.582 4-8 4s-8-1.79-8-4",
    category: "Other",
    priority: 0.8
  },
  {
    href: "/sleep-calculator",
    title: "Sleep Calculator",
    description: "Optimize your sleep schedule with science-based calculations for bedtime and wake-up times.",
    iconBg: "bg-violet-500/10 dark:bg-violet-500/15 backdrop-blur-sm border border-violet-400/30 dark:border-violet-400/40",
    iconColor: "text-violet-600 dark:text-violet-400",
    iconPath: "M20.354 15.354A9 9 0 018.646 3.646 9.003 9.003 0 0012 21a9.003 9.003 0 008.354-5.646z",
    category: "Health",
    priority: 0.8
  },
  {
    href: "/base-converter",
    title: "Base Converter",
    description: "Convert numbers between different bases: binary, octal, decimal, hexadecimal, and custom bases.",
    iconBg: "bg-teal-500/10 dark:bg-teal-500/15 backdrop-blur-sm border border-teal-400/30 dark:border-teal-400/40",
    iconColor: "text-teal-600 dark:text-teal-400",
    iconPath: "M7 20l4-16m2 16l4-16M6 9h14M4 15h14",
    category: "Math",
    priority: 0.8
  },
  {
    href: "/age-calculator",
    title: "Age Calculator",
    description: "Calculate your exact age in years, months, days, hours, minutes, and seconds with zodiac sign.",
    iconBg: "bg-fuchsia-500/10 dark:bg-fuchsia-500/15 backdrop-blur-sm border border-fuchsia-400/30 dark:border-fuchsia-400/40",
    iconColor: "text-fuchsia-600 dark:text-fuchsia-400",
    iconPath: "M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z",
    category: "Other",
    priority: 0.8
  },
  {
    href: "/time-calculator",
    title: "Time Calculator",
    description: "Calculate time differences, add or subtract time intervals, and work with various time formats.",
    iconBg: "bg-cyan-500/10 dark:bg-cyan-500/15 backdrop-blur-sm border border-cyan-400/30 dark:border-cyan-400/40",
    iconColor: "text-cyan-600 dark:text-cyan-400",
    iconPath: "M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z",
    category: "Other",
    priority: 0.8
  },
  {
    href: "/date-calculator",
    title: "Date Calculator",
    description: "Calculate date differences, add or subtract time periods, and work with business days.",
    iconBg: "bg-rose-500/10 dark:bg-rose-500/15 backdrop-blur-sm border border-rose-400/30 dark:border-rose-400/40",
    iconColor: "text-rose-600 dark:text-rose-400",
    iconPath: "M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z",
    category: "Other",
    priority: 0.8
  },
  {
    href: "/scale-conversion-calculator",
    title: "Scale Conversion Calculator",
    description: "Convert between real size and scale size with different ratios. Perfect for architects and model makers.",
    iconBg: "bg-amber-500/10 dark:bg-amber-500/15 backdrop-blur-sm border border-amber-400/30 dark:border-amber-400/40",
    iconColor: "text-amber-600 dark:text-amber-400",
    iconPath: "M3 4a1 1 0 011-1h16a1 1 0 011 1v2.586a1 1 0 01-.293.707l-6.414 6.414a1 1 0 00-.293.707V17l-4 4v-6.586a1 1 0 00-.293-.707L3.293 7.293A1 1 0 013 6.586V4z",
    category: "Math",
    priority: 0.8
  },
  {
    href: "/map-scale-calculator",
    title: "Map Scale Calculator",
    description: "Calculate real distances from map measurements. Essential for navigation and geographic planning.",
    iconBg: "bg-emerald-500/10 dark:bg-emerald-500/15 backdrop-blur-sm border border-emerald-400/30 dark:border-emerald-400/40",
    iconColor: "text-emerald-600 dark:text-emerald-400",
    iconPath: "M9 20l-5.447-2.724A1 1 0 013 16.382V5.618a1 1 0 011.447-.894L9 7m0 13l6-3m-6 3V7m6 10l4.553 2.276A1 1 0 0021 18.382V7.618a1 1 0 00-.553-.894L15 4m0 13V4m0 0L9 7",
    category: "Math",
    priority: 0.8
  },
  {
    href: "/ppi-calculator",
    title: "PPI Calculator",
    description: "Calculate screen PPI (Pixels Per Inch) and DPI for displays, smartphones, tablets, monitors, and TVs.",
    iconBg: "bg-sky-500/10 dark:bg-sky-500/15 backdrop-blur-sm border border-sky-400/30 dark:border-sky-400/40",
    iconColor: "text-sky-600 dark:text-sky-400",
    iconPath: "M9.75 17L9 20l-1 1h8l-1-1-.75-3M3 13h18M5 17h14a2 2 0 002-2V5a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z",
    category: "Other",
    priority: 0.8
  },
  {
    href: "/bandwidth-converter",
    title: "Bandwidth Calculator",
    description: "Professional bandwidth calculator with unit conversion, download time estimation, website bandwidth planning, and network speed reference.",
    iconBg: "bg-red-500/10 dark:bg-red-500/15 backdrop-blur-sm border border-red-400/30 dark:border-red-400/40",
    iconColor: "text-red-600 dark:text-red-400",
    iconPath: "M13 10V3L4 14h7v7l9-11h-7z",
    category: "Other",
    priority: 0.8
  },
  {
    href: "/no-tax-overtime-calculator",
    title: "No Tax on Overtime Calculator",
    description: "Calculate potential federal tax savings under the No Tax on Overtime provision for 2025-2028. Estimate deductions on overtime premium pay.",
    iconBg: "bg-green-500/10 dark:bg-green-500/15 backdrop-blur-sm border border-green-400/30 dark:border-green-400/40",
    iconColor: "text-green-600 dark:text-green-400",
    iconPath: "M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1M9 11h6M9 15h6",
    category: "Financial",
    priority: 0.8
  },
  {
    href: "/time-and-a-half-calculator",
    title: "Time and a Half Calculator",
    description: "Calculate your time and a half overtime pay with our easy-to-use calculator. Enter your regular hourly rate and overtime hours to get instant results.",
    iconBg: "bg-green-500/10 dark:bg-green-500/15 backdrop-blur-sm border border-green-400/30 dark:border-green-400/40",
    iconColor: "text-green-600 dark:text-green-400",
    iconPath: "M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z",
    category: "Financial",
    priority: 0.8
  },
  {
    href: "/salary-to-hourly-calculator",
    title: "Salary to Hourly Calculator",
    description: "Convert your annual salary to hourly rate. Calculate your hourly wage based on your annual salary, weekly hours, and working weeks per year.",
    iconBg: "bg-purple-500/10 dark:bg-purple-500/15 backdrop-blur-sm border border-purple-400/30 dark:border-purple-400/40",
    iconColor: "text-purple-600 dark:text-purple-400",
    iconPath: "M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1",
    category: "Financial",
    priority: 0.8
  },
  {
    href: "/hourly-to-salary-calculator",
    title: "Hourly to Salary Calculator",
    description: "Convert your hourly wage to annual salary. Calculate your yearly earnings based on your hourly rate, weekly hours, and working weeks per year.",
    iconBg: "bg-orange-500/10 dark:bg-orange-500/15 backdrop-blur-sm border border-orange-400/30 dark:border-orange-400/40",
    iconColor: "text-orange-600 dark:text-orange-400",
    iconPath: "M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1",
    category: "Financial",
    priority: 0.8
  },
  {
    href: "/overtime-calculator",
    title: "Overtime Calculator",
    description: "Calculate your overtime pay including time and a half (1.5x) and double time (2x) rates. Perfect for tracking complex overtime scenarios.",
    iconBg: "bg-red-500/10 dark:bg-red-500/15 backdrop-blur-sm border border-red-400/30 dark:border-red-400/40",
    iconColor: "text-red-600 dark:text-red-400",
    iconPath: "M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0zM9 7H7a2 2 0 00-2 2v6a2 2 0 002 2h2m0-8h6m-6 0V7a2 2 0 012-2h2a2 2 0 012 2v2m-6 4h6m-6 0v2a2 2 0 002 2h2a2 2 0 002-2v-2",
    category: "Financial",
    priority: 0.8
  },
  {
    href: "/mortgage-calculator",
    title: "Mortgage Calculator",
    description: "Calculate mortgage payments, interest rates, and amortization schedules. Professional home loan calculator with PMI, taxes, and payment strategies.",
    iconBg: "bg-emerald-500/10 dark:bg-emerald-500/15 backdrop-blur-sm border border-emerald-400/30 dark:border-emerald-400/40",
    iconColor: "text-emerald-600 dark:text-emerald-400",
    iconPath: "M3 12l2-2m0 0l7-7 7 7M5 10v10a1 1 0 001 1h3m10-11l2 2m-2-2v10a1 1 0 01-1 1h-3m-6 0a1 1 0 001-1v-4a1 1 0 011-1h2a1 1 0 011 1v4a1 1 0 001 1m-6 0h6",
    category: "Financial",
    priority: 0.8
  },
  {
    href: "/china-salary-calculator",
    title: "China Salary Calculator",
    description: "中国工资计算器 - 五险一金缴存计算器。计算扣除五险一金和个人所得税后的到手工资。",
    iconBg: "bg-red-500/10 dark:bg-red-500/15 backdrop-blur-sm border border-red-400/30 dark:border-red-400/40",
    iconColor: "text-red-600 dark:text-red-400",
    iconPath: "M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1M9 11h6M9 15h6",
    category: "Financial",
    priority: 0.8
  }
];

export const getCategoryStyle = (category: Category) => {
  switch (category) {
    case 'Financial':
      return 'bg-green-100 dark:bg-green-900 text-green-800 dark:text-green-100 border border-green-200 dark:border-green-700';
    case 'Health':
      return 'bg-blue-100 dark:bg-blue-900 text-blue-800 dark:text-blue-100 border border-blue-200 dark:border-blue-700';
    case 'Math':
      return 'bg-purple-100 dark:bg-purple-900 text-purple-800 dark:text-purple-100 border border-purple-200 dark:border-purple-700';
    case 'Other':
      return 'bg-gray-100 dark:bg-gray-700 text-gray-800 dark:text-gray-100 border border-gray-200 dark:border-gray-600';
    default:
      return 'bg-gray-100 dark:bg-gray-700 text-gray-800 dark:text-gray-100 border border-gray-200 dark:border-gray-600';
  }
};
