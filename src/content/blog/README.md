# Blog Content Management System

This directory contains the new unified blog content management system for the Calc9 project.

## Structure

### 📁 Directory Layout
```
src/content/blog/
├── README.md                                    # This file
├── index.ts                                     # Content components registry
└── [blog-post-slug].tsx                        # Individual blog content components
```

### 🏗️ Architecture

#### 1. **Unified Blog Layout** (`/src/components/BlogLayout.tsx`)
- Provides consistent header, footer, and navigation
- Automatically generates article meta information (category, date, read time)
- Dynamically displays related calculators with proper icons
- Handles all common blog page elements

#### 2. **Content Components** (`/src/content/blog/[slug].tsx`)
- Pure content components containing only the article body
- No layout, header, or footer concerns
- Focus purely on the article content and structure
- Reusable and maintainable

#### 3. **Content Registry** (`/src/content/blog/index.ts`)
- Central registry for all blog content components
- Easy management and imports
- Type-safe component mapping

## Creating a New Blog Post

### Step 1: Add Blog Data
Update `/src/data/blog.ts`:

```typescript
export const blogPosts: Record<string, BlogPost> = {
  // ... existing posts
  'your-new-blog-post-slug': {
    slug: 'your-new-blog-post-slug',
    title: 'Your Blog Post Title',
    description: 'Brief description for SEO and previews',
    keywords: 'keyword1, keyword2, keyword3',
    date: '2025-07-07',
    readTime: '5 min read',
    category: 'Health', // or 'Finance', 'Construction', etc.
    priority: 0.8,
    relatedCalculators: ['calculator-slug-1', 'calculator-slug-2']
  }
};
```

### Step 2: Create Content Component
Create `/src/content/blog/your-new-blog-post-slug.tsx`:

```tsx
export default function YourBlogPostContent() {
  return (
    <>
      <section className="mb-8">
        <h2 className="text-2xl font-bold text-gray-800 dark:text-white mb-4">
          Section Title
        </h2>
        <p className="text-gray-700 dark:text-gray-300 mb-4">
          Your content here...
        </p>
      </section>
      
      {/* Add more sections as needed */}
    </>
  );
}
```

### Step 3: Register Component
Update `/src/content/blog/index.ts`:

```typescript
import YourBlogPostContent from './your-new-blog-post-slug';

export const blogContentComponents = {
  'your-new-blog-post-slug': YourBlogPostContent,
  // ... existing components
};
```

## Content Guidelines

### 🎨 Styling Classes
Use these predefined classes for consistent styling:

- **Headings**: `text-2xl font-bold text-gray-800 dark:text-white mb-4`
- **Body Text**: `text-gray-700 dark:text-gray-300 mb-4`
- **Info Boxes**: `bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-700 rounded-lg p-6`
- **Warning Boxes**: `bg-yellow-50 dark:bg-yellow-900/20 border border-yellow-200 dark:border-yellow-700 rounded-lg p-6`
- **Success Boxes**: `bg-green-50 dark:bg-green-900/20 border border-green-200 dark:border-green-700 rounded-lg p-6`

### 📝 Content Structure
Follow this structure for consistency:

1. **Introduction Section** - Brief overview of the topic
2. **Main Content Sections** - 3-7 sections covering different aspects
3. **Practical Examples** - Code snippets, calculations, or demos
4. **Call-to-Action** - Link to related calculators
5. **Conclusion** - Summary and takeaways

### 🔗 Related Calculators
The `relatedCalculators` array should contain calculator slugs (without leading slash):
- ✅ `'sleep-calculator'`
- ✅ `'bmi-calculator'`
- ❌ `'/sleep-calculator'`

## Benefits of This System

### ✅ Advantages
- **Consistent Layout**: All blog posts use the same header, footer, and navigation
- **Easy Maintenance**: Content changes don't require layout updates
- **Automatic Features**: Related calculators, meta info, and navigation are handled automatically
- **Type Safety**: TypeScript ensures proper component registration
- **SEO Optimized**: Consistent meta tags and structured data
- **Dark Mode**: Full dark mode support built-in

### 🔄 Migration Path
Existing blog posts can be gradually migrated:
1. Extract content from existing page components
2. Create new content component
3. Register in the content registry
4. Remove old page directory

### 🚀 Future Enhancements
- **MDX Support**: Could be added for markdown-based content
- **Content Categories**: Automatic category-based styling
- **Reading Progress**: Progress bars and reading time estimates
- **Social Sharing**: Automatic social media sharing buttons

## Example: Sleep Calculator Blog Post

The sleep calculator blog post (`optimize-your-sleep-schedule-with-science-based-calculations`) demonstrates the new system:

- **Clean Content Component**: Only contains article sections
- **Automatic Layout**: Header, navigation, and footer handled by `BlogLayout`
- **Related Calculators**: Automatically displays sleep-calculator, bmi-calculator, etc.
- **Responsive Design**: Works perfectly on all devices
- **SEO Optimized**: Proper meta tags and structured data