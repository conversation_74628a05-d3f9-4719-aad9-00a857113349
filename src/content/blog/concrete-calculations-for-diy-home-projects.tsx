import Link from 'next/link';

export default function ConcreteBlogContent() {
  return (
    <>
      <section className="mb-8">
        <h2 className="text-2xl font-bold text-gray-800 dark:text-white mb-4">Why Accurate Concrete Calculations Matter</h2>
        <p className="text-gray-700 dark:text-gray-300 mb-4">
          Getting your concrete calculations right is crucial for both cost and project success. Order too little, and you&apos;ll have visible seams and potential structural weaknesses. Order too much, and you&apos;ll waste money and need to dispose of excess material.
        </p>
        <div className="bg-yellow-50 dark:bg-yellow-900/30 border border-yellow-200 dark:border-yellow-600 rounded-lg p-4">
          <p className="text-yellow-800 dark:text-yellow-300 text-sm">
            <strong>Cost Impact:</strong> Ready-mix concrete costs $100-150 per cubic yard in 2024. A miscalculation of just half a yard can cost you $50-75 in wasted materials or additional delivery fees.
          </p>
        </div>
      </section>

      <section className="mb-8">
        <h2 className="text-2xl font-bold text-gray-800 dark:text-white mb-4">Basic Concrete Volume Calculations</h2>
        <p className="text-gray-700 dark:text-gray-300 mb-4">
          All concrete calculations start with determining volume. Here are the essential formulas for common shapes:
        </p>
        
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-6">
          <div className="bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-700 rounded-lg p-4">
            <h3 className="text-lg font-semibold text-blue-800 dark:text-blue-200 mb-3">📐 Rectangular Areas</h3>
            <p className="text-blue-700 dark:text-blue-300 text-sm mb-2">
              <strong>Formula:</strong> Length × Width × Thickness
            </p>
            <p className="text-blue-700 dark:text-blue-300 text-sm">
              Perfect for patios, driveways, sidewalks, and slabs
            </p>
          </div>
          
          <div className="bg-green-50 dark:bg-green-900/20 border border-green-200 dark:border-green-700 rounded-lg p-4">
            <h3 className="text-lg font-semibold text-green-800 dark:text-green-200 mb-3">⭕ Circular Areas</h3>
            <p className="text-green-700 dark:text-green-300 text-sm mb-2">
              <strong>Formula:</strong> π × Radius² × Thickness
            </p>
            <p className="text-green-700 dark:text-green-300 text-sm">
              For round patios, fire pits, and decorative elements
            </p>
          </div>
          
          <div className="bg-purple-50 dark:bg-purple-900/20 border border-purple-200 dark:border-purple-700 rounded-lg p-4">
            <h3 className="text-lg font-semibold text-purple-800 dark:text-purple-200 mb-3">🏗️ Footings</h3>
            <p className="text-purple-700 dark:text-purple-300 text-sm mb-2">
              <strong>Formula:</strong> Length × Width × Depth
            </p>
            <p className="text-purple-700 dark:text-purple-300 text-sm">
              Foundation footings and support structures
            </p>
          </div>
          
          <div className="bg-orange-50 dark:bg-orange-900/20 border border-orange-200 dark:border-orange-700 rounded-lg p-4">
            <h3 className="text-lg font-semibold text-orange-800 dark:text-orange-200 mb-3">🪜 Steps</h3>
            <p className="text-orange-700 dark:text-orange-300 text-sm mb-2">
              <strong>Formula:</strong> W × L × H (per step)
            </p>
            <p className="text-orange-700 dark:text-orange-300 text-sm">
              Calculate each step individually, then sum totals
            </p>
          </div>
        </div>
      </section>

      <section className="mb-8">
        <h2 className="text-2xl font-bold text-gray-800 dark:text-white mb-4">Unit Conversions and Measurements</h2>
        <p className="text-gray-700 dark:text-gray-300 mb-4">
          Concrete is sold by the cubic yard, but most DIY measurements are in feet and inches. Here are essential conversions:
        </p>
        
        <div className="bg-gray-50 dark:bg-gray-900/50 border border-gray-200 dark:border-gray-600 rounded-lg p-6 mb-4">
          <h3 className="text-lg font-semibold text-gray-800 dark:text-gray-200 mb-3">📏 Key Conversions</h3>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
            <div>
              <p className="font-medium mb-2">Volume Conversions:</p>
              <ul className="space-y-1 text-gray-700 dark:text-gray-300">
                <li>• 1 cubic yard = 27 cubic feet</li>
                <li>• 1 cubic foot = 1,728 cubic inches</li>
                <li>• 1 cubic yard = 46,656 cubic inches</li>
              </ul>
            </div>
            <div>
              <p className="font-medium mb-2">Common Thicknesses:</p>
              <ul className="space-y-1 text-gray-700 dark:text-gray-300">
                <li>• 4 inches = 0.33 feet</li>
                <li>• 6 inches = 0.5 feet</li>
                <li>• 8 inches = 0.67 feet</li>
              </ul>
            </div>
          </div>
        </div>
      </section>

      <section className="mb-8">
        <h2 className="text-2xl font-bold text-gray-800 dark:text-white mb-4">Common DIY Project Calculations</h2>
        
        <div className="space-y-6">
          <div className="bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-700 rounded-lg p-6">
            <h3 className="text-lg font-semibold text-blue-800 dark:text-blue-200 mb-3">🏡 Patio Slab Example</h3>
            <p className="text-blue-700 dark:text-blue-300 text-sm mb-3">
              <strong>Project:</strong> 12 ft × 16 ft patio, 4 inches thick
            </p>
            <div className="text-blue-700 dark:text-blue-300 text-sm space-y-1">
              <p><strong>Step 1:</strong> Calculate volume in cubic feet</p>
              <p>12 ft × 16 ft × 0.33 ft = 63.36 cubic feet</p>
              <p><strong>Step 2:</strong> Convert to cubic yards</p>
              <p>63.36 ÷ 27 = 2.35 cubic yards</p>
              <p><strong>Step 3:</strong> Add 10% waste factor</p>
              <p>2.35 × 1.10 = <strong>2.6 cubic yards needed</strong></p>
            </div>
          </div>
          
          <div className="bg-green-50 dark:bg-green-900/20 border border-green-200 dark:border-green-700 rounded-lg p-6">
            <h3 className="text-lg font-semibold text-green-800 dark:text-green-200 mb-3">🚗 Driveway Example</h3>
            <p className="text-green-700 dark:text-green-300 text-sm mb-3">
              <strong>Project:</strong> 10 ft × 30 ft driveway, 6 inches thick
            </p>
            <div className="text-green-700 dark:text-green-300 text-sm space-y-1">
              <p><strong>Step 1:</strong> Calculate volume</p>
              <p>10 ft × 30 ft × 0.5 ft = 150 cubic feet</p>
              <p><strong>Step 2:</strong> Convert to cubic yards</p>
              <p>150 ÷ 27 = 5.56 cubic yards</p>
              <p><strong>Step 3:</strong> Add 15% waste factor (larger project)</p>
              <p>5.56 × 1.15 = <strong>6.4 cubic yards needed</strong></p>
            </div>
          </div>
        </div>
      </section>

      <section className="mb-8">
        <h2 className="text-2xl font-bold text-gray-800 dark:text-white mb-4">Waste Factors and Safety Margins</h2>
        <p className="text-gray-700 dark:text-gray-300 mb-4">
          Always order more concrete than your exact calculations. Here&apos;s why and how much:
        </p>
        
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          <div className="bg-yellow-50 dark:bg-yellow-900/30 border border-yellow-200 dark:border-yellow-600 rounded-lg p-4 text-center">
            <h3 className="text-lg font-semibold text-yellow-800 dark:text-yellow-200 mb-2">🏠 Small Projects</h3>
            <p className="text-yellow-700 dark:text-yellow-300 text-sm mb-2"><strong>+5-10%</strong></p>
            <p className="text-yellow-700 dark:text-yellow-300 text-xs">Patios, small slabs under 2 yards</p>
          </div>
          
          <div className="bg-orange-50 dark:bg-orange-900/20 border border-orange-200 dark:border-orange-700 rounded-lg p-4 text-center">
            <h3 className="text-lg font-semibold text-orange-800 dark:text-orange-200 mb-2">🏗️ Medium Projects</h3>
            <p className="text-orange-700 dark:text-orange-300 text-sm mb-2"><strong>+10-15%</strong></p>
            <p className="text-orange-700 dark:text-orange-300 text-xs">Driveways, larger slabs 2-10 yards</p>
          </div>
          
          <div className="bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-700 rounded-lg p-4 text-center">
            <h3 className="text-lg font-semibold text-red-800 dark:text-red-200 mb-2">🏭 Complex Projects</h3>
            <p className="text-red-700 dark:text-red-300 text-sm mb-2"><strong>+15-20%</strong></p>
            <p className="text-red-700 dark:text-red-300 text-xs">Steps, complex shapes, footings</p>
          </div>
        </div>
      </section>

      <section className="mb-8">
        <h2 className="text-2xl font-bold text-gray-800 dark:text-white mb-4">Concrete Mix Types and Strength</h2>
        <p className="text-gray-700 dark:text-gray-300 mb-4">
          Different projects require different concrete strengths, measured in PSI (pounds per square inch):
        </p>
        
        <div className="overflow-x-auto">
          <table className="w-full border-collapse border border-gray-300 dark:border-gray-600">
            <thead>
              <tr className="bg-gray-100 dark:bg-gray-700">
                <th className="border border-gray-300 dark:border-gray-600 px-4 py-2 text-left">Application</th>
                <th className="border border-gray-300 dark:border-gray-600 px-4 py-2 text-center">PSI Rating</th>
                <th className="border border-gray-300 dark:border-gray-600 px-4 py-2 text-left">Best For</th>
              </tr>
            </thead>
            <tbody className="text-sm">
              <tr>
                <td className="border border-gray-300 dark:border-gray-600 px-4 py-2 font-medium">Light Duty</td>
                <td className="border border-gray-300 dark:border-gray-600 px-4 py-2 text-center">2,500 PSI</td>
                <td className="border border-gray-300 dark:border-gray-600 px-4 py-2">Walkways, patios, light traffic areas</td>
              </tr>
              <tr>
                <td className="border border-gray-300 dark:border-gray-600 px-4 py-2 font-medium">Standard</td>
                <td className="border border-gray-300 dark:border-gray-600 px-4 py-2 text-center">3,000 PSI</td>
                <td className="border border-gray-300 dark:border-gray-600 px-4 py-2">Driveways, garage floors, most residential use</td>
              </tr>
              <tr>
                <td className="border border-gray-300 dark:border-gray-600 px-4 py-2 font-medium">Heavy Duty</td>
                <td className="border border-gray-300 dark:border-gray-600 px-4 py-2 text-center">4,000+ PSI</td>
                <td className="border border-gray-300 dark:border-gray-600 px-4 py-2">Foundations, structural elements, high traffic</td>
              </tr>
            </tbody>
          </table>
        </div>
      </section>

      <section className="mb-8">
        <h2 className="text-2xl font-bold text-gray-800 dark:text-white mb-4">Calculate Your Concrete Needs</h2>
        <div className="bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-700 rounded-lg p-6">
          <h3 className="text-lg font-semibold text-blue-800 dark:text-blue-200 mb-2">🧮 Ready to Calculate?</h3>
          <p className="text-blue-700 dark:text-blue-300 text-sm mb-4">
            Use our concrete calculator to determine exactly how much concrete you need for your project. Input your dimensions and get instant results with waste factors included.
          </p>
          <Link 
            href="/concrete-calculator" 
            className="inline-block bg-blue-600 dark:bg-blue-500 text-white px-6 py-3 rounded-lg font-medium hover:bg-blue-700 dark:hover:bg-blue-600 transition-colors"
          >
            Calculate Concrete Needed
          </Link>
        </div>
      </section>

      <section className="mb-8">
        <h2 className="text-2xl font-bold text-gray-800 dark:text-white mb-4">Cost Estimation</h2>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          <div className="bg-green-50 dark:bg-green-900/20 border border-green-200 dark:border-green-700 rounded-lg p-4">
            <h3 className="text-lg font-semibold text-green-800 dark:text-green-200 mb-3">🚛 Ready-Mix Concrete</h3>
            <ul className="text-sm text-green-700 dark:text-green-300 space-y-1">
              <li>• <strong>$100-150/cubic yard</strong> (2024 prices)</li>
              <li>• Minimum delivery: Usually 1-2 yards</li>
              <li>• Delivery fee: $75-150</li>
              <li>• Best for projects over 2 cubic yards</li>
            </ul>
          </div>
          <div className="bg-orange-50 dark:bg-orange-900/20 border border-orange-200 dark:border-orange-700 rounded-lg p-4">
            <h3 className="text-lg font-semibold text-orange-800 dark:text-orange-200 mb-3">🛍️ Bagged Concrete</h3>
            <ul className="text-sm text-orange-700 dark:text-orange-300 space-y-1">
              <li>• <strong>$5-8 per 80lb bag</strong></li>
              <li>• 40 bags = 1 cubic yard</li>
              <li>• Total: $200-320/cubic yard</li>
              <li>• Best for small projects under 1 yard</li>
            </ul>
          </div>
        </div>
      </section>

      <section className="mb-8">
        <h2 className="text-2xl font-bold text-gray-800 dark:text-white mb-4">Safety and Building Codes</h2>
        <div className="bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-700 rounded-lg p-6">
          <h3 className="text-lg font-semibold text-red-800 dark:text-red-200 mb-3">⚠️ Important Safety Reminders</h3>
          <ul className="text-red-700 dark:text-red-300 text-sm space-y-2">
            <li>• <strong>Check local building codes</strong> - Permits may be required for certain projects</li>
            <li>• <strong>Call 811</strong> - Mark utilities before digging for footings or foundations</li>
            <li>• <strong>Wear protective equipment</strong> - Concrete is caustic and can cause burns</li>
            <li>• <strong>Plan for weather</strong> - Avoid pouring in extreme temperatures</li>
            <li>• <strong>Have help available</strong> - Concrete must be placed quickly once delivered</li>
          </ul>
        </div>
      </section>

      <section className="mb-8">
        <h2 className="text-2xl font-bold text-gray-800 dark:text-white mb-4">The Bottom Line</h2>
        <p className="text-gray-700 dark:text-gray-300 mb-4">
          Accurate concrete calculations are the foundation of any successful DIY project. Take time to measure carefully, understand your project requirements, and always include a safety margin in your orders.
        </p>
        <p className="text-gray-700 dark:text-gray-300">
          Remember that concrete work moves fast once you start - having the right amount of material ready is crucial for a professional-looking finish. When in doubt, consult with local suppliers who can provide project-specific advice.
        </p>
      </section>
    </>
  );
}