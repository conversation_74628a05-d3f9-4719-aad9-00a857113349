import Link from 'next/link';

export default function PayrollCalculatorSuiteGuideBlogContent() {
  return (
    <>
      <section className="mb-8">
        <h2 className="text-2xl font-bold text-gray-800 dark:text-white mb-4">Essential Payroll Calculators</h2>
        <p className="text-gray-700 dark:text-gray-300 mb-4">
          Whether you&apos;re an employee trying to understand your pay, an employer calculating wages, or an HR professional managing payroll, our specialized payroll calculators provide all the tools you need. This collection of four dedicated calculators covers every aspect of payroll calculation, from basic overtime to complex salary conversions.
        </p>
        <div className="bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-700 rounded-lg p-6">
          <h3 className="text-lg font-semibold text-blue-800 dark:text-blue-200 mb-2">🧮 Four Essential Calculators</h3>
          <ul className="text-blue-700 dark:text-blue-300 text-sm space-y-1">
            <li>• <strong>Time and a Half Calculator:</strong> Calculate overtime pay at 1.5x rate</li>
            <li>• <strong>Salary to Hourly Calculator:</strong> Convert annual salary to hourly rate</li>
            <li>• <strong>Hourly to Salary Calculator:</strong> Convert hourly wage to annual salary</li>
            <li>• <strong>Overtime Calculator:</strong> Handle complex overtime with double time</li>
          </ul>
        </div>
      </section>

      <section className="mb-8">
        <h2 className="text-2xl font-bold text-gray-800 dark:text-white mb-4">Time and a Half Calculator</h2>
        <p className="text-gray-700 dark:text-gray-300 mb-4">
          The cornerstone of overtime calculation, our Time and a Half Calculator helps you determine exactly how much you&apos;ll earn for those extra hours. Under federal law, non-exempt employees must receive overtime pay of at least 1.5 times their regular rate for hours worked over 40 in a workweek.
        </p>
        
        <div className="bg-green-50 dark:bg-green-900/20 border border-green-200 dark:border-green-700 rounded-lg p-6 mb-6">
          <h3 className="text-lg font-semibold text-green-800 dark:text-green-200 mb-3">💡 How Time and a Half Works</h3>
          <div className="text-green-700 dark:text-green-300 text-sm space-y-2">
            <p><strong>Example Calculation:</strong></p>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mt-3">
              <div>
                <ul className="space-y-1">
                  <li>• Regular rate: $20/hour</li>
                  <li>• Overtime rate: $30/hour (1.5x)</li>
                  <li>• Regular hours: 40</li>
                  <li>• Overtime hours: 10</li>
                </ul>
              </div>
              <div className="bg-green-100 dark:bg-green-900/30 border border-green-200 dark:border-green-700 p-3 rounded">
                <p className="font-medium text-green-800 dark:text-green-200">Weekly Pay:</p>
                <p className="text-green-700 dark:text-green-300">Regular: $800 + Overtime: $300 = $1,100</p>
              </div>
            </div>
          </div>
        </div>

        <div className="bg-gradient-to-r from-green-50 to-blue-50 dark:from-green-900/20 dark:to-blue-900/20 border border-green-200 dark:border-green-700 rounded-lg p-6 text-center mb-6">
          <h3 className="text-lg font-semibold text-green-800 dark:text-green-200 mb-3">🕐 Calculate Your Overtime</h3>
          <p className="text-green-700 dark:text-green-300 text-sm mb-4">
            Get instant results with our dedicated time and a half calculator, perfect for employees and payroll professionals.
          </p>
          <Link 
            href="/time-and-a-half-calculator" 
            className="inline-block bg-green-600 hover:bg-green-700 text-white font-medium py-2 px-6 rounded-lg transition-colors duration-200"
          >
            Time and a Half Calculator →
          </Link>
        </div>
      </section>

      <section className="mb-8">
        <h2 className="text-2xl font-bold text-gray-800 dark:text-white mb-4">Salary to Hourly Calculator</h2>
        <p className="text-gray-700 dark:text-gray-300 mb-4">
          Converting your annual salary to an hourly rate is essential for comparing job offers, understanding your true earning power, and planning your finances. Our calculator considers your working hours and vacation time to give you an accurate hourly equivalent.
        </p>
        
        <div className="bg-purple-50 dark:bg-purple-900/20 border border-purple-200 dark:border-purple-700 rounded-lg p-6 mb-6">
          <h3 className="text-lg font-semibold text-purple-800 dark:text-purple-200 mb-3">📊 Salary Breakdown</h3>
          <div className="text-purple-700 dark:text-purple-300 text-sm space-y-2">
            <p><strong>Standard Calculation:</strong> Annual Salary ÷ (Hours per Week × Weeks per Year)</p>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mt-3">
              <div>
                <p className="font-medium mb-1">$52,000 Salary</p>
                <ul className="space-y-1 text-xs">
                  <li>• 40 hours/week</li>
                  <li>• 52 weeks/year</li>
                  <li>• 2,080 total hours</li>
                </ul>
              </div>
              <div>
                <p className="font-medium mb-1">Calculation</p>
                <ul className="space-y-1 text-xs">
                  <li>• $52,000 ÷ 2,080</li>
                  <li>• = $25.00/hour</li>
                </ul>
              </div>
              <div className="bg-purple-100 dark:bg-purple-900/30 border border-purple-200 dark:border-purple-700 p-3 rounded">
                <p className="font-medium text-purple-800 dark:text-purple-200">Result:</p>
                <p className="text-purple-700 dark:text-purple-300">$25.00 per hour</p>
              </div>
            </div>
          </div>
        </div>

        <div className="bg-gradient-to-r from-purple-50 to-indigo-50 dark:from-purple-900/20 dark:to-indigo-900/20 border border-purple-200 dark:border-purple-700 rounded-lg p-6 text-center mb-6">
          <h3 className="text-lg font-semibold text-purple-800 dark:text-purple-200 mb-3">💼 Convert Your Salary</h3>
          <p className="text-purple-700 dark:text-purple-300 text-sm mb-4">
            Understand your true hourly value and compare different compensation packages effectively.
          </p>
          <Link 
            href="/salary-to-hourly-calculator" 
            className="inline-block bg-purple-600 hover:bg-purple-700 text-white font-medium py-2 px-6 rounded-lg transition-colors duration-200"
          >
            Salary to Hourly Calculator →
          </Link>
        </div>
      </section>

      <section className="mb-8">
        <h2 className="text-2xl font-bold text-gray-800 dark:text-white mb-4">Hourly to Salary Calculator</h2>
        <p className="text-gray-700 dark:text-gray-300 mb-4">
          Planning to negotiate a salary based on your hourly rate? Our Hourly to Salary Calculator helps you project your annual earnings and understand the financial impact of different work schedules and vacation time.
        </p>
        
        <div className="bg-orange-50 dark:bg-orange-900/20 border border-orange-200 dark:border-orange-700 rounded-lg p-6 mb-6">
          <h3 className="text-lg font-semibold text-orange-800 dark:text-orange-200 mb-3">💰 Annual Projection</h3>
          <div className="text-orange-700 dark:text-orange-300 text-sm space-y-2">
            <p><strong>Formula:</strong> Hourly Rate × Hours per Week × Weeks per Year</p>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mt-3">
              <div>
                <p className="font-medium mb-2">Different Scenarios:</p>
                <ul className="space-y-1">
                  <li>• <strong>Full-time:</strong> $25/hr × 40 hrs × 52 weeks = $52,000</li>
                  <li>• <strong>Part-time:</strong> $25/hr × 20 hrs × 52 weeks = $26,000</li>
                  <li>• <strong>With 2 weeks vacation:</strong> $25/hr × 40 hrs × 50 weeks = $50,000</li>
                </ul>
              </div>
              <div className="bg-orange-100 dark:bg-orange-900/30 border border-orange-200 dark:border-orange-700 p-3 rounded">
                <p className="font-medium text-orange-800 dark:text-orange-200">Key Factors:</p>
                <ul className="text-orange-700 dark:text-orange-300 text-xs space-y-1">
                  <li>• Vacation time reduces total weeks</li>
                  <li>• Overtime increases total earnings</li>
                  <li>• Benefits add to total compensation</li>
                </ul>
              </div>
            </div>
          </div>
        </div>

        <div className="bg-gradient-to-r from-orange-50 to-red-50 dark:from-orange-900/20 dark:to-red-900/20 border border-orange-200 dark:border-orange-700 rounded-lg p-6 text-center mb-6">
          <h3 className="text-lg font-semibold text-orange-800 dark:text-orange-200 mb-3">📈 Project Your Earnings</h3>
          <p className="text-orange-700 dark:text-orange-300 text-sm mb-4">
            Calculate your potential annual salary based on your hourly rate and work schedule.
          </p>
          <Link 
            href="/hourly-to-salary-calculator" 
            className="inline-block bg-orange-600 hover:bg-orange-700 text-white font-medium py-2 px-6 rounded-lg transition-colors duration-200"
          >
            Hourly to Salary Calculator →
          </Link>
        </div>
      </section>

      <section className="mb-8">
        <h2 className="text-2xl font-bold text-gray-800 dark:text-white mb-4">Overtime Calculator with Double Time</h2>
        <p className="text-gray-700 dark:text-gray-300 mb-4">
          For complex overtime scenarios involving both time and a half (1.5x) and double time (2x) rates, our advanced Overtime Calculator handles it all. This is particularly useful for industries with holiday pay, long shifts, or union contracts that specify premium rates.
        </p>
        
        <div className="bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-700 rounded-lg p-6 mb-6">
          <h3 className="text-lg font-semibold text-red-800 dark:text-red-200 mb-3">⚡ Complex Overtime Scenarios</h3>
          <div className="text-red-700 dark:text-red-300 text-sm space-y-2">
            <p><strong>When Double Time Applies:</strong></p>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mt-3">
              <div>
                <ul className="space-y-1">
                  <li>• <strong>Holidays:</strong> Christmas, New Year&apos;s Day, etc.</li>
                  <li>• <strong>Long shifts:</strong> Over 12 hours in some states</li>
                  <li>• <strong>Seventh consecutive day:</strong> In certain states</li>
                  <li>• <strong>Union contracts:</strong> Specified premium rates</li>
                </ul>
              </div>
              <div className="bg-red-100 dark:bg-red-900/30 border border-red-200 dark:border-red-700 p-3 rounded">
                <p className="font-medium text-red-800 dark:text-red-200">Example Calculation:</p>
                <div className="text-red-700 dark:text-red-300 text-xs space-y-1">
                  <p>$25/hr base rate:</p>
                  <p>• Regular: 40 hrs × $25 = $1,000</p>
                  <p>• Overtime: 10 hrs × $37.50 = $375</p>
                  <p>• Double time: 4 hrs × $50 = $200</p>
                  <p><strong>Total: $1,575</strong></p>
                </div>
              </div>
            </div>
          </div>
        </div>

        <div className="bg-gradient-to-r from-red-50 to-pink-50 dark:from-red-900/20 dark:to-pink-900/20 border border-red-200 dark:border-red-700 rounded-lg p-6 text-center mb-6">
          <h3 className="text-lg font-semibold text-red-800 dark:text-red-200 mb-3">⚡ Advanced Overtime Calculator</h3>
          <p className="text-red-700 dark:text-red-300 text-sm mb-4">
            Handle complex overtime scenarios with multiple pay rates and premium time calculations.
          </p>
          <Link 
            href="/overtime-calculator" 
            className="inline-block bg-red-600 hover:bg-red-700 text-white font-medium py-2 px-6 rounded-lg transition-colors duration-200"
          >
            Overtime Calculator →
          </Link>
        </div>
      </section>

      <section className="mb-8">
        <h2 className="text-2xl font-bold text-gray-800 dark:text-white mb-4">Legal Framework and Compliance</h2>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          <div className="bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-700 rounded-lg p-6">
            <h3 className="text-lg font-semibold text-blue-800 dark:text-blue-200 mb-3">🏛️ Federal Laws</h3>
            <div className="text-blue-700 dark:text-blue-300 text-sm space-y-2">
              <p><strong>Fair Labor Standards Act (FLSA):</strong></p>
              <ul className="space-y-1 ml-4">
                <li>• Overtime pay required after 40 hours/week</li>
                <li>• Minimum 1.5x regular rate for overtime</li>
                <li>• Applies to non-exempt employees</li>
                <li>• No daily overtime requirement (federal level)</li>
              </ul>
            </div>
          </div>

          <div className="bg-green-50 dark:bg-green-900/20 border border-green-200 dark:border-green-700 rounded-lg p-6">
            <h3 className="text-lg font-semibold text-green-800 dark:text-green-200 mb-3">📋 State Variations</h3>
            <div className="text-green-700 dark:text-green-300 text-sm space-y-2">
              <p><strong>State-Specific Rules:</strong></p>
              <ul className="space-y-1 ml-4">
                <li>• <strong>California:</strong> Daily overtime after 8 hours</li>
                <li>• <strong>Alaska:</strong> Daily overtime after 8 hours</li>
                <li>• <strong>Colorado:</strong> Daily overtime after 12 hours</li>
                <li>• <strong>Nevada:</strong> Daily overtime after 8 hours</li>
              </ul>
            </div>
          </div>
        </div>
      </section>

      <section className="mb-8">
        <h2 className="text-2xl font-bold text-gray-800 dark:text-white mb-4">Industry-Specific Applications</h2>
        <div className="space-y-6">
          <div className="bg-gray-50 dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-lg p-6">
            <h3 className="text-lg font-semibold text-gray-800 dark:text-gray-200 mb-3">🏥 Healthcare</h3>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
              <div>
                <p className="font-medium mb-2">Common Scenarios:</p>
                <ul className="space-y-1 text-gray-600 dark:text-gray-400">
                  <li>• 12-hour shifts with overtime</li>
                  <li>• On-call pay calculations</li>
                  <li>• Weekend and holiday premiums</li>
                  <li>• Shift differentials</li>
                </ul>
              </div>
              <div>
                <p className="font-medium mb-2">Example:</p>
                <div className="bg-blue-50 dark:bg-blue-900/20 p-3 rounded text-gray-600 dark:text-gray-400">
                  <p>Nurse: $35/hr base</p>
                  <p>• 36 regular hours: $1,260</p>
                  <p>• 12 overtime hours: $630</p>
                  <p>• 4 holiday double-time: $280</p>
                  <p><strong>Total: $2,170</strong></p>
                </div>
              </div>
            </div>
          </div>

          <div className="bg-gray-50 dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-lg p-6">
            <h3 className="text-lg font-semibold text-gray-800 dark:text-gray-200 mb-3">🏗️ Construction</h3>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
              <div>
                <p className="font-medium mb-2">Typical Patterns:</p>
                <ul className="space-y-1 text-gray-600 dark:text-gray-400">
                  <li>• 10-hour days, 4-day weeks</li>
                  <li>• Seasonal overtime spikes</li>
                  <li>• Weather-related schedule changes</li>
                  <li>• Union contract provisions</li>
                </ul>
              </div>
              <div>
                <p className="font-medium mb-2">Example:</p>
                <div className="bg-orange-50 dark:bg-orange-900/20 p-3 rounded text-gray-600 dark:text-gray-400">
                  <p>Electrician: $40/hr base</p>
                  <p>• 40 regular hours: $1,600</p>
                  <p>• 20 overtime hours: $1,200</p>
                  <p>• 8 double-time hours: $640</p>
                  <p><strong>Total: $3,440</strong></p>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      <section className="mb-8">
        <h2 className="text-2xl font-bold text-gray-800 dark:text-white mb-4">Best Practices for Employers</h2>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          <div>
            <h3 className="text-lg font-semibold text-gray-800 dark:text-gray-200 mb-3">📝 Documentation</h3>
            <ul className="text-gray-700 dark:text-gray-300 text-sm space-y-2">
              <li className="flex items-start space-x-2">
                <span className="text-blue-500 mt-1">•</span>
                <span><strong>Accurate Time Tracking:</strong> Use reliable time clock systems</span>
              </li>
              <li className="flex items-start space-x-2">
                <span className="text-blue-500 mt-1">•</span>
                <span><strong>Clear Policies:</strong> Document overtime authorization procedures</span>
              </li>
              <li className="flex items-start space-x-2">
                <span className="text-blue-500 mt-1">•</span>
                <span><strong>Regular Audits:</strong> Review payroll calculations regularly</span>
              </li>
              <li className="flex items-start space-x-2">
                <span className="text-blue-500 mt-1">•</span>
                <span><strong>Training:</strong> Educate managers on overtime rules</span>
              </li>
            </ul>
          </div>
          <div>
            <h3 className="text-lg font-semibold text-gray-800 dark:text-gray-200 mb-3">⚖️ Compliance</h3>
            <ul className="text-gray-700 dark:text-gray-300 text-sm space-y-2">
              <li className="flex items-start space-x-2">
                <span className="text-green-500 mt-1">•</span>
                <span><strong>Know Your State Laws:</strong> Understand local overtime requirements</span>
              </li>
              <li className="flex items-start space-x-2">
                <span className="text-green-500 mt-1">•</span>
                <span><strong>Exempt vs Non-Exempt:</strong> Properly classify employees</span>
              </li>
              <li className="flex items-start space-x-2">
                <span className="text-green-500 mt-1">•</span>
                <span><strong>Record Keeping:</strong> Maintain payroll records for required periods</span>
              </li>
              <li className="flex items-start space-x-2">
                <span className="text-green-500 mt-1">•</span>
                <span><strong>Legal Updates:</strong> Stay informed about law changes</span>
              </li>
            </ul>
          </div>
        </div>
      </section>

      <section className="mb-8">
        <h2 className="text-2xl font-bold text-gray-800 dark:text-white mb-4">Tips for Employees</h2>
        <div className="bg-yellow-50 dark:bg-yellow-900/20 border border-yellow-200 dark:border-yellow-700 rounded-lg p-6">
          <h3 className="text-lg font-semibold text-yellow-800 dark:text-yellow-200 mb-2">💡 Maximizing Your Overtime Benefits</h3>
          <div className="text-yellow-700 dark:text-yellow-300 text-sm space-y-2">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <p className="font-medium mb-2">Track Your Hours:</p>
                <ul className="space-y-1">
                  <li>• Keep personal time records</li>
                  <li>• Note start and end times</li>
                  <li>• Record meal breaks</li>
                  <li>• Document overtime authorization</li>
                </ul>
              </div>
              <div>
                <p className="font-medium mb-2">Know Your Rights:</p>
                <ul className="space-y-1">
                  <li>• Understand your exempt status</li>
                  <li>• Know your state&apos;s overtime laws</li>
                  <li>• Verify overtime rate calculations</li>
                  <li>• Report discrepancies promptly</li>
                </ul>
              </div>
            </div>
          </div>
        </div>
      </section>

      <section className="mb-8">
        <h2 className="text-2xl font-bold text-gray-800 dark:text-white mb-4">Using Our Payroll Calculators Effectively</h2>
        <p className="text-gray-700 dark:text-gray-300 mb-6">
          Our payroll calculators are designed to be user-friendly while providing professional-grade accuracy. Each calculator includes URL sharing capabilities, so you can save and share calculations with colleagues or for record-keeping.
        </p>
        
        <div className="bg-gradient-to-r from-blue-50 to-indigo-50 dark:from-blue-900/20 dark:to-indigo-900/20 border border-blue-200 dark:border-blue-700 rounded-lg p-6 text-center">
          <h3 className="text-lg font-semibold text-blue-800 dark:text-blue-200 mb-3">🔧 Individual Calculators</h3>
          <p className="text-blue-700 dark:text-blue-300 text-sm mb-4">
            Access each specialized calculator with optimized features for your specific needs.
          </p>
          <div className="grid grid-cols-1 md:grid-cols-4 gap-2">
            <Link 
              href="/time-and-a-half-calculator" 
              className="inline-block bg-green-600 hover:bg-green-700 text-white font-medium py-2 px-3 rounded-lg transition-colors duration-200 text-sm"
            >
              Time & Half
            </Link>
            <Link 
              href="/salary-to-hourly-calculator" 
              className="inline-block bg-purple-600 hover:bg-purple-700 text-white font-medium py-2 px-3 rounded-lg transition-colors duration-200 text-sm"
            >
              Salary to Hourly
            </Link>
            <Link 
              href="/hourly-to-salary-calculator" 
              className="inline-block bg-orange-600 hover:bg-orange-700 text-white font-medium py-2 px-3 rounded-lg transition-colors duration-200 text-sm"
            >
              Hourly to Salary
            </Link>
            <Link 
              href="/overtime-calculator" 
              className="inline-block bg-red-600 hover:bg-red-700 text-white font-medium py-2 px-3 rounded-lg transition-colors duration-200 text-sm"
            >
              Overtime
            </Link>
          </div>
        </div>
      </section>

      <section className="mb-8">
        <h2 className="text-2xl font-bold text-gray-800 dark:text-white mb-4">Additional Resources</h2>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <a 
            href="https://www.dol.gov/agencies/whd/flsa" 
            target="_blank" 
            rel="noopener noreferrer"
            className="block p-4 bg-gray-50 dark:bg-gray-800 hover:bg-gray-100 dark:hover:bg-gray-700 rounded-lg border border-gray-200 dark:border-gray-700 transition-colors"
          >
            <h3 className="font-medium text-gray-800 dark:text-gray-200 mb-2">🏛️ U.S. Department of Labor</h3>
            <p className="text-sm text-gray-600 dark:text-gray-400 mb-2">Official information about the Fair Labor Standards Act and overtime regulations</p>
            <span className="text-blue-500 text-sm">Visit dol.gov →</span>
          </a>
          
          <a 
            href="https://www.irs.gov/businesses/small-businesses-self-employed/payroll-taxes" 
            target="_blank" 
            rel="noopener noreferrer"
            className="block p-4 bg-gray-50 dark:bg-gray-800 hover:bg-gray-100 dark:hover:bg-gray-700 rounded-lg border border-gray-200 dark:border-gray-700 transition-colors"
          >
            <h3 className="font-medium text-gray-800 dark:text-gray-200 mb-2">📋 IRS Payroll Information</h3>
            <p className="text-sm text-gray-600 dark:text-gray-400 mb-2">Tax implications of overtime pay and payroll calculation requirements</p>
            <span className="text-blue-500 text-sm">Visit irs.gov →</span>
          </a>
        </div>
      </section>

      <section>
        <h2 className="text-2xl font-bold text-gray-800 dark:text-white mb-4">Related Financial Tools</h2>
        <p className="text-gray-700 dark:text-gray-300 mb-4">
          Explore our other financial calculators to optimize your financial planning:
        </p>
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          <Link href="/no-tax-overtime-calculator" className="block p-4 bg-gray-50 dark:bg-gray-800 hover:bg-gray-100 dark:hover:bg-gray-700 rounded-lg border border-gray-200 dark:border-gray-700 transition-colors">
            <h3 className="font-medium text-gray-800 dark:text-gray-200">No Tax Overtime Calculator</h3>
            <p className="text-sm text-gray-600 dark:text-gray-400">Calculate tax savings on overtime pay</p>
          </Link>
          <Link href="/cd-calculator" className="block p-4 bg-gray-50 dark:bg-gray-800 hover:bg-gray-100 dark:hover:bg-gray-700 rounded-lg border border-gray-200 dark:border-gray-700 transition-colors">
            <h3 className="font-medium text-gray-800 dark:text-gray-200">CD Calculator</h3>
            <p className="text-sm text-gray-600 dark:text-gray-400">Calculate certificate of deposit returns</p>
          </Link>
          <Link href="/calorie-calculator" className="block p-4 bg-gray-50 dark:bg-gray-800 hover:bg-gray-100 dark:hover:bg-gray-700 rounded-lg border border-gray-200 dark:border-gray-700 transition-colors">
            <h3 className="font-medium text-gray-800 dark:text-gray-200">Calorie Calculator</h3>
            <p className="text-sm text-gray-600 dark:text-gray-400">Plan your nutrition and health goals</p>
          </Link>
        </div>
      </section>
    </>
  );
}