import Link from 'next/link';

export default function NoTaxOvertimeCalculatorGuideBlogContent() {
  return (
    <>
      <section className="mb-8">
        <h2 className="text-2xl font-bold text-gray-800 dark:text-white mb-4">What is the No Tax on Overtime Provision?</h2>
        <p className="text-gray-700 dark:text-gray-300 mb-4">
          The No Tax on Overtime provision, part of the One Big Beautiful Bill Act, represents one of the most significant changes to overtime taxation in decades. This temporary measure allows eligible workers to deduct federal income taxes on the premium portion of their overtime pay for tax years 2025-2028.
        </p>
        <div className="bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-700 rounded-lg p-6">
          <h3 className="text-lg font-semibold text-blue-800 dark:text-blue-200 mb-2">🎯 Key Features</h3>
          <ul className="text-blue-700 dark:text-blue-300 text-sm space-y-1">
            <li>• <strong>Effective Period:</strong> Tax years 2025-2028 only</li>
            <li>• <strong>Maximum Deduction:</strong> $12,500 (single) or $25,000 (married filing jointly)</li>
            <li>• <strong>Income Limits:</strong> Phase-out begins at $150K (single) or $300K (married)</li>
            <li>• <strong>Premium Only:</strong> Only the 0.5x premium portion is deductible</li>
          </ul>
        </div>
      </section>

      <section className="mb-8">
        <h2 className="text-2xl font-bold text-gray-800 dark:text-white mb-4">Understanding Overtime Premium vs. Total Pay</h2>
        <p className="text-gray-700 dark:text-gray-300 mb-4">
          This distinction is crucial for accurate calculations. Many workers mistakenly believe their entire overtime pay is deductible, but the law specifically targets only the premium portion.
        </p>
        
        <div className="bg-green-50 dark:bg-green-900/20 border border-green-200 dark:border-green-700 rounded-lg p-6 mb-6">
          <h3 className="text-lg font-semibold text-green-800 dark:text-green-200 mb-3">📊 Example Calculation</h3>
          <div className="text-green-700 dark:text-green-300 text-sm space-y-2">
            <p><strong>If you earn $20/hour regular pay:</strong></p>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mt-3">
              <div>
                <ul className="space-y-1">
                  <li>• Regular overtime rate: $30/hour (1.5x)</li>
                  <li>• Premium portion: $10/hour (0.5x)</li>
                  <li>• <strong>Only the $10/hour premium is deductible</strong></li>
                </ul>
              </div>
              <div className="bg-green-100 dark:bg-green-900/30 border border-green-200 dark:border-green-700 p-3 rounded">
                <p className="font-medium text-green-800 dark:text-green-200">Annual Premium Example:</p>
                <p className="text-green-700 dark:text-green-300">$10 × 10 hrs/week × 50 weeks = $5,000 deductible</p>
              </div>
            </div>
          </div>
        </div>
      </section>

      <section className="mb-8">
        <h2 className="text-2xl font-bold text-gray-800 dark:text-white mb-4">Who Qualifies for the Deduction?</h2>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          <div className="bg-purple-50 dark:bg-purple-900/20 border border-purple-200 dark:border-purple-700 rounded-lg p-6">
            <h3 className="text-lg font-semibold text-purple-800 dark:text-purple-200 mb-3">👤 Single Filers</h3>
            <div className="text-purple-700 dark:text-purple-300 text-sm space-y-2">
              <p><strong>Full deduction:</strong> MAGI up to $150,000</p>
              <p><strong>Phase-out:</strong> $150,001 - $200,000</p>
              <p><strong>No deduction:</strong> MAGI above $200,000</p>
            </div>
          </div>

          <div className="bg-orange-50 dark:bg-orange-900/20 border border-orange-200 dark:border-orange-700 rounded-lg p-6">
            <h3 className="text-lg font-semibold text-orange-800 dark:text-orange-200 mb-3">👫 Married Filing Jointly</h3>
            <div className="text-orange-700 dark:text-orange-300 text-sm space-y-2">
              <p><strong>Full deduction:</strong> MAGI up to $300,000</p>
              <p><strong>Phase-out:</strong> $300,001 - $350,000</p>
              <p><strong>No deduction:</strong> MAGI above $350,000</p>
            </div>
          </div>
        </div>
      </section>

      <section className="mb-8">
        <h2 className="text-2xl font-bold text-gray-800 dark:text-white mb-4">Real-World Examples</h2>
        <div className="space-y-6">
          <div className="bg-gray-50 dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-lg p-6">
            <h3 className="text-lg font-semibold text-gray-800 dark:text-gray-200 mb-3">🏥 Healthcare Worker Example</h3>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
              <div>
                <p className="font-medium mb-2">Background:</p>
                <ul className="space-y-1 text-gray-600 dark:text-gray-400">
                  <li>• Regular rate: $28/hour</li>
                  <li>• Weekly overtime: 12 hours</li>
                  <li>• Weeks worked: 50</li>
                  <li>• MAGI: $85,000</li>
                </ul>
              </div>
              <div>
                <p className="font-medium mb-2">Tax Savings:</p>
                <ul className="space-y-1 text-gray-600 dark:text-gray-400">
                  <li>• Premium: $28 × 0.5 × 12 × 50 = $8,400</li>
                  <li>• Marginal rate: 22%</li>
                  <li>• <strong className="text-green-600 dark:text-green-400">Annual savings: $1,848</strong></li>
                </ul>
              </div>
            </div>
          </div>

          <div className="bg-gray-50 dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-lg p-6">
            <h3 className="text-lg font-semibold text-gray-800 dark:text-gray-200 mb-3">🏭 Manufacturing Supervisor Example</h3>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
              <div>
                <p className="font-medium mb-2">Background:</p>
                <ul className="space-y-1 text-gray-600 dark:text-gray-400">
                  <li>• Regular rate: $35/hour</li>
                  <li>• Weekly overtime: 10 hours</li>
                  <li>• Weeks worked: 52</li>
                  <li>• Combined MAGI: $185,000 (married)</li>
                </ul>
              </div>
              <div>
                <p className="font-medium mb-2">Tax Savings:</p>
                <ul className="space-y-1 text-gray-600 dark:text-gray-400">
                  <li>• Premium: $35 × 0.5 × 10 × 52 = $9,100</li>
                  <li>• Marginal rate: 24%</li>
                  <li>• <strong className="text-green-600 dark:text-green-400">Annual savings: $2,184</strong></li>
                </ul>
              </div>
            </div>
          </div>
        </div>
      </section>

      <section className="mb-8">
        <h2 className="text-2xl font-bold text-gray-800 dark:text-white mb-4">Strategic Planning Tips</h2>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          <div>
            <h3 className="text-lg font-semibold text-gray-800 dark:text-gray-200 mb-3">💡 Maximize Your Benefit</h3>
            <ul className="text-gray-700 dark:text-gray-300 text-sm space-y-2">
              <li>• <strong>Track Hours Carefully:</strong> Maintain detailed overtime records</li>
              <li>• <strong>Consider Timing:</strong> Plan overtime work within income limits</li>
              <li>• <strong>Plan Other Income:</strong> Consider impact of bonuses on MAGI</li>
              <li>• <strong>Coordinate with Spouse:</strong> Optimize combined income strategy</li>
            </ul>
          </div>
          <div>
            <h3 className="text-lg font-semibold text-gray-800 dark:text-gray-200 mb-3">📋 Essential Records</h3>
            <ul className="text-gray-700 dark:text-gray-300 text-sm space-y-2">
              <li>• Pay stubs with overtime hours and rates</li>
              <li>• W-2 forms with overtime pay details</li>
              <li>• Time tracking records</li>
              <li>• Employment contracts specifying rates</li>
            </ul>
          </div>
        </div>
      </section>

      <section className="mb-8">
        <h2 className="text-2xl font-bold text-gray-800 dark:text-white mb-4">Common Misconceptions</h2>
        <div className="space-y-4">
          <div className="bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-700 rounded-lg p-6">
            <h3 className="text-lg font-semibold text-red-800 dark:text-red-200 mb-2">❌ &quot;All Overtime Pay is Deductible&quot;</h3>
            <p className="text-red-700 dark:text-red-300 text-sm">
              <strong>Reality:</strong> Only the premium portion (0.5x regular rate) is deductible, not the entire 1.5x overtime payment.
            </p>
          </div>

          <div className="bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-700 rounded-lg p-6">
            <h3 className="text-lg font-semibold text-red-800 dark:text-red-200 mb-2">❌ &quot;It Reduces All Taxes&quot;</h3>
            <p className="text-red-700 dark:text-red-300 text-sm">
              <strong>Reality:</strong> The deduction only affects federal income tax, not Social Security, Medicare, or state taxes.
            </p>
          </div>

          <div className="bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-700 rounded-lg p-6">
            <h3 className="text-lg font-semibold text-red-800 dark:text-red-200 mb-2">❌ &quot;It&apos;s Permanent&quot;</h3>
            <p className="text-red-700 dark:text-red-300 text-sm">
              <strong>Reality:</strong> The provision only applies to tax years 2025-2028 unless extended by Congress.
            </p>
          </div>
        </div>
      </section>

      <section className="mb-8">
        <h2 className="text-2xl font-bold text-gray-800 dark:text-white mb-4">Economic Impact and Considerations</h2>
        <div className="bg-yellow-50 dark:bg-yellow-900/20 border border-yellow-200 dark:border-yellow-700 rounded-lg p-6">
          <h3 className="text-lg font-semibold text-yellow-800 dark:text-yellow-200 mb-2">📈 Key Statistics</h3>
          <div className="text-yellow-700 dark:text-yellow-300 text-sm space-y-2">
            <p>• <strong>Projected Cost:</strong> $124 billion through 2028 (Congressional Budget Office)</p>
            <p>• <strong>Average Savings:</strong> $1,400-$1,750 for typical overtime workers (White House estimate)</p>
            <p>• <strong>Temporary Nature:</strong> Policy expires after 2028 unless extended</p>
            <p>• <strong>Federal Only:</strong> State tax treatment varies by state</p>
          </div>
        </div>
      </section>

      <section className="mb-8">
        <h2 className="text-2xl font-bold text-gray-800 dark:text-white mb-4">Using Our Calculator Effectively</h2>
        <p className="text-gray-700 dark:text-gray-300 mb-6">
          Our No Tax on Overtime Calculator helps you estimate potential savings with advanced features including income phase-out calculations and marginal tax rate analysis.
        </p>
        
        <div className="bg-gradient-to-r from-blue-50 to-indigo-50 dark:from-blue-900/20 dark:to-indigo-900/20 border border-blue-200 dark:border-blue-700 rounded-lg p-6 text-center">
          <h3 className="text-lg font-semibold text-blue-800 dark:text-blue-200 mb-3">🧮 Calculate Your Savings</h3>
          <p className="text-blue-700 dark:text-blue-300 text-sm mb-4">
            Get personalized estimates with our comprehensive calculator featuring real-time calculations, URL sharing, and detailed breakdowns.
          </p>
          <Link 
            href="/no-tax-overtime-calculator" 
            className="inline-block bg-blue-600 hover:bg-blue-700 text-white font-medium py-2 px-6 rounded-lg transition-colors duration-200"
          >
            Start Calculating Now →
          </Link>
        </div>
      </section>

      <section className="mb-8">
        <h2 className="text-2xl font-bold text-gray-800 dark:text-white mb-4">External Resources</h2>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <a 
            href="https://www.whitehouse.gov/obbb/" 
            target="_blank" 
            rel="noopener noreferrer"
            className="block p-4 bg-gray-50 dark:bg-gray-800 hover:bg-gray-100 dark:hover:bg-gray-700 rounded-lg border border-gray-200 dark:border-gray-700 transition-colors"
          >
            <h3 className="font-medium text-gray-800 dark:text-gray-200 mb-2">🏛️ White House Official Information</h3>
            <p className="text-sm text-gray-600 dark:text-gray-400 mb-2">Official details about the One Big Beautiful Bill Act and overtime tax provisions</p>
            <span className="text-blue-500 text-sm">Visit whitehouse.gov →</span>
          </a>
          
          <a 
            href="https://www.bankrate.com/taxes/no-tax-on-tips-and-overtime-what-workers-should-know/" 
            target="_blank" 
            rel="noopener noreferrer"
            className="block p-4 bg-gray-50 dark:bg-gray-800 hover:bg-gray-100 dark:hover:bg-gray-700 rounded-lg border border-gray-200 dark:border-gray-700 transition-colors"
          >
            <h3 className="font-medium text-gray-800 dark:text-gray-200 mb-2">📰 Bankrate Expert Analysis</h3>
            <p className="text-sm text-gray-600 dark:text-gray-400 mb-2">Comprehensive analysis of overtime and tips tax changes</p>
            <span className="text-blue-500 text-sm">Read analysis →</span>
          </a>

          <a 
            href="https://ottcalculator.com/" 
            target="_blank" 
            rel="noopener noreferrer"
            className="block p-4 bg-gray-50 dark:bg-gray-800 hover:bg-gray-100 dark:hover:bg-gray-700 rounded-lg border border-gray-200 dark:border-gray-700 transition-colors"
          >
            <h3 className="font-medium text-gray-800 dark:text-gray-200 mb-2">🧮 Additional Calculator Resource</h3>
            <p className="text-sm text-gray-600 dark:text-gray-400 mb-2">Compare calculations with other overtime tax calculators</p>
            <span className="text-blue-500 text-sm">Visit ottcalculator.com →</span>
          </a>

          <a 
            href="https://www.irs.gov/" 
            target="_blank" 
            rel="noopener noreferrer"
            className="block p-4 bg-gray-50 dark:bg-gray-800 hover:bg-gray-100 dark:hover:bg-gray-700 rounded-lg border border-gray-200 dark:border-gray-700 transition-colors"
          >
            <h3 className="font-medium text-gray-800 dark:text-gray-200 mb-2">🏛️ IRS Official Guidance</h3>
            <p className="text-sm text-gray-600 dark:text-gray-400 mb-2">Official tax guidance and forms from the Internal Revenue Service</p>
            <span className="text-blue-500 text-sm">Visit irs.gov →</span>
          </a>
        </div>
      </section>

      <section>
        <h2 className="text-2xl font-bold text-gray-800 dark:text-white mb-4">Related Financial Tools</h2>
        <p className="text-gray-700 dark:text-gray-300 mb-4">
          Explore our other financial calculators to optimize your tax planning:
        </p>
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          <Link href="/cd-calculator" className="block p-4 bg-gray-50 dark:bg-gray-800 hover:bg-gray-100 dark:hover:bg-gray-700 rounded-lg border border-gray-200 dark:border-gray-700 transition-colors">
            <h3 className="font-medium text-gray-800 dark:text-gray-200">CD Calculator</h3>
            <p className="text-sm text-gray-600 dark:text-gray-400">Calculate certificate of deposit returns</p>
          </Link>
          <Link href="/calorie-calculator" className="block p-4 bg-gray-50 dark:bg-gray-800 hover:bg-gray-100 dark:hover:bg-gray-700 rounded-lg border border-gray-200 dark:border-gray-700 transition-colors">
            <h3 className="font-medium text-gray-800 dark:text-gray-200">Calorie Calculator</h3>
            <p className="text-sm text-gray-600 dark:text-gray-400">Plan your nutrition and health goals</p>
          </Link>
          <Link href="/time-calculator" className="block p-4 bg-gray-50 dark:bg-gray-800 hover:bg-gray-100 dark:hover:bg-gray-700 rounded-lg border border-gray-200 dark:border-gray-700 transition-colors">
            <h3 className="font-medium text-gray-800 dark:text-gray-200">Time Calculator</h3>
            <p className="text-sm text-gray-600 dark:text-gray-400">Calculate work hours and time differences</p>
          </Link>
        </div>
      </section>
    </>
  );
}