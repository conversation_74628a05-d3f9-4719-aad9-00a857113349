name: Build Develop Docker to ACK
on:
  push:
    branches:
      - main

env:
  REGISTRY: registry.us-west-1.aliyuncs.com
  IMAGE_NAME: frytea_hub/${{ github.event.repository.name }}
  USERNAME: ${{ secrets.ACR_USERNAME }}
  TOKEN: ${{ secrets.ACR_TOKEN }}

jobs:
  docker:
    runs-on: ubuntu-latest
    steps:
      -
        name: Checkout
        uses: actions/checkout@v4
        with:
          fetch-depth: 0  # 获取完整的 git history 以便生成正确的 tag
      -
        name: Get Version
        id: get_version
        run: |
          sudo apt-get update && sudo apt-get install -y git && echo "VERSION=$(git describe --dirty --always --tags --abbrev=7)" >> $GITHUB_OUTPUT
      -
        name: Login to ${{ env.REGISTRY }}
        if: github.event_name != 'pull_request'
        uses: docker/login-action@v3
        with:
          registry: ${{ env.REGISTRY }}
          username: ${{ env.USERNAME }}
          password: ${{ env.TOKEN }}
      -
        name: Set up QEMU
        uses: docker/setup-qemu-action@v3
      -
        name: Set up Docker Buildx
        uses: docker/setup-buildx-action@v3
      -
        name: Build and push
        uses: docker/build-push-action@v6
        with:
          context: .
          push: ${{ github.event_name != 'pull_request' }}
          build-args: |
            PUSH_RECENT=1
            VERSION=${{ steps.get_version.outputs.VERSION }}
            NEXT_PUBLIC_FORMSPREE_ENDPOINT=${{ secrets.NEXT_PUBLIC_FORMSPREE_ENDPOINT }}
          tags: |
            ${{ env.REGISTRY }}/${{ env.IMAGE_NAME }}:develop
      -
        name: Notify Keel
        if: ${{ github.event_name != 'pull_request' }}
        uses: fjogeleit/http-request-action@master
        with:
          url: 'https://keel.skybyte.me/v1/webhooks/native'
          method: 'POST'
          contentType: 'application/json'
          data: '{"name": "${{ env.REGISTRY }}/${{ env.IMAGE_NAME }}", "tag": "develop"}' 