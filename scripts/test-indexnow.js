const IndexNowSubmitter = require('./submit-indexnow');

class IndexNowTester extends IndexNowSubmitter {
  constructor() {
    super();
    // 测试模式，只提交几个 URL
    this.isTestMode = true;
  }

  // 测试模式下只提交少量 URL
  filterUrls(urls) {
    if (this.isTestMode) {
      console.log('🧪 测试模式：只提交前 3 个 URL');
      return urls.slice(0, 3);
    }
    return super.filterUrls(urls);
  }

  // 在测试模式下模拟请求
  async makeRequest(endpoint, payload) {
    if (this.isTestMode) {
      console.log(`🧪 模拟请求到 ${endpoint}`);
      console.log(`📝 载荷:`, JSON.stringify(payload, null, 2));
      
      // 模拟延迟
      await this.delay(500);
      
      // 模拟成功响应
      return {
        statusCode: 200,
        response: 'OK'
      };
    }
    
    return super.makeRequest(endpoint, payload);
  }

  async run() {
    console.log('🧪 IndexNow 测试模式\n');
    console.log('⚠️  这是测试模式，不会发送真实请求\n');
    
    await super.run();
    
    console.log('\n💡 测试完成！要进行真实提交，请运行: npm run indexnow');
  }
}

// 运行测试
const tester = new IndexNowTester();
tester.run();