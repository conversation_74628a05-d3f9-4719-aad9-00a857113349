const fs = require('fs');
const path = require('path');

// IndexNow API key 读取函数
function readApiKeyFromPublic() {
  const publicDir = path.join(process.cwd(), 'public');
  
  if (!fs.existsSync(publicDir)) {
    throw new Error('❌ public 目录不存在');
  }

  // 读取 public 目录下的所有文件
  const files = fs.readdirSync(publicDir);
  
  // 查找符合条件的 API key 文件
  // 条件：32字符长度的十六进制字符串 + .txt 后缀
  const apiKeyFiles = files.filter(file => {
    const match = file.match(/^([a-f0-9]{32})\.txt$/i);
    return match !== null;
  });

  if (apiKeyFiles.length === 0) {
    throw new Error('❌ 在 public 目录下未找到有效的 IndexNow API key 文件\n' +
                   '   文件名格式应为：{32位十六进制字符}.txt\n' +
                   '   例如：62a5c8cf35bb48c4b9f676fb0e5e26fb.txt');
  }

  if (apiKeyFiles.length > 1) {
    console.warn('⚠️  找到多个 API key 文件，使用第一个:', apiKeyFiles[0]);
  }

  const apiKeyFile = apiKeyFiles[0];
  const apiKeyPath = path.join(publicDir, apiKeyFile);
  
  try {
    const apiKeyContent = fs.readFileSync(apiKeyPath, 'utf8').trim();
    const expectedApiKey = path.basename(apiKeyFile, '.txt');
    
    // 验证文件内容是否与文件名匹配
    if (apiKeyContent.toLowerCase() !== expectedApiKey.toLowerCase()) {
      throw new Error(`❌ API key 文件内容与文件名不匹配\n` +
                     `   文件名: ${expectedApiKey}\n` +
                     `   文件内容: ${apiKeyContent}`);
    }
    
    console.log(`✅ 成功读取 API key: ${expectedApiKey}`);
    return expectedApiKey;
    
  } catch (error) {
    if (error.code === 'ENOENT') {
      throw new Error(`❌ 无法读取 API key 文件: ${apiKeyPath}`);
    }
    throw error;
  }
}

// IndexNow 配置
const INDEXNOW_CONFIG = {
  // 从 public 目录读取 API key
  apiKey: readApiKeyFromPublic(),
  
  // 您的网站域名
  host: 'calc9.com',
  
  // IndexNow 端点（支持多个搜索引擎）
  endpoints: [
    'https://api.indexnow.org/indexnow',  // 通用端点
    'https://www.bing.com/indexnow',       // Bing
    // 'https://search.yandex.com/indexnow',  // Yandex（可选）
  ],
  
  // 网站基础 URL
  baseUrl: 'https://calc9.com',
  
  // 要提交的 URL 类型
  includePatterns: [
    '/',                           // 首页
    '/blog',                      // 博客页
    '/privacy-policy',            // 隐私政策
    '/terms-of-service',          // 服务条款
    '/*-calculator',              // 所有计算器页面
    '/blog/*',                    // 所有博客文章
  ],
  
  // 要排除的 URL 模式
  excludePatterns: [
    '/api/*',
    '/_next/*',
    '/favicon.ico',
    '/robots.txt',
    '/sitemap.xml',
  ]
};

module.exports = INDEXNOW_CONFIG;