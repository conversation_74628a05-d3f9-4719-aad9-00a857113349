const fs = require('fs');
const path = require('path');
const https = require('https');
const { URL } = require('url');
const config = require('./indexnow-config');

class IndexNowSubmitter {
  constructor() {
    try {
      this.config = config;
      this.urls = [];
    } catch (error) {
      console.error('❌ IndexNow 配置初始化失败:', error.message);
      process.exit(1);
    }
  }

  // 从 sitemap.xml 读取所有 URL
  readSitemapUrls() {
    try {
      const sitemapPath = path.join(process.cwd(), 'out', 'sitemap.xml');
      
      if (!fs.existsSync(sitemapPath)) {
        console.warn('⚠️  sitemap.xml 未找到，尝试生成 URL 列表');
        return this.generateUrlsFromConfig();
      }

      const sitemapContent = fs.readFileSync(sitemapPath, 'utf8');
      const urlMatches = sitemapContent.match(/<loc>(.*?)<\/loc>/g);
      
      if (!urlMatches) {
        console.warn('⚠️  sitemap.xml 中未找到 URL');
        return [];
      }

      const urls = urlMatches.map(match => {
        return match.replace('<loc>', '').replace('</loc>', '').trim();
      });

      console.log(`📄 从 sitemap.xml 读取到 ${urls.length} 个 URL`);
      return urls;
    } catch (error) {
      console.error('❌ 读取 sitemap.xml 失败:', error.message);
      return this.generateUrlsFromConfig();
    }
  }

  // 从配置生成 URL（备用方案）
  generateUrlsFromConfig() {
    const { calculators } = require('../src/data/calculators');
    const { blogPosts } = require('../src/data/blog');

    const urls = [
      `${this.config.baseUrl}/`,
      `${this.config.baseUrl}/blog`,
      `${this.config.baseUrl}/privacy-policy`,
      `${this.config.baseUrl}/terms-of-service`,
    ];

    // 添加计算器页面
    calculators.forEach(calc => {
      urls.push(`${this.config.baseUrl}${calc.href}`);
    });

    // 添加博客文章
    Object.values(blogPosts).forEach(post => {
      urls.push(`${this.config.baseUrl}/blog/${post.slug}`);
    });

    console.log(`🔧 从配置生成 ${urls.length} 个 URL`);
    return urls;
  }

  // 过滤 URL
  filterUrls(urls) {
    const filtered = urls.filter(url => {
      const pathname = new URL(url).pathname;
      
      // 检查排除模式
      const shouldExclude = this.config.excludePatterns.some(pattern => {
        const regex = new RegExp(pattern.replace('*', '.*'));
        return regex.test(pathname);
      });

      return !shouldExclude;
    });

    console.log(`🔍 过滤后剩余 ${filtered.length} 个 URL`);
    return filtered;
  }

  // 提交到 IndexNow
  async submitToIndexNow(urls) {
    if (urls.length === 0) {
      console.warn('⚠️  没有 URL 需要提交');
      return;
    }

    // IndexNow 单次最多提交 10,000 个 URL，我们分批处理
    const batchSize = 1000;
    const batches = [];
    
    for (let i = 0; i < urls.length; i += batchSize) {
      batches.push(urls.slice(i, i + batchSize));
    }

    console.log(`📦 分成 ${batches.length} 批次提交`);

    const results = [];
    
    for (const [index, batch] of batches.entries()) {
      console.log(`🚀 提交批次 ${index + 1}/${batches.length} (${batch.length} 个 URL)`);
      
      const result = await this.submitBatch(batch);
      results.push(result);
      
      // 避免过于频繁的请求
      if (index < batches.length - 1) {
        await this.delay(1000);
      }
    }

    return results;
  }

  // 提交单个批次
  async submitBatch(urls) {
    const payload = {
      host: this.config.host,
      key: this.config.apiKey,
      urlList: urls
    };

    const results = [];

    // 向每个端点提交
    for (const endpoint of this.config.endpoints) {
      try {
        const result = await this.makeRequest(endpoint, payload);
        results.push({
          endpoint,
          success: true,
          statusCode: result.statusCode,
          response: result.response
        });
        console.log(`✅ ${endpoint}: ${result.statusCode}`);
      } catch (error) {
        results.push({
          endpoint,
          success: false,
          error: error.message
        });
        console.error(`❌ ${endpoint}: ${error.message}`);
      }
    }

    return results;
  }

  // 发送 HTTP 请求
  makeRequest(endpoint, payload) {
    return new Promise((resolve, reject) => {
      const url = new URL(endpoint);
      const postData = JSON.stringify(payload);

      const options = {
        hostname: url.hostname,
        port: url.port || 443,
        path: url.pathname,
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Content-Length': Buffer.byteLength(postData),
          'User-Agent': 'Calc9-IndexNow-Submitter/1.0'
        }
      };

      const req = https.request(options, (res) => {
        let data = '';
        
        res.on('data', (chunk) => {
          data += chunk;
        });
        
        res.on('end', () => {
          resolve({
            statusCode: res.statusCode,
            response: data
          });
        });
      });

      req.on('error', (error) => {
        reject(error);
      });

      req.write(postData);
      req.end();
    });
  }

  // 延迟函数
  delay(ms) {
    return new Promise(resolve => setTimeout(resolve, ms));
  }

  // 验证并复制 API key 文件到 out 目录（用于部署验证）
  copyApiKeyFile() {
    try {
      const sourceApiKeyPath = path.join(process.cwd(), 'public', `${this.config.apiKey}.txt`);
      const targetApiKeyPath = path.join(process.cwd(), 'out', `${this.config.apiKey}.txt`);
      
      // 确保 out 目录存在
      const outDir = path.dirname(targetApiKeyPath);
      if (!fs.existsSync(outDir)) {
        fs.mkdirSync(outDir, { recursive: true });
      }
      
      // 复制文件到 out 目录
      fs.copyFileSync(sourceApiKeyPath, targetApiKeyPath);
      console.log(`🔑 API Key 文件已复制到部署目录: ${targetApiKeyPath}`);
    } catch (error) {
      console.error('❌ 复制 API Key 文件失败:', error.message);
      throw error;
    }
  }

  // 主执行函数
  async run() {
    // 检查是否跳过 IndexNow 提交
    if (process.env.SKIP_INDEXNOW === 'true') {
      console.log('⏭️  SKIP_INDEXNOW=true，跳过 IndexNow 提交');
      return;
    }

    console.log('🚀 开始 IndexNow 提交流程\n');
    
    try {
      // 1. 读取 URL
      const allUrls = this.readSitemapUrls();
      
      // 2. 过滤 URL
      const filteredUrls = this.filterUrls(allUrls);
      
      // 3. 复制 API Key 文件到部署目录
      this.copyApiKeyFile();
      
      // 4. 提交到 IndexNow
      if (filteredUrls.length > 0) {
        const results = await this.submitToIndexNow(filteredUrls);
        
        // 5. 输出结果摘要
        this.printSummary(results, filteredUrls.length);
      }
      
      console.log('\n✅ IndexNow 提交完成');
      
    } catch (error) {
      console.error('\n❌ IndexNow 提交失败:', error.message);
      process.exit(1);
    }
  }

  // 打印结果摘要
  printSummary(results, totalUrls) {
    console.log('\n📊 提交结果摘要:');
    console.log(`总 URL 数量: ${totalUrls}`);
    console.log(`API Key: ${this.config.apiKey}`);
    
    const flatResults = results.flat();
    const successCount = flatResults.filter(r => r.success).length;
    const failCount = flatResults.filter(r => !r.success).length;
    
    console.log(`成功提交: ${successCount}`);
    console.log(`失败提交: ${failCount}`);
    
    if (failCount > 0) {
      console.log('\n失败的端点:');
      flatResults.filter(r => !r.success).forEach(r => {
        console.log(`- ${r.endpoint}: ${r.error}`);
      });
    }
  }
}

// 如果直接运行此脚本
if (require.main === module) {
  const submitter = new IndexNowSubmitter();
  submitter.run();
}

module.exports = IndexNowSubmitter;