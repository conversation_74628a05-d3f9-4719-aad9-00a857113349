# IndexNow 集成指南

本项目集成了 [IndexNow](https://www.indexnow.org/) 协议，可以在构建时自动通知搜索引擎网站内容更新，从而加快搜索引擎索引速度。

## 什么是 IndexNow？

IndexNow 是一个开放协议，允许网站立即通知搜索引擎（如 Bing、Yandex）页面内容已更新，而不需要等待爬虫重新发现。

## 功能特性

- ✅ 自动从 `sitemap.xml` 读取所有 URL
- ✅ 支持多个搜索引擎端点（Bing、IndexNow 通用端点）
- ✅ 批量提交（避免 API 限制）
- ✅ 自动生成和管理 API Key
- ✅ 错误处理和重试机制
- ✅ 详细的日志输出

## 使用方法

### 1. 开发环境测试

```bash
# 运行测试模式（不发送真实请求）
node scripts/test-indexnow.js
```

### 2. 手动提交

```bash
# 手动提交当前所有 URL
npm run indexnow
```

### 3. 生产构建（自动提交）

```bash
# 构建并自动提交到 IndexNow
npm run build:production
```

### 4. 普通构建（不提交）

```bash
# 只构建，不提交
npm run build
```

## 配置选项

### 环境变量

在 `.env.local` 文件中可以设置以下选项：

```bash
# 在 CI/CD 中禁用 IndexNow 提交
SKIP_INDEXNOW=true
```

**注意：** 现在 API Key 从 `public/` 目录的文件中读取，不再支持环境变量设置。

### 配置文件

在 `scripts/indexnow-config.js` 中可以修改：

- 支持的搜索引擎端点
- URL 包含/排除规则
- 网站域名配置

## 工作原理

1. **读取 URL**: 从构建生成的 `out/sitemap.xml` 读取所有 URL
2. **过滤 URL**: 根据配置规则过滤掉不需要的 URL
3. **生成 API Key**: 自动生成或使用预设的 API Key
4. **创建验证文件**: 在 `out/` 目录下创建 `{api-key}.txt` 文件用于验证
5. **批量提交**: 向多个搜索引擎端点提交 URL 列表
6. **结果报告**: 输出详细的提交结果

## API Key 配置

### 1. 创建 API Key 文件

在 `public/` 目录下创建一个 API key 文件，文件名必须符合以下格式：
- 文件名：`{32位十六进制字符}.txt`
- 文件内容：与文件名相同的32位十六进制字符串

**示例：**
```bash
# 文件名
public/62a5c8cf35bb48c4b9f676fb0e5e26fb.txt

# 文件内容
62a5c8cf35bb48c4b9f676fb0e5e26fb
```

### 2. API Key 验证规则

脚本会自动扫描 `public/` 目录：
- ✅ 查找符合 `^[a-f0-9]{32}\.txt$` 格式的文件
- ✅ 验证文件内容与文件名是否匹配
- ✅ 自动复制到 `out/` 目录用于部署验证
- ❌ 如果找不到文件或内容不匹配会报错

### 3. 部署验证

部署后，IndexNow 会通过访问 `https://calc9.com/{api-key}.txt` 来验证网站所有权。

## 支持的搜索引擎

目前配置支持：

- **IndexNow 通用端点**: `https://api.indexnow.org/indexnow`
- **Bing**: `https://www.bing.com/indexnow`
- **Yandex**: `https://search.yandex.com/indexnow` (可选)

## 错误处理

- 如果 `sitemap.xml` 不存在，会从数据配置文件生成 URL 列表
- 网络错误会被记录但不会中断构建过程
- 支持多端点提交，即使部分失败也会继续

## 最佳实践

1. **生产环境**: 使用 `npm run build:production` 进行构建和提交
2. **开发环境**: 使用 `npm run build` 避免不必要的提交
3. **CI/CD**: 设置 `SKIP_INDEXNOW=true` 在某些环境中禁用
4. **固定 API Key**: 在生产环境设置固定的 `INDEXNOW_API_KEY`

## 日志示例

```
🚀 开始 IndexNow 提交流程

📄 从 sitemap.xml 读取到 25 个 URL
🔍 过滤后剩余 25 个 URL
🔑 API Key 文件已生成: /path/to/out/abc123.txt
📦 分成 1 批次提交
🚀 提交批次 1/1 (25 个 URL)
✅ https://api.indexnow.org/indexnow: 200
✅ https://www.bing.com/indexnow: 200

📊 提交结果摘要:
总 URL 数量: 25
API Key: abc123
成功提交: 2
失败提交: 0

✅ IndexNow 提交完成
```

## 故障排除

### 1. sitemap.xml 未找到
确保运行 `next build` 后 `out/sitemap.xml` 文件存在。

### 2. API Key 验证失败
确保生成的 `{api-key}.txt` 文件部署到网站根目录。

### 3. 网络错误
检查网络连接，IndexNow 端点可能暂时不可用。

### 4. 权限错误
确保脚本有权限写入 `out/` 目录。

## 更多信息

- [IndexNow 官方文档](https://www.indexnow.org/)
- [Bing IndexNow 文档](https://docs.microsoft.com/en-us/bingwebmaster/indexnow)
- [IndexNow API 规范](https://www.indexnow.org/documentation)