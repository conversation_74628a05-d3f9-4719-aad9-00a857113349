# Tailwind 暗黑模式最佳实践建议

## 当前问题分析

### 1. 过度使用 !important
```css
/* 当前实现 - 不推荐 */
html.dark div[class*="bg-white"] {
  background-color: rgba(23, 37, 84, 0.6) !important;
}
```

### 2. 混合 Tailwind 和自定义 CSS
```tsx
// 当前实现 - 复杂
<div className="bg-white dark:bg-blue-950/60" />
// 还需要额外的 CSS 覆盖
```

## 推荐的最佳实践

### 1. 纯 Tailwind 解决方案
```tsx
// 推荐实现
<div className="bg-white dark:bg-gray-900 text-gray-900 dark:text-white">
  <div className="bg-gray-50 dark:bg-gray-800 border border-gray-200 dark:border-gray-700">
    <input className="bg-white dark:bg-gray-700 text-gray-900 dark:text-white border-gray-300 dark:border-gray-600" />
  </div>
</div>
```

### 2. 设计 Token 系统
```js
// tailwind.config.js
module.exports = {
  theme: {
    extend: {
      colors: {
        // 语义化颜色命名
        background: {
          primary: 'rgb(255 255 255 / <alpha-value>)',
          'primary-dark': 'rgb(17 24 39 / <alpha-value>)',
          secondary: 'rgb(249 250 251 / <alpha-value>)',
          'secondary-dark': 'rgb(31 41 55 / <alpha-value>)',
        },
        surface: {
          primary: 'rgb(255 255 255 / <alpha-value>)',
          'primary-dark': 'rgb(55 65 81 / <alpha-value>)',
        }
      }
    }
  }
}
```

### 3. 组件样式抽取
```tsx
// 组件化暗黑模式样式
const cardStyles = "bg-background-primary dark:bg-background-primary-dark border border-gray-200 dark:border-gray-700 rounded-lg shadow-sm dark:shadow-lg"
const inputStyles = "bg-white dark:bg-gray-700 text-gray-900 dark:text-white border-gray-300 dark:border-gray-600 focus:ring-blue-500 focus:border-blue-500"

function Calculator() {
  return (
    <div className={cardStyles}>
      <input className={inputStyles} />
    </div>
  )
}
```

### 4. 使用 CSS 变量（高级）
```css
/* 全局 CSS 变量定义 */
:root {
  --color-background-primary: 255 255 255;
  --color-surface-primary: 249 250 251;
  --color-text-primary: 17 24 39;
}

.dark {
  --color-background-primary: 17 24 39;
  --color-surface-primary: 31 41 55;
  --color-text-primary: 249 250 251;
}
```

```js
// tailwind.config.js
module.exports = {
  theme: {
    extend: {
      colors: {
        background: {
          primary: 'rgb(var(--color-background-primary) / <alpha-value>)',
        },
        surface: {
          primary: 'rgb(var(--color-surface-primary) / <alpha-value>)',
        },
        text: {
          primary: 'rgb(var(--color-text-primary) / <alpha-value>)',
        }
      }
    }
  }
}
```

## 迁移建议

### 阶段 1: 清理现有实现
1. 移除 globals.css 中的 !important 规则
2. 统一使用 Tailwind 暗黑模式类

### 阶段 2: 建立设计系统
1. 定义语义化颜色变量
2. 创建组件样式库

### 阶段 3: 组件重构
1. 抽取通用样式组件
2. 统一暗黑模式实现

## 优势对比

| 方案 | 维护性 | 性能 | 一致性 | 可扩展性 |
|------|--------|------|--------|----------|
| 当前实现 | ❌ 低 | ⚠️ 中 | ⚠️ 中 | ❌ 低 |
| Tailwind 最佳实践 | ✅ 高 | ✅ 高 | ✅ 高 | ✅ 高 |

## 示例重构

### 重构前
```tsx
<div className="bg-white/90 dark:bg-blue-950/60 rounded-lg shadow-lg dark:shadow-lg p-6 border border-gray-200/50 dark:border-blue-400/40 backdrop-blur-sm">
```

### 重构后
```tsx
<div className="bg-white dark:bg-gray-900 rounded-lg shadow-lg p-6 border border-gray-200 dark:border-gray-700">
```

更简洁、更易维护、性能更好。