/** @type {import('tailwindcss').Config} */
module.exports = {
  content: [
    './src/pages/**/*.{js,ts,jsx,tsx,mdx}',
    './src/components/**/*.{js,ts,jsx,tsx,mdx}',
    './src/app/**/*.{js,ts,jsx,tsx,mdx}',
  ],
  darkMode: 'class',
  theme: {
    extend: {
      colors: {
        blue: {
          950: '#0c1938',
        }
      }
    },
  },
  safelist: [
    // 确保暗黑模式的灰色类被生成
    'dark:bg-gray-800',
    'dark:bg-gray-900',
    'dark:bg-gray-700',
    'dark:bg-slate-800',
    'dark:bg-slate-700',
    'dark:border-gray-700',
    'dark:border-gray-600',
    'dark:text-white',
    'dark:text-gray-300',
    'dark:text-gray-400',
    // Badge 暗黑模式样式
    'dark:bg-blue-900',
    'dark:bg-green-900',
    'dark:bg-purple-900',
    'dark:border-blue-700',
    'dark:border-green-700',
    'dark:border-purple-700',
    'dark:text-blue-100',
    'dark:text-green-100',
    'dark:text-purple-100',
    // 确保所有状态颜色的暗黑模式变体被生成
    'dark:bg-blue-900/20',
    'dark:bg-green-900/20',
    'dark:bg-yellow-900/20',
    'dark:bg-orange-900/20',
    'dark:bg-purple-900/20',
    'dark:border-blue-800',
    'dark:border-green-800',
    'dark:border-yellow-800',
    'dark:border-orange-800',
    'dark:border-purple-800',
    'dark:text-blue-200',
    'dark:text-blue-300',
    'dark:text-blue-400',
    'dark:text-green-200',
    'dark:text-green-300',
    'dark:text-green-400',
    'dark:text-yellow-200',
    'dark:text-yellow-300',
    'dark:text-yellow-400',
    'dark:text-orange-200',
    'dark:text-orange-300',
    'dark:text-orange-400',
    'dark:text-purple-200',
    'dark:text-purple-300',
    'dark:text-purple-400',
    // Focus ring offset for dark mode
    'dark:focus:ring-offset-gray-800',
    // 计算器图标颜色和边框 - 所有颜色变体
    'bg-blue-500/10', 'bg-blue-500/15', 'border-blue-400/30', 'border-blue-400/40',
    'dark:bg-blue-500/15', 'dark:border-blue-400/40', 'text-blue-600', 'dark:text-blue-400',
    'bg-green-500/10', 'bg-green-500/15', 'border-green-400/30', 'border-green-400/40',
    'dark:bg-green-500/15', 'dark:border-green-400/40', 'text-green-600', 'dark:text-green-400',
    'bg-purple-500/10', 'bg-purple-500/15', 'border-purple-400/30', 'border-purple-400/40',
    'dark:bg-purple-500/15', 'dark:border-purple-400/40', 'text-purple-600', 'dark:text-purple-400',
    'bg-emerald-500/10', 'bg-emerald-500/15', 'border-emerald-400/30', 'border-emerald-400/40',
    'dark:bg-emerald-500/15', 'dark:border-emerald-400/40', 'text-emerald-600', 'dark:text-emerald-400',
    'bg-orange-500/10', 'bg-orange-500/15', 'border-orange-400/30', 'border-orange-400/40',
    'dark:bg-orange-500/15', 'dark:border-orange-400/40', 'text-orange-600', 'dark:text-orange-400',
    'bg-slate-500/10', 'bg-slate-500/15', 'border-slate-400/30', 'border-slate-400/40',
    'dark:bg-slate-500/15', 'dark:border-slate-400/40', 'text-slate-600', 'dark:text-slate-400',
    'bg-yellow-500/10', 'bg-yellow-500/15', 'border-yellow-400/30', 'border-yellow-400/40',
    'dark:bg-yellow-500/15', 'dark:border-yellow-400/40', 'text-yellow-600', 'dark:text-yellow-400',
    'bg-indigo-500/10', 'bg-indigo-500/15', 'border-indigo-400/30', 'border-indigo-400/40',
    'dark:bg-indigo-500/15', 'dark:border-indigo-400/40', 'text-indigo-600', 'dark:text-indigo-400',
    'bg-pink-500/10', 'bg-pink-500/15', 'border-pink-400/30', 'border-pink-400/40',
    'dark:bg-pink-500/15', 'dark:border-pink-400/40', 'text-pink-600', 'dark:text-pink-400',
    'bg-violet-500/10', 'bg-violet-500/15', 'border-violet-400/30', 'border-violet-400/40',
    'dark:bg-violet-500/15', 'dark:border-violet-400/40', 'text-violet-600', 'dark:text-violet-400',
    'bg-teal-500/10', 'bg-teal-500/15', 'border-teal-400/30', 'border-teal-400/40',
    'dark:bg-teal-500/15', 'dark:border-teal-400/40', 'text-teal-600', 'dark:text-teal-400',
    'bg-fuchsia-500/10', 'bg-fuchsia-500/15', 'border-fuchsia-400/30', 'border-fuchsia-400/40',
    'dark:bg-fuchsia-500/15', 'dark:border-fuchsia-400/40', 'text-fuchsia-600', 'dark:text-fuchsia-400',
    'bg-cyan-500/10', 'bg-cyan-500/15', 'border-cyan-400/30', 'border-cyan-400/40',
    'dark:bg-cyan-500/15', 'dark:border-cyan-400/40', 'text-cyan-600', 'dark:text-cyan-400',
    'bg-rose-500/10', 'bg-rose-500/15', 'border-rose-400/30', 'border-rose-400/40',
    'dark:bg-rose-500/15', 'dark:border-rose-400/40', 'text-rose-600', 'dark:text-rose-400',
    'bg-amber-500/10', 'bg-amber-500/15', 'border-amber-400/30', 'border-amber-400/40',
    'dark:bg-amber-500/15', 'dark:border-amber-400/40', 'text-amber-600', 'dark:text-amber-400',
    'bg-sky-500/10', 'bg-sky-500/15', 'border-sky-400/30', 'border-sky-400/40',
    'dark:bg-sky-500/15', 'dark:border-sky-400/40', 'text-sky-600', 'dark:text-sky-400',
    'backdrop-blur-sm',
  ],
  plugins: [],
}